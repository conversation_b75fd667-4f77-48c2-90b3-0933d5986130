# 设备新增和编辑页面优化

## 修改目标
修改设备新增和编辑页面，使其使用后端API返回的封面图和文件大小，而不是前端下载文件和生成缩略图。

## 主要修改文件
- `src/components/Device/CreateDeviceDialog.vue`

## 修改内容

### 1. 上传成功后的处理逻辑
**修改前**：
```javascript
// 前端生成PDF缩略图
if (isPdf(updatedFile)) {
  generatePdfThumbnail(updatedFile).then(thumbnailUrl => {
    // 设置缩略图
  });
}
```

**修改后**：
```javascript
// 使用后端返回的缩略图和文件大小信息
const thumbnailUrl = typeof serverResponseData === 'object' ? serverResponseData.thumbnailUrl : null
const fileSize = typeof serverResponseData === 'object' ? serverResponseData.fileSize : file.size

const updatedFile = {
  // ...其他属性
  size: fileSize,
  thumbnail: thumbnailUrl // 直接使用后端返回的缩略图
}
```

### 2. 编辑设备时的附件处理
**修改前**：
```javascript
// 前端获取文件大小和生成缩略图
if (!fileObject.size) {
  getFileSize(fileObject.url).then(size => {
    // 设置文件大小
  })
}

if (isPdf(fileObject)) {
  generatePdfThumbnail(fileObject).then(thumbnailUrl => {
    // 设置缩略图
  });
}
```

**修改后**：
```javascript
const fileObject = {
  // ...其他属性
  size: att.fileSize || 0, // 使用后端返回的文件大小
  thumbnail: att.thumbnailUrl || null, // 使用后端返回的缩略图
};

// 不再需要前端获取文件大小和生成缩略图，直接使用后端数据
```

### 3. 移除代理URL处理
**修改前**：
```javascript
const displayFileList = computed(() => {
  return deviceUiFileList.value.map(file => ({
    ...file,
    url: isImage(file) && file.url
      ? file.url.replace('https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com', '/cos-proxy')
      : file.url
  }))
})
```

**修改后**：
```javascript
const displayFileList = computed(() => {
  return deviceUiFileList.value.map(file => ({
    ...file,
    // 不再需要代理URL处理，直接使用原始URL
    url: file.url
  }))
})
```

### 4. 模板显示逻辑优化
**修改前**：
```vue
<div v-else-if="isPdf(file) && !file.thumbnail" class="thumbnail-icon loading">
  <el-icon class="is-loading"><Loading /></el-icon>
  <span>{{ t('common.loading', '加载中...') }}</span>
</div>
```

**修改后**：
```vue
<!-- PDF文件：如果有后端返回的缩略图就显示，否则显示默认图标 -->
<el-image v-else-if="isPdf(file) && file.thumbnail" :src="file.thumbnail" fit="cover" class="thumbnail-image">
  <!-- 错误处理 -->
</el-image>
<!-- PDF文件没有缩略图时显示默认图标 -->
<div v-else class="thumbnail-icon">
  <el-icon><Document /></el-icon>
  <span class="thumbnail-ext">{{ getFileType(file.name) }}</span>
</div>
```

### 5. 移除不需要的函数和导入
- 移除 `generatePdfThumbnail` 函数
- 移除 `getFileSize` 函数
- 移除 PDF.js 相关导入
- 移除 Loading 图标导入

## 后端API期望返回格式

上传文件成功后，后端应该返回：
```json
{
  "code": 1000,
  "data": {
    "url": "https://example.com/files/document.pdf",
    "name": "document.pdf",
    "thumbnailUrl": "https://example.com/thumbnails/document_thumb.jpg",
    "fileSize": 2048576
  }
}
```

设备详情中的附件格式：
```json
{
  "attachments": [
    {
      "id": "att_001",
      "fileName": "document.pdf",
      "fileUrl": "https://example.com/files/document.pdf",
      "thumbnailUrl": "https://example.com/thumbnails/document_thumb.jpg",
      "fileSize": 2048576,
      "fileType": "PDF"
    }
  ]
}
```

## 新增时没有封面图的处理方案

1. **上传时**：如果后端无法生成缩略图，返回 `thumbnailUrl: null`
2. **显示时**：前端检查 `file.thumbnail` 是否存在
   - 有缩略图：显示缩略图
   - 无缩略图：显示默认的文档图标和文件类型

这样避免了前端通过代理下载文件生成缩略图的复杂逻辑。

## 优化效果

- ✅ 不再需要前端下载文件获取大小
- ✅ 不再需要前端生成PDF缩略图  
- ✅ 减少网络请求和处理时间
- ✅ 提升用户体验，减少加载时间
- ✅ 减少前端计算资源消耗
- ✅ 移除代理URL处理逻辑
- ✅ 简化代码结构，提高可维护性

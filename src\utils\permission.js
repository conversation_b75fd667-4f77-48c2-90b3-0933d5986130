/**
 * 权限类别常量
 */
export const PermissionCategories = {
  SYSTEM: {
    code: 'SYSTEM',
    name: '系统管理'
  },
  USER: {
    code: 'USER',
    name: '用户管理'
  },
  ROLE: {
    code: 'ROLE',
    name: '角色管理'
  },
  CUSTOMER: {
    code: 'CUSTOMER',
    name: '客户管理'
  },
  ORDER: {
    code: 'ORDER',
    name: '订单管理'
  },
  DEVICE: {
    code: 'DEVICE',
    name: '设备管理'
  },
  REPORT: {
    code: 'REPORT',
    name: '报表管理'
  }
}

/**
 * 权限操作类型常量
 */
export const PermissionActions = {
  VIEW: {
    code: 'VIEW',
    name: '查看'
  },
  CREATE: {
    code: 'CREATE',
    name: '创建'
  },
  EDIT: {
    code: 'EDIT',
    name: '编辑'
  },
  DELETE: {
    code: 'DELETE',
    name: '删除'
  },
  EXPORT: {
    code: 'EXPORT',
    name: '导出'
  },
  IMPORT: {
    code: 'IMPORT',
    name: '导入'
  },
  APPROVE: {
    code: 'APPROVE',
    name: '审批'
  }
}

/**
 * 检查用户是否有指定权限
 * @param {Array} userPermissions 用户权限列表
 * @param {String} permissionCode 权限代码
 * @returns {Boolean} 是否有权限
 */
export function hasPermission(userPermissions, permissionCode) {
  if (!userPermissions || !Array.isArray(userPermissions) || userPermissions.length === 0) {
    return false
  }
  
  return userPermissions.some(permission => 
    permission.code === permissionCode ||
    permission.permissionCode === permissionCode ||
    permission.id === permissionCode
  )
}

/**
 * 检查用户是否有指定类别的权限
 * @param {Array} userPermissions 用户权限列表
 * @param {String} categoryCode 权限类别代码
 * @param {String} actionCode 权限操作代码
 * @returns {Boolean} 是否有权限
 */
export function hasCategoryPermission(userPermissions, categoryCode, actionCode) {
  if (!userPermissions || !Array.isArray(userPermissions) || userPermissions.length === 0) {
    return false
  }
  
  return userPermissions.some(permission => {
    if (!permission.category) return false
    
    return permission.category.code === categoryCode && 
           permission.action === actionCode
  })
}

/**
 * 生成权限代码
 * @param {String} categoryCode 权限类别代码
 * @param {String} actionCode 权限操作代码
 * @returns {String} 权限代码
 */
export function generatePermissionCode(categoryCode, actionCode) {
  return `${categoryCode}_${actionCode}`
}

/**
 * 格式化权限名称
 * @param {Object} permission 权限对象
 * @returns {String} 格式化后的权限名称
 */
export function formatPermissionName(permission) {
  if (!permission) return ''
  
  let categoryName = ''
  let actionName = ''
  
  if (permission.category) {
    categoryName = permission.category.name || ''
  }
  
  if (permission.action) {
    const action = Object.values(PermissionActions).find(a => a.code === permission.action)
    actionName = action ? action.name : ''
  }
  
  if (categoryName && actionName) {
    return `${categoryName}-${actionName}`
  }
  
  return permission.name || ''
} 
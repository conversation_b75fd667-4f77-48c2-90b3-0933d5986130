<script setup>
import { useRoute } from "vue-router";
import { useNav } from "@/layout/hooks/useNav";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isAllEmpty } from "@pureadmin/utils";
// import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStore } from "@/stores/modules/permission";
import { ref, computed, watch, onMounted } from "vue";
import LaySidebarLogo from "./components/SidebarLogo.vue";
import LaySidebarItem from "./components/SidebarItem.vue";
import LaySidebarCollapse from "./components/SidebarCollapse.vue";

const route = useRoute();
const permissionStore = usePermissionStore();
const isShow = ref(false);
const showLogo = ref(
  storageLocal().getItem(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);

const {
  device,
  isCollapse,
  menuSelect,
  toggleSideBar
} = useNav();

const menuData = computed(() => {
  return permissionStore.wholeMenus;
});

const loading = computed(() => {
  return menuData.value.length === 0;
});

const defaultActive = computed(() => {
  return !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path;
});

watch(
  () => [route.path, permissionStore.wholeMenus],
  () => {
    if (route.path.includes("/redirect")) return;
    menuSelect(route.path);
  }
);
</script>

<template>
  <div
    v-loading="loading"
    :class="['sidebar-container', showLogo ? 'has-logo' : 'no-logo']"
    @mouseenter.prevent="isShow = true"
    @mouseleave.prevent="isShow = false"
  >
    <LaySidebarLogo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar
      wrap-class="scrollbar-wrapper"
      :class="[device === 'mobile' ? 'mobile' : 'pc']"
    >
      <el-menu
        unique-opened
        mode="vertical"
        popper-class="pure-scrollbar"
        class="outer-most select-none"
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="defaultActive"
      >
        <LaySidebarItem
          v-for="routes in menuData"
          :key="routes.path"
          :item="routes"
          :base-path="routes.path"
          class="outer-most select-none"
        />
      </el-menu>
    </el-scrollbar>
    <LaySidebarCollapse
      v-if="device !== 'mobile' && (isShow || isCollapse)"
      :is-active="!isCollapse"
      @toggleClick="toggleSideBar"
    />
  </div>
</template>

<style scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
<template>
  <div class="customer-container">
    <!-- 面包屑 -->
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ t('menus.pureHome') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('menus.customer') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('menus.customerList') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item>
          <el-input 
            v-model="queryParams.keyword" 
            :placeholder="t('customer.search.placeholder')"
            clearable
            @keyup.enter="handleQuery"
            @input="handleQuery"
            style="width: 300px" 
          />
        </el-form-item>
        <el-form-item>
          <el-select 
            v-model="queryParams.hasOrder" 
            :placeholder="t('customer.search.orderRelation')"
            clearable 
            @change="handleQuery" 
            style="width: 160px"
          >
            <el-option :label="t('common.all')" :value="null" />
            <el-option :label="t('customer.filter.linked')" :value="true" />
            <el-option :label="t('customer.filter.unlinked')" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select 
            v-model="queryParams.customerStatus" 
            :placeholder="t('customer.search.status')"
            clearable
            @change="handleQuery"
            style="width: 160px"
          >
            <el-option :label="t('common.all')" :value="null" />
            <el-option :label="t('customer.filter.normal')" :value="'NORMAL'" />
            <el-option :label="t('customer.filter.overdue')" :value="'OVERDUE'" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select 
            v-model="queryParams.idType" 
            :placeholder="t('customer.search.idType')"
            clearable 
            @change="handleQuery"
            style="width: 160px"
          >
            <el-option :label="t('common.all')" value="" />
            <el-option
              v-for="item in idTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <span class="range-label">{{ t('customer.filter.overdueRange') }}:</span>
          <div class="inline-range-inputs">
            <el-input-number 
              v-model="queryParams.minOverdueCount" 
              :placeholder="t('customer.filter.minValue')"
              :min="0"
              :max="queryParams.maxOverdueCount || 999"
              :controls="false"
              class="range-input min-input"
              @change="handleQuery"
            />
            <span class="range-separator">-</span>
            <el-input-number 
              v-model="queryParams.maxOverdueCount" 
              :placeholder="t('customer.filter.maxValue')"
              :min="queryParams.minOverdueCount || 0"
              :max="999"
              :controls="false"
              @change="handleQuery"
              class="range-input max-input"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">{{ t('customer.button.search') }}</el-button>
          <el-button @click="resetQuery">{{ t('customer.button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><plus /></el-icon>{{ t('customer.button.add') }}
      </el-button>
      
      <!-- 修改为下拉菜单形式 -->
      <el-dropdown @command="handleExportCommand" trigger="click">
        <el-button type="primary">
          <el-icon><download /></el-icon>{{ t('customer.button.export') }}
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="excel">{{ t('customer.export.excel') }}</el-dropdown-item>
            <el-dropdown-item command="csv">{{ t('customer.export.csv') }}</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <el-button 
        type="primary" 
        @click="handleBatchOperation"
        :disabled="!selectedCustomers.length" 
      >
        {{ t('customer.button.batchDelete') }}
      </el-button>
    </div>

    <!-- 客户列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="customerList"
        style="width: 100%"
        max-height="58vh"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="customerNo" :label="t('customer.table.code')" min-width="140" show-overflow-tooltip>
            <template #default="scope">
              <el-button type="primary" link @click="handleDetail(scope.row)">
                {{ scope.row.customerNo }}
              </el-button>
            </template>
          </el-table-column>
        <el-table-column prop="name" :label="t('customer.table.name')" min-width="100" show-overflow-tooltip />
        <el-table-column :label="t('customer.table.idInfo')" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ getIdTypeName(row.idType) }} {{ row.idNumber }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" :label="t('customer.table.phone')" min-width="120" show-overflow-tooltip />
        <el-table-column prop="email" :label="t('customer.table.email')" min-width="180" show-overflow-tooltip />
        <el-table-column :label="t('customer.table.orderId')" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.orderId || '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="t('customer.table.orderStatus')" width="130">
          <template #default="{ row }">
            <el-tag :type="row.hasOrder ? 'success' : 'info'" size="small">
              {{ row.hasOrder ? t('customer.status.linked') : t('customer.status.unlinked') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="overdueCount" :label="t('customer.table.overdueCount')" width="130">
          <template #default="{ row }">
            <el-tag :type="row.overdueCount > 0 ? 'danger' : 'success'" size="small" v-if="row.hasOrder">
              {{ row.overdueCount }}
            </el-tag>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('common.createTime')" min-width="160" show-overflow-tooltip />
        <el-table-column prop="updateTime" :label="t('customer.table.updateTime')" min-width="160" show-overflow-tooltip />
        <el-table-column :label="t('customer.table.operation')" width="180" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleDetail(row)">{{ t('common.detail') }}</el-button>
            <el-button link type="primary" @click="handleEdit(row)">{{ t('common.edit') }}</el-button>
            <el-button link type="danger" @click="handleDelete(row)">{{ t('common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        {{ t('customer.pagination.total', { total }) }}
      </div>
      <el-pagination
        v-model:current-page="queryParams.current"
        v-model:page-size="queryParams.size"
        :total="total"
        :page-sizes="[20, 50, 100]"
        :layout="`sizes, prev, pager, next, goto, ${t('customer.pagination.pageClassifier')}`"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #goto>
          {{ t('customer.pagination.goto') }}
        </template>
        <template #sizes="{ item }">
          {{ item.value + t('customer.pagination.pagesize') }}
        </template>
      </el-pagination>
    </div>

    <!-- 创建/编辑客户弹窗 -->
    <create-customer-dialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :customer-data="currentCustomer"
      @success="handleSuccess"
      @close="handleDialogClose"
    />

    <!-- 客户详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="t('customer.details.title')"
      width="1000px"
      :close-on-click-modal="false"
      destroy-on-close
      :key="'detail-dialog-' + (currentCustomer?.id || 'new')"
    >
      <div class="customer-detail" v-if="currentCustomer">
        <div class="detail-section">
          <div class="section-title">{{ t('customer.details.basicInfo') }}</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="$t('customer.customerNo')">
              {{ currentCustomer?.customerNo || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.name')">
              {{ currentCustomer?.name || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.idType')">
              {{ getIdTypeName(currentCustomer?.idType) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.idNumber')">
              {{ currentCustomer?.idNumber || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.phone')">
              {{ currentCustomer?.phone || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.email')">
              {{ currentCustomer?.email || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.residenceAddress')" :span="2">
              {{ currentCustomer?.residenceAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('customer.mailingAddress')" :span="2">
              {{ currentCustomer?.mailingAddress || '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <div class="section-title">{{ t('customer.details.orderInfo') }}</div>
          <div class="order-list">
            <el-table
              v-if="currentCustomer?.orders?.length"
              :data="currentCustomer?.orders"
              style="width: 100%"
              max-height="300"
              :border="true"
              size="small"
            >
              <el-table-column :label="t('order.orderNo')" min-width="180">
                <template #default="{ row }">
                  <el-button link type="primary" @click="viewOrderDetail(row.orderId)">
                    {{ row.orderId }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" :label="t('order.orderDate')" min-width="160" />
              <el-table-column :label="t('order.deviceInfo')" min-width="200">
                <template #default="{ row }">
                  <div>{{ row.deviceName }}</div>
                  <div>({{ row.deviceSn }})</div>
                </template>
              </el-table-column>
              <el-table-column prop="rentPeriod" :label="t('order.rentPeriod')" min-width="220" />
              <el-table-column prop="orderStatusName" :label="t('common.status')" width="150">
                <template #default="{ row }">
                  <el-tag :type="getOrderStatusTagType(row.orderStatus)">
                    {{ formatOrderStatus(row.orderStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
             <div v-else class="no-orders-message">
                 {{ t('customer.details.noOrders') }}
             </div>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">{{ t('customer.details.attachments') }}</div>
          <AttachmentList 
          :attachments="customerAttachments"
          :is-loading="isLoading"
        />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">{{ t('common.close') }}</el-button>
          <el-button type="primary" @click="handleEditFromDetail">{{ t('common.edit') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, ArrowDown, UploadFilled, Document } from '@element-plus/icons-vue'
import AttachmentList from '@/components/common/AttachmentList.vue';
import { 
  getCustomerList,
  deleteCustomer,
  batchDeleteCustomers,
  exportCustomerListCSV,
  exportCustomerListExcel,
  getCustomerDetail
} from '@/api/customer'
import { getDictFields } from '@/api/dictionary'
import CreateCustomerDialog from '@/components/customer/CreateCustomerDialog.vue'

const { t, locale } = useI18n()

const isLoading = ref(false)

// 查询参数
const queryParams = reactive({
  current: 1,
  size: 20,
  keyword: '',
  idType: '',
  hasOrder: null,
  customerStatus: null,
  minOverdueCount: null,
  maxOverdueCount: null
})

const loading = ref(false)
const customerList = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const currentCustomer = ref(null)
const detailVisible = ref(false)
const selectedCustomers = ref([])
const customerOrders = ref([])
const customerAttachments = ref([])
const idTypeOptions = ref([]);

// 上传相关
const uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/crm-web/crm-service/api/attachments/upload`
const uploadHeaders = {
  Authorization: `Bearer ${localStorage.getItem('token')}`
}

// 获取客户列表
const getList = async () => {
  try {
    loading.value = true
    const res = await getCustomerList(queryParams)
    if (res?.code === 1000) {
      res.data.records.forEach(item => {
        item.name = item.name.split('_').join('')
      })
      customerList.value = res.data?.records || []
      total.value = res.data?.total || 0
    } else {
      customerList.value = []
      total.value = 0
      throw new Error(res?.message)
    }
  } catch (error) {
    ElMessage.error(error.message)
    customerList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  // 校验逾期次数范围
  if (
    queryParams.minOverdueCount !== null &&
    queryParams.maxOverdueCount !== null &&
    queryParams.minOverdueCount > queryParams.maxOverdueCount
  ) {
    ElMessage.warning(t('customer.filter.error.minMaxOverdue'))
    return // 阻止查询
  }
  queryParams.current = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.keyword = ''
  queryParams.idType = ''
  queryParams.hasOrder = null
  queryParams.customerStatus = null
  queryParams.minOverdueCount = null
  queryParams.maxOverdueCount = null
  handleQuery()
}

// 新增客户
const handleAdd = () => {
  currentCustomer.value = null
  dialogVisible.value = true
}

// 编辑客户
const handleEdit = async (row) => {
  if (!row || !row.id) return;
  try {
    // 显示加载状态，或者在CreateCustomerDialog内部处理加载状态
    loading.value = true; // 假设我们用列表的loading状态
    const res = await getCustomerDetail(row.id);
    loading.value = false;

    if (res?.code === 1000 && res.data) {
      // 将从详情接口获取的完整数据传递给弹窗
      // 确保 res.data.customerInfo 和 res.data.attachments 等结构符合弹窗预期
      currentCustomer.value = {
        ...res.data.customerInfo, // 客户基本信息
        id: row.id, // 确保ID传递过去，因为详情接口的customerInfo中可能不直接包含id，或者用res.data.customerInfo.id
        // 如果CreateCustomerDialog需要原始的 attachments 结构，直接传递
        // attachments: res.data.attachments || [], 
        // 或者如果弹窗希望接收处理过的附件列表 (如 handleDetail 中 customerAttachments.value 的格式)
        // mappedAttachments: res.data.attachments?.map(att => ({ fileName: att.fileName, fileUrl: att.fileUrl })) || []
      };
      // 为了保持与 handleDetail 中附件处理的一致性，这里也准备一个映射过的附件列表给 currentCustomer
      // CreateCustomerDialog 可能需要根据这个列表来初始化它的 el-upload file-list
      if (res.data.attachments) {
        currentCustomer.value.attachmentsToDisplay = res.data.attachments.map(att => ({
          name: att.fileName, // el-upload 通常使用 name 属性显示文件名
          url: att.fileUrl,   // 用于预览或下载
          uid: att.id || Date.now() + Math.random(), // 给一个唯一标识，如果API返回了附件ID最好用那个
          status: 'success', // 标记为已上传成功的文件
          // 包含完整的附件信息
          thumbnailUrl: att.thumbnailUrl,
          fileSize: att.fileSize,
          fileType: att.fileType,
          uploadTime: att.uploadTime,
          raw: {
            id: att.id,
            fileName: att.fileName,
            fileUrl: att.fileUrl,
            thumbnailUrl: att.thumbnailUrl,
            fileSize: att.fileSize,
            fileType: att.fileType,
            uploadTime: att.uploadTime
          }
        }));
      }

      console.log("Editing customer with data:", JSON.parse(JSON.stringify(currentCustomer.value)));
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || t('customer.details.fetchFailed'));
    }
  } catch (error) {
    loading.value = false;
    console.error(t('customer.details.fetchFailed'), error);
    ElMessage.error(error.message || t('customer.details.fetchFailed'));
  }
};

// 删除客户
const handleDelete = async (row) => {
  if (!row?.id) return
  try {
    await ElMessageBox.confirm(
      t('customer.delete.confirm'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    const res = await deleteCustomer(row.id)
    if (res?.code === 1000) {
      ElMessage.success(t('customer.delete.success'))
      await getList()
    } else {
      throw new Error(res?.message || t('customer.delete.error'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败：', error)
      ElMessage.error(error.message || t('customer.delete.error'))
    }
  }
}

// 处理分页大小改变
const handleSizeChange = (val) => {
  queryParams.size = val
  getList()
}

// 处理页码改变
const handleCurrentChange = (val) => {
  queryParams.current = val
  getList()
}

// 处理操作成功
const handleSuccess = () => {
  getList()
}

// 处理客户选择变化
const handleSelectionChange = (val) => {
  selectedCustomers.value = val
}

// 处理导出
const handleExportCommand = async (command) => {
  try {
    loading.value = true
    let res
    let mimeType
    let fileExt
    let fileName
    
    // 使用特定格式的导出API
    if (command === 'csv') {
      res = await exportCustomerListCSV(queryParams)
      mimeType = 'text/csv;charset=utf-8'
      fileExt = 'csv'
    } else {
      res = await exportCustomerListExcel(queryParams)
      mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      fileExt = 'xlsx'
    }
    
    if (!res) throw new Error(t('customer.export.error'))
    
    // 确定文件名
    fileName = `客户列表_${command}_${new Date().getTime()}.${fileExt}`
    
    // 处理响应数据，确保正确创建Blob对象
    let blob
    
    if (res instanceof Blob) {
      // 如果已经是Blob对象
      blob = res
    } else if (res.data && res.data instanceof Blob) {
      // 如果响应中包含data属性且为Blob
      blob = res.data
    } else if (typeof res === 'object') {
      // 如果是普通对象，转换为JSON字符串再创建Blob
      blob = new Blob([JSON.stringify(res, null, 2)], { type: 'application/json' })
      mimeType = 'application/json'
      fileExt = 'json'
      fileName = `客户列表_错误响应_${new Date().getTime()}.${fileExt}`
      console.error('导出接口返回非二进制数据:', res)
      ElMessage.warning('导出格式有误，已将返回数据保存为JSON文件')
    } else if (typeof res === 'string') {
      // 如果是字符串
      blob = new Blob([res], { type: mimeType })
    } else {
      throw new Error('无法处理的返回数据格式')
    }
    
    // 下载文件
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(link.href)
    
    ElMessage.success(t('customer.export.success'))
  } catch (error) {
    console.error('导出失败：', error)
    ElMessage.error(error.message || t('customer.export.error'))
  } finally {
    loading.value = false
  }
}

// 处理批量操作
const handleBatchOperation = async () => {
  if (!selectedCustomers.value?.length) {
    return ElMessage.warning(t('customer.batch.noSelection'))
  }

  try {
    await ElMessageBox.confirm(
      t('customer.batch.deleteConfirm', { count: selectedCustomers.value.length }), 
      t('common.warning'),
      { type: 'warning', confirmButtonText: t('common.confirm'), cancelButtonText: t('common.cancel') }
    )
    
    const ids = selectedCustomers.value.map(item => item.id).filter(Boolean)
    if (!ids.length) return
    
    const res = await batchDeleteCustomers(ids)
    if (res?.code === 1000) {
      ElMessage.success(t('customer.batch.deleteSuccess'))
      await getList()
    } else {
      throw new Error(res?.message || t('customer.batch.deleteError'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败：', error)
      ElMessage.error(error.message || t('customer.batch.deleteError'))
    }
  }
}

// 处理客户详情
const handleDetail = async (row) => {
  try {
    loading.value = true; // 开始加载状态
    const res = await getCustomerDetail(row.id);
    if (res?.code === 1000 && res.data) {
      currentCustomer.value = {
        ...row, // 从列表行数据开始
        ...res.data.customerInfo, // 合并基础信息
        orders: res.data.orders || [], // 合并订单信息
        attachments: res.data.attachments || [] // API原始附件数据
      };
      // 确保 customerAttachments 包含所有必要字段
      customerAttachments.value = res.data.attachments?.map(att => ({
        id: att.id,
        fileName: att.fileName,
        fileUrl: att.fileUrl,
        thumbnailUrl: att.thumbnailUrl,
        fileSize: att.fileSize,
        fileType: att.fileType,
        uploadTime: att.uploadTime
      })) || [];
      console.log('Processed attachments for detail view:', JSON.parse(JSON.stringify(customerAttachments.value))); // 调试日志
      detailVisible.value = true;
    } else {
      throw new Error(res?.message || t('customer.details.fetchFailed')); // 使用i18n
    }
  } catch (error) {
    console.error(t('customer.details.fetchFailed'), error); // 使用i18n
    ElMessage.error(error.message || t('customer.details.fetchFailed'));
    currentCustomer.value = row; // 出错时，至少显示列表中的基本信息
    customerOrders.value = []; // 保持一致性，虽然模板中可能未使用
    customerAttachments.value = [];
  } finally {
    loading.value = false; // 结束加载状态
  }
};

// 从详情页面编辑
const handleEditFromDetail = () => {
  if (!currentCustomer.value) return
  detailVisible.value = false
  nextTick(() => {
    dialogVisible.value = true
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
  currentCustomer.value = null
}

// 上传相关
const handleUploadSuccess = (response, file) => {
  if (response.code === 1000) {
    ElMessage.success('上传成功')
    // 刷新客户详情
    getCustomerDetail(currentCustomer.value.id)
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleUploadError = (error) => {
  console.error('上传失败：', error)
  ElMessage.error('上传失败')
}

const beforeUpload = (file) => {
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  return true
}

const downloadFile = async (attachment) => {
  if (attachment.url) {
    try {
      const response = await fetch(attachment.url);
      if (!response.ok) throw new Error('Network response was not ok.');
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = attachment.name || 'downloaded_file'; 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('下载文件失败:', error);
      ElMessage.error('下载文件失败，请检查链接或网络连接。');
      // 也可以提供一个直接打开链接的选项
      // window.open(attachment.url, '_blank');
    }
  } else {
    ElMessage.warning('附件链接无效。');
  }
};

// 查看订单详情
const viewOrderDetail = (orderId) => {
  // 这里需要实现跳转到订单详情页的逻辑
  // 例如，使用 Vue Router 跳转
  console.log('查看订单详情:', orderId);
  // router.push({ name: 'OrderDetail', params: { id: orderId } });
  ElMessage.info(`跳转到订单 ${orderId} 的详情页面 (待实现)`);
};

// 获取订单状态对应的标签类型
const getOrderStatusTagType = (status) => {
  switch (status) {
    case 'NORMAL':
      return 'success'; // 使用中
    case 'OVERDUE':
      return 'danger'; // 逾期
    case 'BAD_DEBT':
      return 'warning'; // 坏账
    // 如果有 COMPLETED 状态，可以添加 'info' 或其他
    // case 'COMPLETED':
    //   return 'info';
    default:
      return 'primary'; // 其他状态
  }
};

// 监听语言变化，确保在语言切换时重新渲染弹窗
watch(locale, (newLocale) => {
  console.log('语言已切换:', newLocale);
  if (detailVisible.value && currentCustomer.value) {
    // 如果弹窗已经打开，强制重新打开以更新内容
    const tempCustomer = { ...currentCustomer.value };
    detailVisible.value = false;
    nextTick(() => {
      currentCustomer.value = tempCustomer;
      detailVisible.value = true;
    });
  }
});

// 获取证件类型名称
const getIdTypeName = (idType) => {
  if (!idType) return '--';
  const option = idTypeOptions.value.find(opt => opt.value === idType);
  return option ? option.label : idType;
}

// 格式化订单状态
const formatOrderStatus = (status) => {
  if (!status) return t('common.unknown');
  const statusMap = {
    'ACTIVE': t('order.status.ACTIVE'),
    'COMPLETED': t('order.status.COMPLETED'),
    'OVERDUE': t('order.status.OVERDUE'),
    'CANCELLED': t('order.status.CANCELLED'),
    'UNKNOWN': t('order.status.UNKNOWN'),
    'NORMAL': t('order.status.NORMAL'),
    'NOT_STARTED': t('order.status.NOT_STARTED'),
    'DEFAULTED': t('order.status.DEFAULTED'),
    'BAD_DEBT': t('order.status.BAD_DEBT')
  };
  return statusMap[status] || status;
};

// 加载字段选项的函数
const loadFieldOptions = async () => {
  console.log('开始加载客户页面字段选项...')
  try {
    const response = await getDictFields({ module: 'CUSTOMER', fieldCode: 'id_type' });
    if (response.data && response.data.records && response.data.records.length > 0) {
      idTypeOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch id_type options:", error);
  }
}

// 初始化
onMounted(async () => {
  getList()
  await loadFieldOptions()
})

// 页面激活时重新加载字段选项
onActivated(async () => {
  console.log('客户列表页面激活，重新加载字段选项...')
  await loadFieldOptions()
})
</script>

<style lang="scss" scoped>
.customer-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .breadcrumb {
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .search-bar {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      
      .el-form-item {
        margin-bottom: 0;
      }
      
      .filter-button {
        display: flex;
        align-items: center;
        &.is-active {
          color: #409eff;
          border-color: #409eff;
        }
      }
      
      .range-label {
        margin-right: 8px;
        color: #606266;
        font-size: 14px;
        line-height: 32px;
        white-space: nowrap;
        min-width: 85px;
        display: inline-block;
      }
      
      .inline-range-inputs {
        display: inline-flex;
        align-items: center;
        
        .range-input {
          width: 150px;
          margin: 0;
          
          :deep(.el-input) {
            width: 100%;
          }
          
          :deep(.el-input__wrapper) {
            width: 100%;
            box-sizing: border-box;
            padding-top: 1px;
            padding-bottom: 1px;
            padding-left: 6px;
            padding-right: 6px;
          }
          
          :deep(.el-input__inner) {
            width: 100%;
            box-sizing: border-box;
            text-align: center;
            font-size: 14px;
            padding: 0;
          }
        }
        
        .range-separator {
          padding: 0 10px;
          margin: 0;
          color: #909399;
          font-weight: bold;
          line-height: normal;
          flex-shrink: 0;
        }
      }
    }
  }

  .range-filter {
    .range-header {
      font-weight: 500;
      font-size: 14px;
      color: #303133;
      margin-bottom: 16px;
    }
    
    .range-content {
      margin-bottom: 16px;
      
      .range-inputs {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .range-input {
          width: 125px;
          
          :deep(.el-input__inner) {
            text-align: center;
            font-size: 14px;
          }
        }
        
        .range-divider {
          font-size: 16px;
          color: #909399;
          margin: 0 12px;
          flex-shrink: 0;
        }
      }
    }
    
    .range-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      border-top: 1px solid #EBEEF5;
      padding-top: 16px;
      margin-top: 8px;
    }
  }

  .toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 10px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .el-table {
      border-radius: 4px;
      overflow: hidden;

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }

      td {
        padding: 8px 0;
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .pagination-info {
      color: #606266;
      font-size: 14px;
    }

    .pagination-content {
      display: flex;
      align-items: center;
    }
  }
}

.customer-detail {
  padding: 0 20px;
  max-height: 600px;
  overflow-y: auto;

  .detail-section {
    margin-bottom: 24px;
    background: #fff;
    border-radius: 4px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .upload-button {
        margin-left: auto;
      }
    }

    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #ebeef5;

        .el-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #909399;
        }

        .file-name {
          flex: 1;
          margin-right: 16px;
          color: #606266;
        }
      }
    }

    .no-attachments {
      color: #909399;
      text-align: center;
      padding: 16px 0;
    }

    .order-list {
      margin-top: 16px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

.no-orders-message, .no-attachments {
  color: #909399;
  text-align: center;
  padding: 16px;
  background-color: #f7f8fa;
  border-radius: 4px;
  margin-top: 8px;
  font-size: 14px;
}

// 全局样式
:deep(.overdue-range-popover) {
  padding: 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
  
  .el-popper__arrow {
    display: none !important;
  }
}
</style> 
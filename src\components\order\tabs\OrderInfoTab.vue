<template>
  <div class="order-info-tab">
    <div v-if="props.isLoading" class="loading-container">
      <el-progress 
        type="circle" 
        :width="24" 
        :stroke-width="3" 
        :percentage="100" 
        status="success" 
        :indeterminate="true"
        :duration="1"
        class="custom-loader"
      />
      <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
    </div>

    <div v-else-if="props.loadingError" class="error-container">
      <span>{{ $t('common.fetchFailed') }}</span>
      <el-button type="danger" :icon="Refresh" @click="handleRetryFetch" plain size="small" class="retry-button">
        {{ $t('common.retry') }}
      </el-button>
    </div>

    <div v-else-if="orderInfo" class="order-info-content">
      <!-- 订单基本信息 -->
      <div class="info-section">
        <!-- <div class="section-header">{{ $t('order.basicInfo') }}</div> -->
        
        <el-descriptions :column="3" border>
          <!-- Row 1 -->
          <el-descriptions-item :label="$t('order.orderNo')">
            {{ orderInfo.orderNo || orderInfo.orderId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.orderType')">
            <el-tag :type="orderInfo.orderType === 'INSTALLMENT' ? 'success' : 'info'" size="small">
              {{ formatOrderType(orderInfo.orderType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.name')">
            {{ (orderInfo.customerFirstName + orderInfo.customerLastName).replace('_', '') }}
          </el-descriptions-item>

          <!-- Row 2 -->
          <el-descriptions-item :label="$t('order.startDate')">
            {{ formatDate(orderInfo.orderDate) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.numberOfInstallments')">
            {{ orderInfo.totalInstallments || orderInfo.numberOfInstallments || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.totalAmount')">
            <span class="important-amount">¥{{ formatAmount(orderInfo.totalAmount || orderInfo.contractAmount) }}</span>
          </el-descriptions-item>

          <!-- Row 3 -->
          <el-descriptions-item :label="$t('order.penaltyFine')">
            <template v-if="orderInfo.penaltyValue">
              {{ formatAmount(orderInfo.penaltyValue) }}
              {{ orderInfo.penaltyAmountType === 'PERCENTAGE_AMOUNT' ? '%' : $t('order.yuan') }}
              <template v-if="orderInfo.penaltyCalculationType === 'DAILY'">/{{ $t('order.day') }}</template>
              <template v-else-if="orderInfo.penaltyCalculationType === 'WEEKLY'">/{{ $t('order.week') }}</template>
              <template v-else-if="orderInfo.penaltyCalculationType === 'MONTHLY'">/{{ $t('order.month') }}</template>
            </template>
            <template v-else>
              -
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.initialPayment')">
            ¥{{ formatAmount(orderInfo.initialPayment || orderInfo.firstPaymentAmount || 0) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.periodicPayment')">
            ¥{{ formatAmount(orderInfo.periodicPayment || orderInfo.installmentAmount || 0) }}
          </el-descriptions-item>

          <!-- Row 4 -->
          <el-descriptions-item :label="$t('order.periodicLength')">
            {{ orderInfo.periodicLength || (typeof orderInfo.periodicLengthDay === 'number' ? orderInfo.periodicLengthDay : '-') }} 
            {{ orderInfo.periodicLengthDay ? $t('order.day') : formatPeriodicUnit(orderInfo.periodicUnit || orderInfo.periodicLengthUnit) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.penaltyCalculationMethod')">
            {{ formatPenaltyCalculationType(orderInfo.penaltyCalculationType) || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.deposit')">
            ¥{{ formatAmount(orderInfo.deposit || orderInfo.depositAmount || 0) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 备注信息 -->
      <div class="info-section" v-if="orderInfo.remarks">
        <div class="section-header">{{ $t('order.remarks') }}</div>
        
        <div class="remarks-container">
          <div class="remarks-content" :class="{ 'collapsed': !remarksExpanded && orderInfo.remarks.length > 200 }">
            {{ orderInfo.remarks }}
          </div>
          
          <el-button 
            v-if="orderInfo.remarks.length > 200" 
            type="primary" 
            link 
            @click="remarksExpanded = !remarksExpanded"
          >
            {{ remarksExpanded ? $t('common.collapse') : $t('common.expand') }}
          </el-button>
        </div>
      </div>


      
      <!-- 订单附件 -->
      <div class="info-section">
        <div class="section-header">{{ $t('order.attachments') }}</div>


        <AttachmentList 
          :attachments="orderInfo.attachments"
          :is-loading="isLoading"
        />
        
        <!-- <div class="attachments-container" v-if="orderInfo.attachments && orderInfo.attachments.length > 0">
          <el-table :data="orderInfo.attachments" style="width: 100%" border size="large">
            <el-table-column :label="$t('order.attachment.fileName')" min-width="200">
              <template #default="{ row }">
                <div class="file-info">
                  <el-icon v-if="isImageFile(row.name, row.type)"><Picture /></el-icon>
                  <el-icon v-else-if="isPdfFile(row.name, row.type)"><Document /></el-icon>
                  <el-icon v-else><Files /></el-icon>
                  <span class="file-name">{{ row.fileName }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column :label="$t('order.attachment.operations')" width="200" align="center">
              <template #default="{ row }">
                <el-button 
                  v-if="isImageFile(row.fileName)"
                  type="primary" 
                  link
                  @click="handlePreview(row)"
                >
                  {{ $t('order.attachment.preview') }}
                </el-button>
                <el-button 
                  v-else-if="isPdfFile(row.fileName)"
                  type="primary" 
                  link
                  @click="handlePreview(row)"
                >
                  {{ $t('order.attachment.preview') }}
                </el-button>
                <el-button 
                  type="primary" 
                  link
                  @click="handleDownload(row)"
                >
                  {{ $t('order.attachment.download') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div> -->
        
        <!-- <div v-else class="empty-attachments">
          {{ $t('order.noAttachments') }}
        </div> -->
      </div>
    </div>
    
    <div v-else class="empty-data">
      <el-empty :description="$t('common.noData')" />
    </div>
    
    <!-- 图片预览弹窗 -->
    <el-dialog v-model="previewVisible" :title="currentAttachment?.fileName || ''" class="preview-dialog" width="70%">
      <div class="image-preview-container">
        <img v-if="currentAttachment" :src="currentAttachment.url" alt="Preview" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Refresh, Picture, Document, Files } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { downloadOssFile } from '@/api/upload';
import AttachmentList from '@/components/common/AttachmentList.vue';

const { t } = useI18n();

const props = defineProps({
  orderId: {
    type: String,
    required: true
  },
  orderInfo: {
    type: Object,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);

// 备注展开控制
const remarksExpanded = ref(false);

// 附件预览控制
const previewVisible = ref(false);
const currentAttachment = ref(null);

// 重试获取数据
const handleRetryFetch = () => {
  emit('retry-fetch');
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  try {
    // 防止日期字符串格式不正确
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // 如果无法解析，直接返回原始字符串
    
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit' 
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString; // 发生错误时返回原始字符串
  }
};

// 格式化日期和时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  // Assuming dateString might be a full ISO string, or already formatted.
  // For consistency, try to parse and reformat, but fallback if it's not a parsable date.
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
      // If it's not a valid date, and doesn't look like a placeholder already
      if (dateString !== '-' && !/^\d{4}-\d{2}-\d{2}/.test(dateString)) {
        // console.warn('Invalid date string for formatDateTime:', dateString);
      }
      return dateString; // Return original if not parsable, or it's already a placeholder
  }
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化金额
const formatAmount = (amount) => {
  if (amount === undefined || amount === null) return '0.00';
  return Number(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 格式化订单类型
const formatOrderType = (type) => {
  if (!type) return '-';
  
  
  // 转换小写以便统一匹配
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  // 如果是中文直接显示
  if (type === '租赁' || type === '分期') {
    return type;
  }
  
  const typeMap = {
    'installment': t('order.types.installment'),
    'rental': t('order.types.rental'),
    'INSTALLMENT': t('order.types.installment'),
    'RENTAL': t('order.types.rental')
  };
  
  return typeMap[type] || type;
};

// New function for penalty calculation type
const formatPenaltyCalculationType = (type) => {
  if (!type) return '-';
  const typeMap = {
    'DAILY': t('order.penaltyCalculationMethods.daily'),
    'WEEKLY': t('order.penaltyCalculationMethods.weekly'),
    'MONTHLY': t('order.penaltyCalculationMethods.monthly'),
    'daily': t('order.penaltyCalculationMethods.daily'),
    'weekly': t('order.penaltyCalculationMethods.weekly'),
    'monthly': t('order.penaltyCalculationMethods.monthly'),
    // Add direct Chinese mappings if they can come from API
    '按日计息': '按日计息',
    '按周计息': '按周计息',
    '按月计息': '按月计息',
  };
  return typeMap[type] || type;
};

// 格式化周期单位
const formatPeriodicUnit = (unit) => {
  if (!unit) return '';
  
  // 如果是中文单位，直接返回
  if (unit === '天' || unit === '月' || unit === '年') {
    return unit;
  }
  
  // 常见API返回的类型映射
  const unitMap = {
    'DAY': t('order.day'),
    'WEEK': t('order.week'),
    'MONTH': t('order.month'),
    'YEAR': t('order.year'),
    'day': t('order.day'),
    'week': t('order.week'),
    'month': t('order.month'),
    'year': t('order.year'),
    // 有些API可能返回数字编码
    '1': t('order.day'),
    '2': t('order.month'),
    '3': t('order.year')
  };
  
  return unitMap[unit] || unit;
};

// 判断是否为图片文件
const isImageFile = (fileName, apiType) => {
  if (apiType && typeof apiType === 'string' && apiType.toLowerCase() === 'image') {
    return true;
  }
  if (!fileName) return false;
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = fileName.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
};

// 判断是否为PDF文件
const isPdfFile = (fileName, apiType) => {
  if (apiType && typeof apiType === 'string' && apiType.toLowerCase() === 'pdf') {
    return true;
  }
  if (!fileName) return false;
  const pdfExtensions = ['pdf'];
  const extension = fileName.split('.').pop().toLowerCase();
  return pdfExtensions.includes(extension);
};

// 判断是否可预览
const isPreviewable = (fileName, apiType) => {
  // If API explicitly says image or pdf, it's previewable
  if (apiType && typeof apiType === 'string') {
    const lowerApiType = apiType.toLowerCase();
    if (lowerApiType === 'image' || lowerApiType === 'pdf') {
      return true;
    }
    // If apiType is something else but known not to be previewable, return false
    // Example: if (lowerApiType === 'zip') return false;
  }
  // Fallback to filename based check if apiType is not conclusive or not present
  if (!fileName) return false;
  // Use the updated isImageFile and isPdfFile which now only check extension if apiType wasn't 'image' or 'pdf'
  return isImageFile(fileName, apiType) || isPdfFile(fileName, apiType); 
};

// 获取文件类型
const getFileType = (fileName, apiType) => {
  if (apiType && typeof apiType === 'string') {
    const lowerApiType = apiType.toLowerCase();
    if (lowerApiType === 'image') return 'Image';
    if (lowerApiType === 'pdf') return 'PDF';
    // For other known API types, you can add more specific labels
    // Otherwise, capitalize the apiType or return it as is
    // Ensure fileName is not null or undefined before trying to use its methods
    const nameForExtension = fileName || "";
    if (isImageFile(nameForExtension, apiType)) return 'Image'; 
    if (isPdfFile(nameForExtension, apiType)) return 'PDF';  
    return apiType.charAt(0).toUpperCase() + apiType.slice(1);
  }

  // Fallback to extension parsing if apiType is not available/useful
  if (!fileName) return '-';
  // Ensure isImageFile and isPdfFile are called with only fileName if apiType is not relevant for this path
  if (isImageFile(fileName)) return 'Image'; 
  if (isPdfFile(fileName)) return 'PDF';   
  
  const ext = fileName.split('.').pop();
  return ext ? ext.toUpperCase() : '-';
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB'];
  let i = 0;
  let formattedSize = size;
  
  while (formattedSize >= 1024 && i < units.length - 1) {
    formattedSize /= 1024;
    i++;
  }
  
  return `${formattedSize.toFixed(2)} ${units[i]}`;
};

// 预览附件
const handlePreview = (attachment) => {
  if (!attachment || !attachment.fileUrl) {
    ElMessage.warning(t('order.attachment.invalidLink'));
    return;
  }

  if (isImageFile(attachment.fileName)) {
    currentAttachment.value = {
      fileName: attachment.fileName,
      url: attachment.fileUrl
    };
    previewVisible.value = true;
  } else if (isPdfFile(attachment.fileName)) {
    window.open(attachment.fileUrl, '_blank');
  } else {
    ElMessage.info(t('order.attachment.noPreviewAvailable'));
  }
};

// 下载附件
const handleDownload = async (attachment) => {
  if (attachment.fileUrl) {
    try {
      const { blob, filename } = await downloadOssFile(attachment.fileUrl);

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      ElMessage.success(t('common.download') + t('common.success'));
    } catch (error) {
      console.error('文件下载失败:', error);
      ElMessage.error(t('common.download') + t('common.failed') + ': ' + (error.message || t('common.unknown')));
    }
  } else {
    ElMessage.warning(t('order.attachment.downloadUrlMissing'));
  }
};
</script>

<style lang="scss" scoped>
.order-info-tab {
  width: 100%;
  padding: 24px;
  background-color: #f9fafc;
  
  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .error-container {
    flex-direction: column;
    
    .retry-button {
      margin-top: 16px;
    }
  }
  
  .empty-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .info-section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    margin-bottom: 20px;
    overflow: hidden;
    
    .section-header {
      padding: 14px 16px;
      font-size: 15px;
      font-weight: 600;
      color: #303133;
      background-color: #f7f9fc;
      border-bottom: 1px solid #ebeef5;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* El-Descriptions 样式调整 */
  :deep(.el-descriptions) {
    border: 1px solid #ebeef5;
    border-right: none;
    border-bottom: none;
  }

  :deep(.el-descriptions__header) {
    margin-bottom: 0;
  }

  :deep(.el-descriptions__title) {
    font-size: 16px;
    font-weight: normal;
    color: #303133;
  }

  :deep(.el-descriptions__body) {
    background-color: #fff;
  }

  :deep(.el-descriptions-item) {
    border-left: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    padding: 12px 16px;
  }

  :deep(.el-descriptions-item__label) {
    background-color: #fcfcfc;
    color: #606266;
    font-weight: 500;
    padding-right: 10px;
    width: 200px !important; /* Changed to width for stronger alignment */
    text-align: left !important; /* Ensure left alignment */
    vertical-align: middle;
  }

  :deep(.el-descriptions-item__content) {
    color: #303133;
    word-break: break-word;
    text-align: left !important; /* Ensure content is also left-aligned */
  }
  
  /* Fix for custom styles not applying due to Element Plus scoped issues */
  :deep(.el-descriptions-item__container) {
    display: flex;
    align-items: center;
  }

  :deep(.el-descriptions-item__label.is-bordered-label) {
    vertical-align: top;
  }

  .attachments-container {
    padding: 16px;
  }

  .file-info {
    display: flex;
    align-items: center;
    .el-icon {
      margin-right: 8px;
      font-size: 16px;
      color: #909399;
    }
    .file-name {
      color: #606266;
      font-size: 14px;
    }
  }

  .empty-attachments {
    color: #909399;
    text-align: center;
    padding: 20px;
    border: 1px dashed #ebeef5;
    border-radius: 4px;
    background-color: #fcfcfc;
  }

  .remarks-container {
    padding: 16px;
    background-color: #fcfcfc;
    border-top: 1px solid #ebeef5;

    .remarks-content {
      white-space: pre-wrap; /* Preserve whitespace and line breaks */
      word-wrap: break-word; /* Break long words */
      max-height: 100px; /* Limit height */
      overflow: hidden; /* Hide overflow */
      transition: max-height 0.3s ease-in-out;
      line-height: 1.6;
      color: #303133;

      &.collapsed {
        max-height: 48px; /* Show only 3 lines when collapsed (approx. 16px * 3) */
      }
    }

    .el-button {
      margin-top: 10px;
    }
  }
}

:deep(.preview-dialog) {
  .el-dialog__header {
    background-color: #f7f9fc;
    margin-right: 0;
    padding: 14px 16px;
  }
  
  .el-dialog__body {
    padding: 0;
  }
}
</style> 
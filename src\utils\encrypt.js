import JSEncrypt from 'jsencrypt/bin/jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair
const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANL378k3RiZHWx5AfJqdH9xRNBmD9wGD\n' +
  '2iRe41HdTNF8RUhNnHit5NpMNtGL0NPTSSpPjjI1kJfVorRvaQerUgkCAwEAAQ=='

/**
 * RSA加密
 * @param {string} txt 需要加密的文本
 * @returns {string} 加密后的文本
 */
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对需要加密的数据进行加密
}

/**
 * 批量加密对象中的指定字段
 * @param {Object} obj 需要加密的对象
 * @param {Array<string>} fields 需要加密的字段数组
 * @returns {Object} 加密后的对象
 */
export function encryptFields(obj, fields) {
  const result = { ...obj }
  fields.forEach(field => {
    if (obj[field]) {
      result[field] = encrypt(obj[field])
    }
  })
  return result
} 
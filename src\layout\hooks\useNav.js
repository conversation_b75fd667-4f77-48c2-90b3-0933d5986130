import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/stores/modules/app";
import { useUserStore } from "@/stores/modules/user";
import { useMultiTagsStore } from "@/stores/modules/multiTags";

export function useNav() {
  const route = useRoute();
  const router = useRouter();
  const appStore = useAppStore();
  const userStore = useUserStore();
  const tagsStore = useMultiTagsStore();

  const device = computed(() => {
    return appStore.device;
  });

  const isCollapse = computed(() => {
    return !appStore.sidebar.opened;
  });

  // 用户信息
  const username = computed(() => {
    return userStore?.username;
  });

  const avatarsStyle = computed(() => {
    return username.value ? { marginRight: "10px" } : "";
  });

  const userAvatar = computed(() => {
    return userStore?.avatar;
  });

  // 切换侧边栏
  function toggleSideBar() {
    appStore.toggleSideBar();
  }

  // 退出登录
  function logout() {
    userStore.logout();
    router.push("/login");
  }

  // 面板展开
  function onPanel() {
    appStore.togglePanel();
  }

  // 菜单选择 (keep this for potential use with vertical menu)
  function menuSelect(index) {
    if (isCollapse.value) {
      return;
    } 
    // 可以在这里添加导航逻辑
  }

  return {
    route,
    device,
    isCollapse,
    username,
    userAvatar,
    avatarsStyle,
    toggleSideBar,
    logout,
    onPanel,
    menuSelect
  };
}
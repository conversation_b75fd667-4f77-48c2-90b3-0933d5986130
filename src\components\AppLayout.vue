<template>
    <div class="app-layout">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4">
          <div class="flex items-center justify-between h-16">
            <!-- Logo and brand -->
            <div class="flex items-center">
              <router-link to="/" class="flex items-center">
                <img src="@/assets/logo.svg" alt="CRM Logo" class="h-8 w-8 mr-2" />
                <span class="text-xl font-semibold text-primary">CRM Web</span>
              </router-link>
            </div>
            
            <!-- Main navigation -->
            <nav class="hidden md:flex space-x-6">
              <router-link 
                v-for="item in navigationItems" 
                :key="item.path" 
                :to="item.path"
                class="nav-link"
                active-class="nav-link-active"
              >
                {{ item.name }}
              </router-link>
            </nav>
            
            <!-- User menu -->
            <div class="flex items-center">
              <!-- Notifications -->
              <el-dropdown trigger="click" class="mr-4">
                <el-badge :value="3" class="cursor-pointer">
                  <i class="el-icon-bell text-xl"></i>
                </el-badge>
                <template #dropdown>
                  <el-dropdown-menu>
                    <div class="px-4 py-2 border-b">
                      <h3 class="text-sm font-semibold">Notifications</h3>
                    </div>
                    <el-dropdown-item v-for="(n, index) in notifications" :key="index">
                      <div class="flex items-start py-1">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-2 flex-shrink-0">
                          <i :class="n.icon" class="text-blue-500"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium">{{ n.title }}</p>
                          <p class="text-xs text-gray-500">{{ n.time }}</p>
                        </div>
                      </div>
                    </el-dropdown-item>
                    <div class="px-4 py-2 border-t text-center">
                      <el-button type="link" size="small">View All Notifications</el-button>
                    </div>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              
              <!-- User dropdown -->
              <el-dropdown trigger="click">
                <div class="flex items-center cursor-pointer">
                  <el-avatar size="small" src="https://randomuser.me/api/portraits/men/1.jpg" class="mr-2"></el-avatar>
                  <span class="text-sm font-medium hidden sm:block">{{ userFullName }}</span>
                  <i class="el-icon-arrow-down ml-1 text-xs"></i>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="$router.push('/profile')">
                      <i class="el-icon-user mr-2"></i> My Profile
                    </el-dropdown-item>
                    <el-dropdown-item @click="$router.push('/settings')">
                      <i class="el-icon-setting mr-2"></i> Settings
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleLogout">
                      <i class="el-icon-switch-button mr-2"></i> Logout
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              
              <!-- Mobile menu button -->
              <button class="ml-4 md:hidden" @click="mobileMenuOpen = !mobileMenuOpen">
                <i :class="mobileMenuOpen ? 'el-icon-close' : 'el-icon-menu'" class="text-xl"></i>
              </button>
            </div>
          </div>
        </div>
      </header>
      
      <!-- Mobile menu -->
      <div v-if="mobileMenuOpen" class="md:hidden bg-white shadow-md">
        <div class="px-4 py-2 space-y-1">
          <router-link 
            v-for="item in navigationItems" 
            :key="item.path" 
            :to="item.path"
            class="block px-3 py-2 rounded-md text-base font-medium"
            active-class="bg-primary-light text-white"
            @click="mobileMenuOpen = false"
          >
            {{ item.name }}
          </router-link>
        </div>
      </div>
      
      <!-- Main content -->
      <main>
        <slot></slot>
      </main>
      
      <!-- Footer -->
      <footer class="bg-white border-t mt-auto">
        <div class="container mx-auto px-4 py-6">
          <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-4 md:mb-0">
              <p class="text-sm text-gray-500">
                &copy; {{ new Date().getFullYear() }} CRM Web. All rights reserved.
              </p>
            </div>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-500 hover:text-primary">
                <i class="el-icon-document"></i> Documentation
              </a>
              <a href="#" class="text-gray-500 hover:text-primary">
                <i class="el-icon-question"></i> Support
              </a>
              <a href="#" class="text-gray-500 hover:text-primary">
                <i class="el-icon-postcard"></i> Terms
              </a>
              <a href="#" class="text-gray-500 hover:text-primary">
                <i class="el-icon-lock"></i> Privacy
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/stores/modules/user';
  
  // Router
  const router = useRouter();
  
  // User store
  const userStore = useUserStore();
  
  // Computed properties
  const userFullName = computed(() => userStore.username || 'User');
  
  // State
  const mobileMenuOpen = ref(false);
  
  // Navigation items
  const navigationItems = [
    { name: 'Home', path: '/' },
    { name: 'Dashboard', path: '/dashboard' },
    { name: 'About', path: '/about' },
    // Add more navigation items as needed
  ];
  
  // Sample notifications data
  const notifications = [
    {
      title: 'New lead assigned to you',
      time: '10 minutes ago',
      icon: 'el-icon-user'
    },
    {
      title: 'Meeting reminder: Client call at 2 PM',
      time: '1 hour ago',
      icon: 'el-icon-alarm-clock'
    },
    {
      title: 'New comment on your task',
      time: 'Yesterday',
      icon: 'el-icon-chat-dot-round'
    }
  ];
  
  // Methods
  const handleLogout = () => {
    userStore.logout();
    router.push('/login');
  };
  </script>
  
  <style scoped>
  .app-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  
  .nav-link {
    display: inline-flex;
    align-items: center;
    height: 100%;
    padding: 0 0.5rem;
    color: #4b5563;
    font-weight: 500;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
  }
  
  .nav-link:hover {
    color: var(--primary-color);
  }
  
  .nav-link-active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
  }
  </style>
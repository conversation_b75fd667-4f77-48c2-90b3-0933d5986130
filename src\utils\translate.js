/**
 * 本地翻译映射表（备用方案）
 */
const localTranslationMap = {
  // 设备相关
  '设备': 'Device',
  '设备在途': 'Device in Transit',
  '设备状态': 'Device Status',
  '设备类型': 'Device Type',
  
  // 收款相关
  '暂缓收款': 'Payment Suspended',
  '收款': 'Payment',
  '收款状态': 'Payment Status',
  
  // 审核相关
  '审核中': 'Under Review',
  '审核': 'Review',
  '审核状态': 'Review Status',
  
  // 客户相关
  '客户协商中': 'Customer Negotiating',
  '客户': 'Customer',
  '黑名单客户': 'Blacklisted Customer',
  
  // 风险相关
  '风险预警': 'Risk Warning',
  '风险': 'Risk',
  '风险状态': 'Risk Status',
  
  // 法务相关
  '法务处理中': 'Legal Processing',
  '法务': 'Legal',
  '法务状态': 'Legal Status',
  
  // 资产相关
  '已报损': 'Asset Reported Lost',
  '资产': 'Asset',
  '资产状态': 'Asset Status',
  
  // 通用状态
  '启用': 'Enabled',
  '禁用': 'Disabled',
  '是': 'Yes',
  '否': 'No',
  '其他': 'Other'
}

/**
 * 使用免费的翻译API翻译文本
 * @param {string} text - 要翻译的文本
 * @param {string} targetLang - 目标语言代码 (zh-CN, en-US)
 * @returns {Promise<string>} 翻译后的文本
 */
export async function translateText(text, targetLang = 'en-US') {
  console.log(`Starting translation: "${text}" to ${targetLang}`)
  
  try {
    // 如果目标语言是中文，直接返回原文
    if (targetLang === 'zh-CN') {
      console.log(`Target is Chinese, returning original: "${text}"`)
      return text
    }
    
    // 首先尝试本地翻译映射表
    if (localTranslationMap[text]) {
      console.log(`Local translation found: "${text}" -> "${localTranslationMap[text]}"`)
      return localTranslationMap[text]
    }
    
    // 使用 Google Translate 的免费接口（主要方案）
    try {
      console.log('Trying Google Translate...')
      const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=zh&tl=en&dt=t&q=${encodeURIComponent(text)}`
      console.log('Request URL:', url)
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log('Google Translate response:', data)
      
      if (data && data[0] && data[0][0]) {
        const translatedText = data[0][0][0]
        console.log(`Google Translate success: "${text}" -> "${translatedText}"`)
        return translatedText || text
      } else {
        console.warn('Google Translate returned invalid data:', data)
      }
    } catch (error) {
      console.warn('Google Translate failed:', error)
    }
    
    // 备用方案：使用 LibreTranslate
    try {
      console.log('Trying LibreTranslate...')
      const response = await fetch('https://libretranslate.de/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          source: 'zh',
          target: 'en',
          format: 'text'
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        const translatedText = data.translatedText || text
        console.log(`LibreTranslate success: "${text}" -> "${translatedText}"`)
        return translatedText
      } else {
        console.warn('LibreTranslate failed with status:', response.status)
      }
    } catch (error) {
      console.warn('LibreTranslate failed:', error)
    }
    
    // 如果所有翻译服务都失败，返回原文
    console.warn('All translation services failed, returning original text')
    return text
  } catch (error) {
    console.error('Translation failed:', error)
    return text // 翻译失败时返回原文
  }
}

/**
 * 构建多语言选项对象
 * @param {string} text - 用户输入的文本
 * @param {string} code - 选项的代码（可选）
 * @returns {Promise<Object>} 包含 code、zh_CN、en_US 的对象
 */
export async function buildMultilingualOption(text, code = null) {
  // 默认中文文本就是用户输入的文本
  const zhCN = text
  
  // 翻译为英文
  const enUS = await translateText(text, 'en-US')
  
  // 如果没有提供 code，则根据翻译后的英文生成
  if (!code) {
    code = enUS.toUpperCase().replace(/[^A-Z0-9]/g, '_')
  }
  
  return {
    code: code,
    zh_CN: zhCN,
    en_US: enUS
  }
}

/**
 * 批量构建多语言选项
 * @param {Array<string>} textOptions - 文本选项数组
 * @returns {Promise<Array>} 多语言选项数组
 */
export async function buildMultilingualOptions(textOptions) {
  const promises = textOptions.map(text => buildMultilingualOption(text))
  return Promise.all(promises)
}

/**
 * 解析选项字符串，支持多种格式
 * @param {string|Array} optionsStr - 选项字符串或数组
 * @returns {Array} 解析后的选项数组
 */
export function parseOptions(optionsStr) {
  if (!optionsStr || optionsStr === '-') {
    return []
  }
  
  // 如果已经是数组，直接返回
  if (Array.isArray(optionsStr)) {
    return optionsStr
  }
  
  // 尝试解析为 JSON
  if (typeof optionsStr === 'string' && optionsStr.startsWith('[') && optionsStr.endsWith(']')) {
    try {
      const parsed = JSON.parse(optionsStr)
      if (Array.isArray(parsed)) {
        return parsed.filter(Boolean); // Filter out empty values
      }
    } catch (e) {
      // 解析失败，继续处理
    }
  }
  
  // 如果是逗号分隔的字符串，分割处理
  if (typeof optionsStr === 'string') {
    return optionsStr.split(',').filter(Boolean).map(item => item.trim())
  }
  
  return []
}

/**
 * 从多语言选项中获取当前语言的显示文本
 * @param {Object} option - 多语言选项对象
 * @param {string} locale - 当前语言
 * @returns {string} 当前语言的显示文本
 */
export function getDisplayText(option, locale = 'zh-CN') {
  if (typeof option === 'string') {
    return option
  }
  
  if (option && typeof option === 'object') {
    if (locale === 'zh-CN') {
      return option.zh_CN || option.code || ''
    } else {
      return option.en_US || option.code || ''
    }
  }
  
  return ''
}

/**
 * 检查选项是否已经是多语言格式
 * @param {Object} option - 选项对象
 * @returns {boolean} 是否为多语言格式
 */
export function isMultilingualOption(option) {
  return option && typeof option === 'object' && 
         option.hasOwnProperty('code') && 
         option.hasOwnProperty('zh_CN') && 
         option.hasOwnProperty('en_US')
}

/**
 * 将简单文本选项转换为多语言格式（同步版本，用于显示）
 * @param {Array} textOptions - 文本选项数组
 * @returns {Array} 多语言选项数组（使用原文作为中英文）
 */
export function convertToMultilingualOptionsSync(textOptions) {
  return textOptions.map(text => {
    // 生成 code：英文大写，多文字用下划线拼接
    // 注意：这里暂时使用中文生成 code，实际应该使用翻译后的英文
    const code = text.toUpperCase().replace(/[^A-Z0-9]/g, '_')
    return {
      code: code,
      zh_CN: text,
      en_US: text // 暂时使用原文，后续可以通过翻译服务更新
    }
  })
}

/**
 * 获取选项的显示值（用于表单输入）
 * @param {Object|string} option - 选项对象或字符串
 * @returns {string} 显示值
 */
export function getOptionDisplayValue(option) {
  if (typeof option === 'string') {
    return option
  }
  
  if (isMultilingualOption(option)) {
    return option.zh_CN || option.code || ''
  }
  
  return option || ''
}

/**
 * 测试翻译功能
 * @param {string} text - 要测试的文本
 * @returns {Promise<void>}
 */
export async function testTranslation(text = '设备在途') {
  console.log('Testing translation for:', text)
  try {
    const result = await buildMultilingualOption(text)
    console.log('Translation result:', result)
    return result
  } catch (error) {
    console.error('Translation test failed:', error)
    return null
  }
}

/**
 * 批量测试翻译功能
 * @param {Array<string>} texts - 要测试的文本数组
 * @returns {Promise<Array>}
 */
export async function testBatchTranslation(texts = ['设备在途', '暂缓收款', '审核中']) {
  console.log('Testing batch translation for:', texts)
  try {
    const results = await buildMultilingualOptions(texts)
    console.log('Batch translation results:', results)
    return results
  } catch (error) {
    console.error('Batch translation test failed:', error)
    return []
  }
}

/**
 * 简单测试翻译服务
 * @param {string} text - 要测试的文本
 * @returns {Promise<void>}
 */
export async function simpleTestTranslation(text = '设备') {
  console.log('=== 开始测试翻译服务 ===')
  console.log('输入文本:', text)
  
  try {
    // 直接测试翻译
    const translated = await translateText(text, 'en-US')
    console.log('翻译结果:', translated)
    
    // 测试构建多语言选项
    const option = await buildMultilingualOption(text)
    console.log('多语言选项:', option)
    
    return option
  } catch (error) {
    console.error('翻译测试失败:', error)
    return null
  }
}

// 全局测试函数，方便在浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testTranslation = testTranslation
  window.testBatchTranslation = testBatchTranslation
  window.simpleTestTranslation = simpleTestTranslation
  console.log('Translation test functions available: testTranslation(text), testBatchTranslation(texts), simpleTestTranslation(text)')
} 
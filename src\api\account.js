import request from '@/utils/request'
import { getRoleList } from './role'

const API_PREFIX = '/cloud-service/user'

/**
 * 获取账号列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 当前页码
 * @param {number} params.size - 每页数量
 * @param {string} [params.keyword] - 搜索关键词(用户名/手机号)
 * @param {string} [params.status] - 账号状态
 * @param {string} [params.accountType] - 账号类型
 * @param {string} [params.enterpriseId] - 企业ID
 * @param {string} [params.userName] - 用户名称
 * @returns {Promise<Object>} 账号列表
 */
export function getAccountList(params) {
  return request({
    url: `${API_PREFIX}/List`,
    method: 'get',
    params
  })
}

/**
 * 创建账号
 * @param {Object} data - 账号数据
 * @returns {Promise<Object>} 创建结果
 */
export function createAccount(data) {
  return request({
    url: `${API_PREFIX}/add`,
    method: 'post',
    data
  })
}

/**
 * 更新账号信息
 * @param {Object} data - 账号数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateAccount(data) {
  return request({
    url: `/pub-mdm-platform/user/update`,
    method: 'put',
    data
  })
}

/**
 * 删除账号
 * @param {string|number} id - 账号ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteAccount(id) {
  return request({
    url: `${API_PREFIX}/delete`,
    method: 'delete',
    params: { userId: id }
  })
}

/**
 * 更新账号状态
 * @param {Object} data - 状态数据
 * @param {string|number} data.id - 账号ID
 * @param {string} data.status - 新状态('active'/'disabled')
 * @returns {Promise<Object>} 更新结果
 */
export function updateAccountStatus(data) {
  // 状态更新通过用户修改接口进行
  return request({
    url: `${API_PREFIX}/updateStatus`,
    method: 'put',
    data: {
      ...data,
      id: data.id
    }
  })
}

/**
 * 重置账号密码
 * @param {Object} data - 密码数据
 * @param {string|number} data.id - 账号ID
 * @param {string} data.password - 新密码
 * @returns {Promise<Object>} 重置结果
 */
export function resetPassword(data) {
  // 密码重置通过用户修改接口进行
  return request({
    url: `/pub-mdm-platform/user/reset`,
    method: 'put',
    data: {
      id: data.id,
      username: data.username,
    }
  })
}

// https://dev-platform.easycontrol.io/pub-mdm-platform/user/reset

/**
 * 获取角色列表（用于选择角色）
 * @returns {Promise<Array>} 角色选项列表
 */
export function getRoleOptions() {
  // 调用角色列表接口，设置较大的size确保能获取到所有角色
  return getRoleList({ page: 1, size: 100 })
    .then(res => {
      if (res.code === 1000 && res.data && res.data.records) {
        // 转换为选项格式
        return res.data.records.map(role => ({
          id: role.id,
          roleName: role.roleName
        }))
      } else {
        return []
      }
    })
    .catch(() => {
      console.error('获取角色选项失败')
      return []
    })
} 
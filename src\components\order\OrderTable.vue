<template>
  <div class="order-table">
    <el-table
      v-loading="loading"
      :data="data"
      @selection-change="handleSelectionChange"
      border
      stripe
      style="width: 100%"
      :max-height="tableHeight"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
      <!-- 选择列 -->
      <el-table-column type="selection" width="55" fixed="left" />

      <!-- 订单号 -->
      <el-table-column 
        prop="orderId" 
        :label="$t('order.list.orderNo')" 
        min-width="140" 
        fixed="left"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <a class="order-id-link" @click="handleView(row)">{{ row.orderId }}</a>
        </template>
      </el-table-column>

      <!-- 订单类型 -->
      <el-table-column 
        prop="type" 
        :label="$t('order.list.orderType')" 
        width="100"
        sortable
      >
        <template #default="{ row }">
          <el-tag
            :type="row.type === 'installment' ? 'success' : 'primary'"
            effect="light"
          >
            {{ row.type === 'installment' ? $t('order.list.installmentWithTag') : $t('order.list.rentalWithTag') }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 首付日期 -->
      <el-table-column 
        prop="startDate" 
        :label="$t('order.list.firstPaymentDate')" 
        width="120"
        sortable
      />

      <!-- 本期 -->
      <el-table-column 
        prop="installment" 
        :label="$t('order.list.currentInstallment')" 
        width="100"
        sortable
      />
      <!-- 订单状态 -->
      <el-table-column 
        prop="statusText" 
        :label="$t('order.list.orderStatus')" 
        width="130"
        sortable
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            effect="light"
          >
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 坏账状态 -->
      <el-table-column 
        prop="badDebtFlag" 
        :label="$t('order.list.badDebt')" 
        width="100"
      >
        <template #default="{ row }">
          <span :class="row.badDebtFlag ? 'text-danger' : 'text-success'">
            {{ row.badDebtFlag ? $t('order.list.yes') : $t('order.list.no') }}
          </span>
        </template>
      </el-table-column>

      <!-- 客户信息 -->
      <el-table-column 
        :label="$t('order.list.customerName')" 
        min-width="120"
        prop="customerName"
        show-overflow-tooltip
      />
      
      <!-- 客户电话 -->
      <el-table-column 
        :label="$t('order.list.customerPhone')" 
        min-width="140"
        prop="phone"
        show-overflow-tooltip
      />

      <!-- 设备信息 -->
      <el-table-column 
        :label="$t('order.list.deviceInfo')" 
        min-width="180"
        prop="deviceInfo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.deviceBrand }} {{ row.deviceModel }} ({{ row.deviceName }})
        </template>
      </el-table-column>

      <!-- 序列号 -->
      <el-table-column 
        :label="$t('order.list.serialNumber')" 
        min-width="140"
        prop="serialNumber"
        show-overflow-tooltip
      />

      <!-- IMEI -->
      <el-table-column
        :label="$t('order.list.imei')"
        min-width="270"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ [row.imei1, row.imei2].filter(Boolean).join(' / ') }}
        </template>
      </el-table-column>

      <!-- 信贷状态 -->
      <el-table-column 
        :label="$t('order.list.financialStatus')" 
        width="120"
        prop="financeStatusText"
        sortable
      >
        <template #default="{ row }">
          <el-tag
            :type="getFinanceStatusType(row.financeStatus)"
            effect="light"
          >
            {{ row.financeStatusText }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 创建日期 -->
      <el-table-column 
        :label="$t('order.list.createDate')" 
        width="120"
        prop="createDate"
        sortable
      />

      <!-- 更新日期 -->
      <el-table-column 
        :label="$t('order.list.updateDate')" 
        width="120"
        prop="updateDate"
        sortable
      />

      <!-- 操作 -->
      <el-table-column 
        :label="$t('order.list.operations')" 
        fixed="right" 
        width="200"
      >
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            text
            @click="handleView(row)"
          >
            {{ $t('order.list.actions.view') }}
          </el-button>
          <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, row)">
            <el-button type="primary" size="small" text>
              {{ $t('common.more') }} <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="'payment'">
                  {{ $t('order.list.actions.payment') }}
                </el-dropdown-item>
                <el-dropdown-item :command="'attachment'">
                  {{ $t('order.list.actions.attachment') }}
                </el-dropdown-item>
                <el-dropdown-item :command="'statusUpdate'">
                  {{ $t('order.list.actions.statusUpdate') }}
                </el-dropdown-item>
                <el-dropdown-item 
                  command="delete" 
                  :disabled="!row.canDelete"
                  :style="{ color: row.canDelete ? '#F56C6C' : '' }"
                >
                  <el-icon><Delete /></el-icon>
                  {{ $t('common.delete') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[20, 50, 100]"
        :layout="'total, sizes, prev, pager, next, jumper'"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import { ArrowDown, Delete } from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  pageSize: {
    type: Number,
    default: 20
  },
  currentPage: {
    type: Number,
    default: 1
  }
})

// 定义事件
const emit = defineEmits([
  'selection-change',
  'page-change',
  'size-change',
  'view',
  'edit',
  'delete',
  'payment',
  'attachment',
  'status-update',
  'update:currentPage',
  'update:pageSize'
])

// 表格高度自适应
const tableHeight = ref('calc(100vh - 270px)')

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  emit('size-change', size)
  emit('update:pageSize', size)
}

// 处理页码变化
const handleCurrentChange = (page) => {
  emit('page-change', page)
  emit('update:currentPage', page)
}

// 处理查看详情
const handleView = (row) => {
  emit('view', row)
}

// 处理下拉菜单操作
const handleCommand = (command, row) => {
  switch (command) {
    case 'payment':
      emit('payment', row)
      break
    case 'attachment':
      emit('attachment', row)
      break
    case 'statusUpdate':
      emit('status-update', row)
      break
    case 'delete':
      emit('delete', row)
      break
    default:
      break
  }
}

// 判断是否可以删除
const canDelete = (row) => {
  return row.canDelete;
}

// 获取订单状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case 'NORMAL':
      return 'success'; // 正常状态为绿色
    case 'OVERDUE':
      return 'warning'; // 逾期状态为橙色
    case 'COMPLETED':
      return 'primary'; // 已完成状态为蓝色
    case 'CANCELLED':
      return 'info'; // 已取消
    case 'ACTIVE':
      return 'success'; // 活跃
    default:
      return ''; // 默认
  }
}

// 获取付款状态对应的类型
const getFinanceStatusType = (status) => {
  const map = {
    // Standard statuses
    NORMAL: 'success',
    OVERDUE: 'warning',
    COMPLETED: 'info',
    SETTLED: 'info',
    IN_PROGRESS: 'primary',
    NOT_STARTED: 'info',
    
    // Custom financial statuses
    DEVICE_IN_TRANSIT: 'primary',
    DEFERRED_COLLECTION: 'warning',
    UNDER_REVIEW: 'primary',
    CUSTOMER_NEGOTIATING: 'warning',
    RISK_FLAGGED: 'danger',
    LEGAL_PROCESSING: 'danger',
    ASSET_REPORTED_LOST: 'danger',
    BLACKLISTED_CUSTOMER: 'danger'
  }
  return map[status] || 'info'
}

onMounted(() => {
  // 根据窗口大小调整表格高度
  const resizeTable = () => {
    tableHeight.value = `calc(100vh - 270px)`
  }
  
  window.addEventListener('resize', resizeTable)
  resizeTable()
  
  return () => {
    window.removeEventListener('resize', resizeTable)
  }
})
</script>

<style lang="scss" scoped>
.order-table {
  width: 100%;

  .el-table {
    width: 100%;
    margin-bottom: 16px;
    
    .order-id-link {
      color: #409EFF;
      cursor: pointer;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .pagination-container {
    position: sticky;
    bottom: 0;
    background-color: #fff;
    padding: 10px 0;
    z-index: 10;
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
  
  .text-danger {
    color: #F56C6C;
  }
  
  .text-success {
    color: #67c23a;
  }

  .text-muted {
    color: #909399;
  }
}
</style> 
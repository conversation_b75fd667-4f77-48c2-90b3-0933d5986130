import request from '@/utils/request'

const API_PREFIX = '/crm-service/api/customers'

/**
 * 获取客户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCustomerList(params) {
  return request({
    url: `${API_PREFIX}`,
    method: 'get',
    params
  })
}

/**
 * 分页查询客户列表
 * @param {Object} params 查询参数
 * @param {string} [params.name] 客户姓名
 * @param {string} [params.idNumber] 证件号码
 * @param {string} [params.phone] 联系电话
 * @param {boolean} [params.hasOrder] 是否有订单
 * @param {string} [params.startTime] 开始时间
 * @param {string} [params.endTime] 结束时间
 * @param {number} [params.current=1] 当前页码
 * @param {number} [params.size=10] 每页数量
 * @returns {Promise} 返回客户列表数据
 */
export function getCustomerPage(params) {
  return request({
    url: API_PREFIX,
    method: 'get',
    params
  })
}

/**
 * 创建客户
 * @param {Object} data 客户数据
 * @param {string} data.name 客户姓名
 * @param {string} data.idType 证件类型
 * @param {string} data.idNumber 证件号码
 * @param {string} data.phone 联系电话
 * @param {string} data.email 电子邮箱
 * @param {string} data.residenceAddress 居住地址
 * @param {string} data.mailingAddress 通信地址
 * @returns {Promise} 返回创建结果
 */
export function createCustomer(data) {
  return request({
    url: `${API_PREFIX}`,
    method: 'post',
    data
  })
}

/**
 * 更新客户信息
 * @param {Object} data 客户数据
 * @param {number} data.id 客户ID
 * @param {string} data.name 客户姓名
 * @param {string} data.phone 联系电话
 * @param {string} data.email 电子邮箱
 * @param {string} data.residenceAddress 居住地址
 * @param {string} data.mailingAddress 通信地址
 * @returns {Promise} 返回更新结果
 */
export function updateCustomer(id, data) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除客户
 * @param {number} id 客户ID
 * @returns {Promise} 返回删除结果
 */
export function deleteCustomer(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除客户
 * @param {number[]} ids 客户ID数组
 * @returns {Promise} 返回批量删除结果
 */
export function batchDeleteCustomers(ids) {
  return request({
    url: `${API_PREFIX}/batch`,
    method: 'delete',
    data: ids
  })
}

/**
 * 导出客户列表为CSV
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportCustomerListCSV(params) {
  return request({
    url: `${API_PREFIX}/export`,
    method: 'get',
    params: { ...params, format: 'csv' },
    responseType: 'blob'
  })
}

/**
 * 导出客户列表为Excel
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportCustomerListExcel(params) {
  return request({
    url: `${API_PREFIX}/export`,
    method: 'get',
    params: { ...params, format: 'excel' },
    responseType: 'blob'
  })
}

/**
 * 获取客户详情
 * @param {string} id - 客户ID
 * @returns {Promise}
 */
export function getCustomerDetail(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'get'
  })
}

/**
 * 检查客户是否可以删除
 * @param {string} id - 客户ID
 * @returns {Promise}
 */
export function checkCustomerCanDelete(id) {
  return request({
    url: `${API_PREFIX}/${id}/can-delete`,
    method: 'get'
  })
}

/**
 * 导出客户数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportCustomers(params) {
  return request({
    url: `${API_PREFIX}/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 按条件导出客户数据
 * @param {Object} params - 导出参数
 * @param {Object} params.dto - 查询条件
 * @param {Array} [params.ids] - 要导出的客户ID列表
 * @param {string} [params.format='excel'] - 导出格式，可选值：csv, excel
 * @returns {Promise} - 返回二进制数据流
 */
export function exportCustomersData(params = {}) {
  return request({
    url: `${API_PREFIX}/export/condition`,
    method: 'get',
    params,
    responseType: 'blob',
    headers: {
      'Accept': '*/*'
    }
  })
}

/**
 * 检查证件号码是否存在
 * @param {string} idNumber 证件号码
 * @returns {Promise<boolean>} 返回检查结果
 */
export function checkIdNumber(idNumber) {
  return request({
    url: `${API_PREFIX}/check-id-number`,
    method: 'get',
    params: { idNumber }
  })
}

/**
 * 搜索客户
 * @param {string} keyword - 搜索关键词（客户编号、姓名、证件号码或手机号）
 * @returns {Promise}
 */
export function searchCustomers(keyword) {
  return request({
    url: `${API_PREFIX}/search`,
    method: 'get',
    params: { keyword }
  })
} 
import request from '@/utils/request'

// 获取验证码
export function getCaptcha() {
  return request({
    url: '/cloud-service/captchaImage',
    method: 'get',
    responseType: 'blob',
    headers: {
      'Accept': 'image/jpeg'
    }
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/cloud-service/login',
    method: 'post',
    data: {
      username: data.username,
      password: data.password,
      code: data.code,
      uuid: data.uuid
    }
  })
}

// 获取用户信息
export function getUserInfo(params) {
  return request({
    url: '/cloud-service/userInfo',
    method: 'post',
    params
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/cloud-service/logout',
    method: 'post'
  })
} 
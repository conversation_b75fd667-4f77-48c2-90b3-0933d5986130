<script setup>
import { ref, onMounted, computed, onActivated } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/stores/modules/user';
import { Search } from '@element-plus/icons-vue';
import { ElLoading, ElMessage } from 'element-plus';
import { getTotalStatistics, getStatistics, getCurrentPeriodStatistics } from '@/api/statistics';

const { t } = useI18n();
const userStore = useUserStore();
const username = ref(userStore.username);
const loading = ref(false);

// 统计数据
const statistics = ref({
  newOrdersCount: 0,
  completedOrdersCount: 0,
  badDebtOrdersCount: 0,
  actualReceived: {
    totalAmount: 0,
    orderCount: 0,
    initialPaymentAmount: 0,
    installmentAmount: 0,
    penaltyAmount: 0,
    depositAmount: 0
  },
  accountsReceivable: {
    totalAmount: 0,
    overdueAmount: 0,
    overdueRatio: 0,
    initialPaymentAmount: 0,
    installmentAmount: 0,
    penaltyAmount: 0,
    depositAmount: 0
  },
  expenditure: {
    totalAmount: 0,
    deviceCost: 0,
    badDebtAmount: 0
  }
});

// 企业及子企业合计数据
const companyStatistics = ref({
  totalOrdersCount: 0,
  overdueOrdersCount: 0,
  completedOrdersCount: 0,
  badDebtOrdersCount: 0,
  actualReceived: {
    totalAmount: 0,
    orderCount: 0,
    initialPaymentAmount: 0,
    installmentAmount: 0,
    penaltyAmount: 0,
    depositAmount: 0
  },
  accountsReceivable: {
    totalAmount: 0,
    overdueAmount: 0,
    overdueRatio: 0,
    initialPaymentAmount: 0,
    installmentAmount: 0,
    penaltyAmount: 0,
    depositAmount: 0
  },
  expenditure: {
    totalAmount: 0,
    deviceCost: 0,
    badDebtAmount: 0
  }
});

// 格式化数字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null) return '0.00';
  return parseFloat(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 日期选择快捷方式
const dateShortcuts = [
  {
    text: t('dateRange.lastMonth'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      return [start, end];
    }
  },
  {
    text: t('dateRange.lastQuarter'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start, end];
    }
  },
  {
    text: t('dateRange.lastSixMonths'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 6);
      return [start, end];
    }
  },
  {
    text: t('dateRange.lastYear'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setFullYear(start.getFullYear() - 1);
      return [start, end];
    }
  }
];

// 设置默认日期范围为最近一个月
const getDefaultDateRange = () => {
  const end = new Date();
  const start = new Date();
  start.setMonth(start.getMonth() - 1);
  return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
};

// 筛选条件
const dateRange = ref(getDefaultDateRange());
const selectedCompany = ref('');

// 企业选项
const companyOptions = ref([
  { value: 'all', label: t('common.all') },
  { value: 'company1', label: t('dashboard.mainCompany') },
  { value: 'company2', label: t('dashboard.branchA') },
  { value: 'company3', label: t('dashboard.branchB') },
  { value: 'company4', label: t('dashboard.branchC') }
]);

// 搜索处理函数
const handleSearch = () => {
  fetchDashboardData();
};

// 构建API请求参数
const buildApiParams = () => {
  const params = {
    periodType: 'monthly'
  };
  
  // 如果日期范围存在，添加到请求参数中
  if (dateRange.value && Array.isArray(dateRange.value)) {
    params.startTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  }
  
  if (selectedCompany.value && selectedCompany.value !== 'all') {
    params.enterpriseId = selectedCompany.value;
  }
  
  return params;
};

// 在组件被激活时也刷新数据
onActivated(() => {
  // 恢复到默认选择
  dateRange.value = getDefaultDateRange();
  selectedCompany.value = '';
  fetchDashboardData();
});

// 获取仪表盘数据
const fetchDashboardData = async () => {
  // 如果日期范围被清除，重置为默认日期范围并提示用户
  if (!dateRange.value) {
    dateRange.value = getDefaultDateRange();
    ElMessage.info(t('dashboard.resetToDefaultDateRange'));
  }
  
  const loadingInstance = ElLoading.service({
    target: '.dashboard-container',
    text: t('common.loading')
  });
  
  try {
    loading.value = true;
    const params = buildApiParams();
    
    // 并行请求获取当前时间段统计数据和企业合计数据
    const [currentPeriodRes, totalRes] = await Promise.all([
      getCurrentPeriodStatistics(params),
      getTotalStatistics()
    ]);
    
    // 处理当前时间段统计数据
    if (currentPeriodRes && currentPeriodRes.code === 1000 && currentPeriodRes.data) {
      // 映射API返回的数据到页面展示的格式
      const data = currentPeriodRes.data;
      statistics.value = {
        newOrdersCount: data.newOrdersCount || 0,
        completedOrdersCount: data.completedOrdersCount || 0,
        badDebtOrdersCount: data.badDebtOrdersCount || 0,
        actualReceived: {
          totalAmount: data.actualReceived?.totalAmount || 0,
          orderCount: data.actualReceived?.orderCount || 0,
          initialPaymentAmount: data.actualReceived?.initialPaymentAmount || 0,
          installmentAmount: data.actualReceived?.installmentAmount || 0,
          penaltyAmount: data.actualReceived?.penaltyAmount || 0,
          depositAmount: data.actualReceived?.depositAmount || 0
        },
        accountsReceivable: {
          totalAmount: data.accountsReceivable?.totalAmount || 0,
          overdueAmount: data.accountsReceivable?.overdueAmount || 0,
          overdueRatio: data.accountsReceivable?.overdueRatio || 0,
          initialPaymentAmount: data.accountsReceivable?.initialPaymentAmount || 0,
          installmentAmount: data.accountsReceivable?.installmentAmount || 0,
          penaltyAmount: data.accountsReceivable?.penaltyAmount || 0,
          depositAmount: data.accountsReceivable?.depositAmount || 0
        },
        expenditure: {
          totalAmount: data.expenditure?.totalAmount || 0,
          deviceCost: data.expenditure?.deviceCost || 0,
          badDebtAmount: data.expenditure?.badDebtAmount || 0
        }
      };
    }
    
    // 处理企业合计数据
    if (totalRes && totalRes.code === 1000 && totalRes.data) {
      // 映射API返回的数据到页面展示的格式
      const data = totalRes.data;
      companyStatistics.value = {
        totalOrdersCount: data.totalOrdersCount || 0,
        overdueOrdersCount: data.overdueOrdersCount || 0,
        completedOrdersCount: data.completedOrdersCount || 0,
        badDebtOrdersCount: data.badDebtOrdersCount || 0,
        actualReceived: {
          totalAmount: data.actualReceived?.totalAmount || 0,
          orderCount: data.actualReceived?.orderCount || 0,
          initialPaymentAmount: data.actualReceived?.initialPaymentAmount || 0,
          installmentAmount: data.actualReceived?.installmentAmount || 0,
          penaltyAmount: data.actualReceived?.penaltyAmount || 0,
          depositAmount: data.actualReceived?.depositAmount || 0
        },
        accountsReceivable: {
          totalAmount: data.accountsReceivable?.totalAmount || 0,
          overdueAmount: data.accountsReceivable?.overdueAmount || 0,
          overdueRatio: data.accountsReceivable?.overdueRatio || 0,
          initialPaymentAmount: data.accountsReceivable?.initialPaymentAmount || 0,
          installmentAmount: data.accountsReceivable?.installmentAmount || 0,
          penaltyAmount: data.accountsReceivable?.penaltyAmount || 0,
          depositAmount: data.accountsReceivable?.depositAmount || 0
        },
        expenditure: {
          totalAmount: data.expenditure?.totalAmount || 0,
          deviceCost: data.expenditure?.deviceCost || 0,
          badDebtAmount: data.expenditure?.badDebtAmount || 0
        }
      };
    }
  } catch (error) {
    console.error('获取仪表盘数据失败', error);
    ElMessage.error(error.message || t('dashboard.fetchDataFailed'));
  } finally {
    loading.value = false;
    loadingInstance.close();
  }
};

onMounted(() => {
  fetchDashboardData();
});
</script>

<template>
  <div class="dashboard-container">
    <!-- 顶部标题 -->
    <div class="dashboard-header">
      <h2 class="page-title">{{ t('menus.pureHome') }}</h2>
    </div>
    
    <!-- 筛选区域 -->
    <div class="filter-container">
      <div class="filter-wrapper">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          :range-separator="t('common.to')"
          :start-placeholder="t('dateRange.startDate')"
          :end-placeholder="t('dateRange.endDate')"
          :shortcuts="dateShortcuts"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 260px;"
          class="custom-date-range"
          placement="bottom-start"
        />
        
        <el-select
          v-model="selectedCompany"
          :placeholder="t('dashboard.selectCompany')"
          clearable
          filterable
          style="width: 140px; margin-left: 8px;"
        >
          <el-option
            v-for="item in companyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        
        <el-button type="primary" @click="handleSearch" :icon="Search" style="margin-left: 8px;">{{ t('common.search') }}</el-button>
      </div>
    </div>
   
    
    <!-- 企业及子企业合计数据 -->
    <div class="section-title">
      <div class="title-line"></div>
      <span>{{ t('dashboard.enterpriseTotalStatistics') }}</span>
    </div>
    
    <!-- 下半部分卡片 -->
    <div class="card-section">
      <el-row :gutter="20">
        <!-- 订单数 -->
        <el-col :xs="24" :sm="12" :md="6">
          <div class="data-card green-card">
            <div class="card-title">{{ t('dashboard.totalOrders') }}</div>
            <div class="card-value">{{ formatNumber(companyStatistics.totalOrdersCount) }}</div>
          </div>
        </el-col>
        
        <!-- 逾期订单数 -->
        <el-col :xs="24" :sm="12" :md="6">
          <div class="data-card orange-card">
            <div class="card-title">{{ t('dashboard.overdueOrders') }}</div>
            <div class="card-value">{{ formatNumber(companyStatistics.overdueOrdersCount) }}</div>
          </div>
        </el-col>
        
        <!-- 完成订单数 -->
        <el-col :xs="24" :sm="12" :md="6">
          <div class="data-card blue-card">
            <div class="card-title">{{ t('dashboard.completedOrdersCount') }}</div>
            <div class="card-value">{{ formatNumber(companyStatistics.completedOrdersCount) }}</div>
          </div>
        </el-col>
        
        <!-- 坏账订单数 -->
        <el-col :xs="24" :sm="12" :md="6">
          <div class="data-card red-card">
            <div class="card-title">{{ t('dashboard.badDebtOrdersCount') }}</div>
            <div class="card-value">{{ formatNumber(companyStatistics.badDebtOrdersCount) }}</div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="mt-20">
        <!-- 实收金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card green-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.actualReceived') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(companyStatistics.actualReceived.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.initialPaymentIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.actualReceived.initialPaymentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.installmentIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.actualReceived.installmentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.penaltyIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.actualReceived.penaltyAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.depositReceived') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.actualReceived.depositAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 应收金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card orange-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.accountsReceivable') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(companyStatistics.accountsReceivable.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.initialPaymentReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.accountsReceivable.initialPaymentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.installmentReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.accountsReceivable.installmentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.penaltyReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.accountsReceivable.penaltyAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.overdueIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.accountsReceivable.overdueAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.depositReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.accountsReceivable.depositAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 支出金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card orange-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.expenditure') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(companyStatistics.expenditure.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.deviceCost') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.expenditure.deviceCost) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.badDebtAmount') }}</span>
                <span class="detail-value">{{ formatCurrency(companyStatistics.expenditure.badDebtAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>


       
    <!-- 当前数据统计 -->
    <div class="section-title">
      <div class="title-line"></div>
      <span>{{ t('dashboard.currentStatistics') }}</span>
    </div>
    
    <!-- 上半部分卡片 -->
    <div class="card-section">
      <el-row :gutter="20">
        <!-- 新增订单数 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card green-card">
            <div class="card-title">{{ t('dashboard.newOrdersCount') }}</div>
            <div class="card-value">{{ formatNumber(statistics.newOrdersCount) }}</div>
          </div>
        </el-col>
        
        <!-- 完成订单数 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card blue-card">
            <div class="card-title">{{ t('dashboard.completedOrdersCount') }}</div>
            <div class="card-value">{{ formatNumber(statistics.completedOrdersCount) }}</div>
          </div>
        </el-col>
        
        <!-- 坏账订单数 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card red-card">
            <div class="card-title">{{ t('dashboard.badDebtOrdersCount') }}</div>
            <div class="card-value">{{ formatNumber(statistics.badDebtOrdersCount) }}</div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="mt-20">
        <!-- 实收金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card green-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.actualReceived') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(statistics.actualReceived.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.initialPaymentIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.actualReceived.initialPaymentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.installmentIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.actualReceived.installmentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.penaltyIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.actualReceived.penaltyAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.depositReceived') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.actualReceived.depositAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 应收金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card orange-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.accountsReceivable') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(statistics.accountsReceivable.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.initialPaymentReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.accountsReceivable.initialPaymentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.installmentReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.accountsReceivable.installmentAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.penaltyReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.accountsReceivable.penaltyAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.overdueIncome') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.accountsReceivable.overdueAmount) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.depositReceivable') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.accountsReceivable.depositAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 支出金额 -->
        <el-col :xs="24" :sm="12" :md="8">
          <div class="data-card white-card orange-border">
            <div class="card-header">
              <div class="card-title">{{ t('dashboard.expenditure') }}</div>
            </div>
            <div class="card-value">{{ formatCurrency(statistics.expenditure.totalAmount) }}</div>
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.deviceCost') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.expenditure.deviceCost) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ t('dashboard.badDebtAmount') }}</span>
                <span class="detail-value">{{ formatCurrency(statistics.expenditure.badDebtAmount) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  
  .dashboard-header {
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .filter-container {
    margin-bottom: 20px;
    
    .filter-wrapper {
      display: inline-flex;
      align-items: center;
      padding: 12px 15px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .section-title {
    position: relative;
    display: flex;
    align-items: center;
    margin: 20px 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    
    .title-line {
      width: 4px;
      height: 16px;
      background-color: #1890ff;
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  .card-section {
    margin-bottom: 30px;
    
    .mt-20 {
      margin-top: 20px;
    }
    
    .data-card {
      height: 100%;
      padding: 20px;
      border-radius: 4px;
      margin-bottom: 20px;
      background-color: #fff;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .card-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 15px;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: 500;
        margin: 10px 0;
      }
      
      .card-details {
        margin-top: 20px;
        
        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .detail-label {
            color: #909399;
            font-size: 13px;
          }
          
          .detail-value {
            color: #606266;
            font-size: 13px;
            text-align: right;
          }
        }
      }
    }
    
    // 绿色卡片 - 新增订单数/订单数
    .green-card {
      border-top: 4px solid #67C23A;
      border-radius: 2%;
      
      .card-value {
        color: #67C23A;
      }
    }
    
    // 蓝色卡片 - 完成订单数
    .blue-card {
      border-top: 4px solid #409EFF;
      border-radius: 2%;

      .card-value {
        color: #409EFF;
      }
    }
    
    // 红色卡片 - 坏账订单数
    .red-card {
      border-top: 4px solid #F56C6C;
      border-radius: 2%;

      .card-value {
        color: #F56C6C;
      }
    }
    
    // 橙色卡片 - 逾期订单数
    .orange-card {
      border-top: 4px solid #E6A23C;
      border-radius: 2%;

      .card-value {
        color: #E6A23C;
      }
    }
    
    // 白色卡片 - 实收金额
    .white-card {
      border-top: 4px solid #909399;
      border-radius: 2%;

    }
    
    // 绿色边框 - 实收金额
    .green-border {
      border-top: 4px solid #4CAF50;
      border-radius: 2%;
    }

    // 蓝色边框 - 完成订单数
    .blue-border {
      border-top: 4px solid #2196F3;
      border-radius: 2%;
    }

    // 橙色边框 - 应收金额/支出金额
    .orange-border {
      border-top: 4px solid #FF9800;
      border-radius: 2%;
    }
  }
}

// 修复日期选择器删除图标的位置和文本重叠问题
:deep(.custom-date-range) {
  // 增加内部输入框右侧的padding，为删除图标留出空间
  .el-range-input {
    padding-right: 25px;
  }
  
  .el-range__close-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    display: flex;
    align-items: center;
    height: auto;
    cursor: pointer;
    z-index: 1;
  }
  
  // 确保文本不会和图标重叠
  .el-range-separator {
    padding: 0 5px;
  }
}
</style>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('order.attachment.title')"
    width="40%"
    :destroy-on-close="true"
  >
    <div class="attachment-manager">
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          action="#"
          :http-request="handleUpload"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :on-exceed="handleExceed"
          :on-remove="handleFileRemove"
          multiple
          :disabled="uploading || remainingLimit <= 0"
          :limit="remainingLimit"
          drag
          :show-file-list="false"
          accept=".jpg,.jpeg,.png,.pdf"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <span>{{ $t('order.attachment.dragHint') }}</span>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ $t('order.attachment.formatLimit') }} ({{ $t('order.attachment.sizeLimit') }})
            </div>
          </template>
        </el-upload>
      </div>
      
      <!-- 新增：显示已上传的附件列表 -->
      <div v-if="existingAttachments.length + uploadedFilesInSession.length > 0" class="existing-attachments-section">
        <h4>{{ $t('order.attachment.allAttachments') }} ({{ existingAttachments.length + uploadedFilesInSession.length }})</h4>
        <div v-if="loadingExistingAttachments" class="loading-container">
          <el-progress 
            type="circle" 
            :width="20" 
            :stroke-width="2" 
            :percentage="100" 
            status="success" 
            :indeterminate="true"
            :duration="1"
            class="custom-loader"
          />
          <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
        </div>
        <ul class="existing-attachment-list">
          <li v-for="attachment in [...existingAttachments, ...uploadedFilesInSession]" :key="attachment.id || attachment.fileUrl" class="existing-attachment-item">
            <span class="existing-filename">
              <el-icon><Document /></el-icon> 
              {{ attachment.fileName }}
            </span>
            <div class="attachment-actions">
              <el-link :href="attachment.fileUrl" target="_blank" class="existing-view-link">
                {{ $t('common.view') }}
              </el-link>
              <el-link @click="handleDownload(attachment)" class="existing-download-link">
                {{ $t('common.download') }}
              </el-link>
              <el-link 
                type="danger"
                class="existing-delete-link"
                @click="handleRemoveExistingAttachment(attachment)"
              >
                {{ $t('common.delete') }}
              </el-link>
            </div>
          </li>
        </ul>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer-actions"> 
        <el-button @click="handleCloseDialog">{{ $t('common.close') }}</el-button>
        <el-button type="primary" @click="handleSaveAttachments" :loading="isSaving" :disabled="uploading">{{ $t('common.save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { UploadFilled, Document, Delete } from '@element-plus/icons-vue';
import { uploadFile, downloadOssFile } from '@/api/upload';
import { updateOrderAttachments, getOrderAttachments, deleteOrderAttachment } from '@/api/order';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: ''
  },
});

const emit = defineEmits(['update:modelValue', 'save-attachments']);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const activeUploads = ref(0);
const uploading = computed(() => activeUploads.value > 0);
const isSaving = ref(false);
const loadingExistingAttachments = ref(false);

const remainingLimit = computed(() => {
  const limit = 5;
  const currentTotal = existingAttachments.value.length + uploadedFilesInSession.value.length;
  return limit > currentTotal ? limit - currentTotal : 0;
});

const fileList = ref([]);
const uploadedFilesInSession = ref([]);
const existingAttachments = ref([]);

const uploadStatuses = ref([]);
let summaryTimeout = null;

const triggerShowSummary = () => {
  if (summaryTimeout) {
    clearTimeout(summaryTimeout);
  }
  summaryTimeout = setTimeout(() => {
    if (activeUploads.value === 0 && uploadStatuses.value.length > 0) {
      showSummaryNotification();
    } else if (activeUploads.value > 0) {
      triggerShowSummary();
    }
  }, 500);
};

const showSummaryNotification = () => {
  if (uploadStatuses.value.length === 0) return;

  const successes = uploadStatuses.value.filter(s => s.status === 'success');
  const failures = uploadStatuses.value.filter(s => s.status === 'error');

  const title = "上传结果汇总";

  let message = '';
  if (successes.length > 0) {
    message += `<p>${successes.length} 个文件上传成功。</p>`;
  }
  if (failures.length > 0) {
    message += `<p style="color: #F56C6C;">${failures.length} 个文件处理失败:</p>`;
    message += '<ul style="padding-left: 20px; margin: 0; max-height: 150px; overflow-y: auto;">';
    failures.forEach(f => {
      message += `<li style="font-size: 12px;"><strong>${f.fileName}</strong>: ${f.message}</li>`;
    });
    message += '</ul>';
  }

  ElNotification({
    title: title,
    dangerouslyUseHTMLString: true,
    message: message,
    type: failures.length > 0 ? 'warning' : 'success',
    duration: 3000,
  });

  uploadStatuses.value = [];
};

const beforeUpload = (file) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const isValidType = allowedTypes.includes(file.type) || file.name.toLowerCase().endsWith('.jpeg');

  if (!isValidType) {
    uploadStatuses.value.push({
      fileName: file.name,
      status: 'error',
      message: t('order.attachment.invalidFormat')
    });
  }

  const isLessThan5M = file.size / 1024 / 1024 < 5;
  if (!isLessThan5M) {
    uploadStatuses.value.push({
      fileName: file.name,
      status: 'error',
      message: t('order.attachment.sizeLimit', '文件大小不能超过5MB')
    });
  }
  
  const validationPassed = isValidType && isLessThan5M;
  if (!validationPassed) {
      triggerShowSummary();
  }
  return validationPassed;
};

const handleUpload = async (options) => {
  activeUploads.value++;
  const originalFile = options.file;
  
  try {
    const uploadResponse = await uploadFile(originalFile, 'order_attachments'); 

    if (uploadResponse && uploadResponse.data) {
      const serverResponseData = uploadResponse.data;
      const fileUrl = typeof serverResponseData === 'object' ? serverResponseData.url : serverResponseData;
      const fileName = typeof serverResponseData === 'object' ? (serverResponseData.name || serverResponseData.originalFilename || originalFile.name) : originalFile.name;

      // 使用后端返回的完整附件信息
      const thumbnailUrl = typeof serverResponseData === 'object' ? serverResponseData.thumbnailUrl : null;
      const fileSize = typeof serverResponseData === 'object' ? serverResponseData.fileSize : originalFile.size;
      const fileType = typeof serverResponseData === 'object' ? serverResponseData.fileType : originalFile.type;
      const uploadTime = typeof serverResponseData === 'object' ? serverResponseData.uploadTime : new Date().toISOString();

      uploadedFilesInSession.value.push({
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Temporary ID for new uploads
        fileName: fileName,
        fileUrl: fileUrl,
        thumbnailUrl: thumbnailUrl,
        fileSize: fileSize,
        fileType: fileType,
        uploadTime: uploadTime
      });
      
      uploadStatuses.value.push({
          fileName: originalFile.name,
          status: 'success'
      });
      options.onSuccess(uploadResponse, originalFile);

    } else {
      const errorMsg = t('order.attachment.uploadFailedInvalidResponse', { fileName: originalFile.name });
      uploadStatuses.value.push({ fileName: originalFile.name, status: 'error', message: errorMsg });
      options.onError(new Error(errorMsg), originalFile);
    }

  } catch (error) {
    console.error('调用 uploadFile 失败:', error);
    let errorMessage = t('order.attachment.uploadFailed', { fileName: originalFile.name });
    if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
    } else if (error.data && error.data.message) { 
        errorMessage = error.data.message;
    }
    uploadStatuses.value.push({ fileName: originalFile.name, status: 'error', message: errorMessage });
    options.onError(new Error(errorMessage), originalFile);
  } finally {
    activeUploads.value--;
    triggerShowSummary();
  }
};

const handleExceed = (files) => {
  ElMessage.warning(t('order.attachment.uploadLimit'));
};

const handleFileRemove = (file) => {
  // For files newly selected but not yet uploaded (might not have a server ID)
  fileList.value = fileList.value.filter(item => item.uid !== file.uid);
  // For files already uploaded in the current session (have a temporary ID)
  uploadedFilesInSession.value = uploadedFilesInSession.value.filter(item => item.fileUrl !== file.fileUrl);
  // ElMessage.success(t('order.attachment.fileRemoved', { fileName: file.name }));
};

const handleRemoveExistingAttachment = async (attachment) => {
  console.log('handleRemoveExistingAttachment called with attachment:', attachment);
  ElMessageBox.confirm(
    t('order.attachment.deleteConfirm'),
    t('common.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  )
  .then(async () => {
    console.log('User confirmed deletion.');
    console.log('Current orderId:', props.orderId);
    console.log('Attachment ID for deletion attempt:', attachment.id);

    if (!props.orderId) {
      ElMessage.error(t('order.attachment.noOrderId'));
      console.error('Error: orderId is missing, cannot proceed with deletion.');
      return;
    }

    if (typeof attachment.id === 'string' && attachment.id.startsWith('temp_')) {
      // If it's a newly uploaded file in the current session (temporary ID)
      console.log('Removing temporary file from session:', attachment.fileName);
      uploadedFilesInSession.value = uploadedFilesInSession.value.filter(file => file.id !== attachment.id);
      ElMessage.success(t('order.attachment.fileRemoved', { fileName: attachment.fileName }));
    } else {
      console.log('Handling existing attachment deletion path.');
      // If it's an existing attachment (real ID)
      try {
        console.log('Attempting to delete existing attachment with ID:', attachment.id);
        await deleteOrderAttachment(props.orderId, attachment.id);
        existingAttachments.value = existingAttachments.value.filter(file => file.id !== attachment.id);
        ElMessage.success(t('order.attachment.deleteSuccess'));
        console.log('Attachment deleted successfully from backend and UI.');
      } catch (error) {
        console.error('Error deleting attachment via API:', error);
        ElMessage.error(t('order.attachment.deleteFailed'));
      }
    }
    console.log('End of .then() block execution.');
  })
  .catch(() => {
    console.log('User cancelled deletion. (from catch)');
    // User cancelled deletion
  });
};

const handleSaveAttachments = async () => {
  if (!props.orderId) {
    ElMessage.error(t('order.attachment.noOrderId'));
    return;
  }

  isSaving.value = true;

  try {
    // The final list of attachments is the combination of what's already on the server
    // (and wasn't deleted in this session) and what was just uploaded.
    const finalAttachments = [
      ...existingAttachments.value,
      ...uploadedFilesInSession.value,
    ];

    // Map this final list to the format expected by the API.
    const attachmentsPayload = finalAttachments.map(file => ({
      fileName: file.fileName,
      fileUrl: file.fileUrl,
    }));
    
    // Send the complete list to the backend. This will overwrite the previous list.
    await updateOrderAttachments(props.orderId, attachmentsPayload);
    ElMessage.success(t('order.attachment.saveSuccess'));
    
    // After a successful save, reset the local state and inform the parent.
    uploadedFilesInSession.value = [];
    fetchExistingAttachments(props.orderId); // Re-fetch to ensure consistency if the dialog is reopened.
    emit('save-attachments');
    dialogVisible.value = false;

  } catch (error) {
    console.error('Error saving attachments:', error);
    ElMessage.error(t('order.attachment.saveFailed'));
  } finally {
    isSaving.value = false;
  }
};

const fetchExistingAttachments = async (orderId) => {
  if (!orderId) {
    existingAttachments.value = [];
    return;
  }
  loadingExistingAttachments.value = true;
  try {
    const response = await getOrderAttachments(orderId);
    if (response && response.data) {
      existingAttachments.value = response.data.map(att => ({
        id: att.id,
        fileName: att.fileName,
        fileUrl: att.fileUrl,
        thumbnailUrl: att.thumbnailUrl,
        fileSize: att.fileSize,
        fileType: att.fileType,
        uploadTime: att.uploadTime
      }));
    } else {
      existingAttachments.value = [];
    }
  } catch (error) {
    console.error('获取已有附件失败:', error);
    ElMessage.error(t('order.attachment.fetchFailed'));
    existingAttachments.value = [];
  } finally {
    loadingExistingAttachments.value = false;
  }
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  // Optionally, clear states when dialog closes if you don't want to persist between openings
  fileList.value = [];
  uploadedFilesInSession.value = [];
};

// New function to handle download
const handleDownload = async (attachment) => {
  if (attachment.fileUrl) {
    try {
      const { blob, filename } = await downloadOssFile(attachment.fileUrl);

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      ElMessage.success(t('common.download') + t('common.success'));
    } catch (error) {
      console.error('文件下载失败:', error);
      ElMessage.error(t('common.download') + t('common.failed') + ': ' + (error.message || t('common.unknown')));
    }
  } else {
    ElMessage.warning(t('order.attachment.downloadUrlMissing'));
  }
};

// Watch for dialog visibility and orderId changes to fetch existing attachments
watch(dialogVisible, (newVal) => {
  if (newVal && props.orderId) {
    fetchExistingAttachments(props.orderId);
  } else if (!newVal) {
    // Clear attachments when dialog is closed
    existingAttachments.value = [];
  }
});

watch(() => props.orderId, (newVal) => {
  if (dialogVisible.value && newVal) {
    fetchExistingAttachments(newVal);
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.attachment-manager {
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }
  
  .upload-section {
    margin-bottom: 20px;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    
    .el-upload {
      width: 100%;
    }
  }
  
  .attachment-list {
    .list-header {
      display: grid;
      grid-template-columns: 3fr 1fr 1fr 2fr 2fr;
      gap: 10px;
      padding: 10px 0;
      font-weight: 600;
      border-bottom: 1px solid var(--el-border-color-light);
    }
    
    .no-attachments {
      padding: 30px 0;
      text-align: center;
      color: var(--el-text-color-secondary);
    }
    
    .attachment-items {
      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid var(--el-border-color-extra-light);
        
        &:last-child {
          border-bottom: none;
        }
        
        .existing-filename {
          display: flex;
          align-items: center;
          color: var(--el-text-color-primary);
          font-size: 0.9em;
          
          .el-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--el-color-info);
          }
        }
        
        .existing-view-link {
          margin-left: 10px;
          color: var(--el-color-primary);
          font-size: 0.9em;
        }
      }
      
      .item-actions {
        display: flex;
        align-items: center; /* 垂直居中 */
        gap: 10px; /* 按钮间距 */
        flex-shrink: 0; /* 防止操作区域收缩 */

        .el-link {
          font-size: 0.9em; /* 调整字体大小 */
          white-space: nowrap; /* 防止换行 */
        }

        .el-button.el-button--danger.is-circle {
          padding: 0; /* 移除默认内边距 */
          min-height: auto; /* 自动高度 */
          font-size: 1em; /* 确保图标大小正常 */
        }
      }
    }
  }
}

.preview-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }
  
  .preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--el-fill-color-darker);
    
    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    
    .preview-pdf {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .preview-error {
      padding: 20px;
      text-align: center;
      color: var(--el-color-danger);
    }
  }
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  .el-icon--upload {
    font-size: 40px;
    color: var(--el-color-primary);
    margin-bottom: 10px;
  }
}

:deep(.el-upload__tip) {
  text-align: center;
  padding: 0 20px 10px;
}

.existing-attachments-section {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  background-color: var(--el-fill-color-lighter);
  
  h4 {
    margin-top: 0;
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 1em;
    color: var(--el-text-color-regular);
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
  }

  .existing-attachment-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .existing-attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-extra-light);
    
    &:last-child {
      border-bottom: none;
    }
    
    .existing-filename {
      display: flex;
      align-items: center;
      color: var(--el-text-color-primary);
      font-size: 0.9em;
      
      .el-icon {
        margin-right: 6px;
        font-size: 16px;
        color: var(--el-color-info);
      }
    }
    
    .existing-view-link {
      margin-left: 10px;
      color: var(--el-color-primary);
      font-size: 0.9em;
    }
  }
  
  .el-empty {
    padding: 10px 0;
    --el-empty-description-font-size: 13px;
  }
}

.dialog-footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.attachment-actions {
  display: flex;
  align-items: center; /* 垂直居中 */
  gap: 10px; /* 按钮间距 */
  flex-shrink: 0; /* 防止操作区域收缩 */

  .el-link {
    font-size: 0.9em; /* 调整字体大小 */
    white-space: nowrap; /* 防止换行 */
  }

  .el-button.el-button--danger.is-circle {
    padding: 0; /* 移除默认内边距 */
    min-height: auto; /* 自动高度 */
    font-size: 1em; /* 确保图标大小正常 */
  }
}

.existing-download-link {
  color: var(--el-color-primary);
  font-size: 0.9em;
}
</style> 
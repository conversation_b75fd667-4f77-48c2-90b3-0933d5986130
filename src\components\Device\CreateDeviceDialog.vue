﻿<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
    :modal-append-to-body="false"
    style="margin-top: 3vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="device-form"
    >
      <div class="form-section">
        <div class="section-title">{{ t('device.basicInfo') }}</div>
        <!-- 品牌、型号、型号编号 -->
        <el-row :gutter="8" class="top-input-row">
          <el-col :span="8">
            <el-form-item :label="t('device.brand.label')" prop="brand">
              <el-select v-model="formData.brand" :placeholder="t('device.placeholder.brand')" clearable style="width: 100%">
                <el-option
                  v-for="item in brandOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('device.model')" prop="model">
              <el-input v-model="formData.model" :placeholder="t('device.placeholder.model')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('device.modelNumber')" prop="deviceName">
              <el-input v-model="formData.deviceName" :placeholder="t('device.placeholder.modelNumber')" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item class="unbreakable-label-row">
              <template #label>
                <span>{{ t('device.deviceInfoLabel')}}</span>
                <span class="auto-generate-label"> ({{ t('device.autoGenerated') }})</span>
              </template>
              <el-input v-model="autoGeneratedDeviceInfo" :placeholder="t('device.deviceInfoPlaceholder', '设备信息将自动生成, 无需手动输入')" disabled />
              <div class="el-form-item__description">
                {{ t('device.deviceInfoHint') }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12" v-if="isEdit || showStatusFieldOnCreate">
            <el-form-item :label="t('device.status.label')" prop="status">
              <el-select v-model="formData.status" :placeholder="t('device.rules.statusRequired')" style="width: 100%" popper-class="device-status-dropdown" clearable>
                <el-option :label="t('device.status.IN_STOCK')" value="IN_STOCK" />
                <el-option :label="t('device.status.IN_USE')" value="IN_USE" />
                <el-option :label="t('device.status.UNDER_REPAIR')" value="UNDER_REPAIR" />
                <el-option :label="t('device.status.IN_TRANSIT')" value="IN_TRANSIT" />
                <el-option :label="t('device.status.SCRAPPED')" value="SCRAPPED" />
                <el-option :label="t('device.status.LOST')" value="LOST" />
                <el-option :label="t('device.status.PREPARING')" value="PREPARING" />
                <el-option :label="t('device.status.SOLD')" value="SOLD" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="t('device.color.label')" prop="color">
              <el-select v-model="formData.color" :placeholder="t('device.placeholder.color')" clearable style="width: 100%">
                <el-option
                  v-for="item in colorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('device.costAmount')" prop="costAmount">
              <el-input
                v-model="formData.costAmount"
                :placeholder="t('device.rules.costAmountPlaceholder')"
                maxlength="7"
                @input="handleCostAmountInput"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <div class="section-title">{{ t('device.identificationInfo') }}</div>
        <!-- 序列号和IMEI -->
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="t('device.serialNumber')" prop="sn">
              <el-input v-model="formData.sn" :placeholder="t('device.serialNumberPlaceholder')">
                <template #append>
                  <el-tooltip :content="t('device.serialNumberTip')" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('device.imei1')" prop="imei1">
              <el-input v-model="formData.imei1" :placeholder="t('device.imeiPlaceholder')" maxlength="15" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="t('device.imei2')" prop="imei2">
              <el-input v-model="formData.imei2" :placeholder="t('device.imei2Placeholder')" maxlength="15" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <div class="section-title">{{ t('device.additionalInfo') }}</div>
        <!-- 备注 -->
        <el-form-item :label="t('device.remarks')" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :placeholder="t('device.remarksPlaceholder')"
            :rows="3"
            maxlength="256"
            show-word-limit
          />
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item :label="t('device.attachments')">
          <div class="attachment-section">
            <el-upload
              ref="uploadRef"
              class="upload-area"
              drag
              :action="null"
              accept=".jpg,.jpeg,.png,.pdf"
              :auto-upload="true"
              :http-request="handleDeviceAttachmentUpload"
              v-model:file-list="deviceUiFileList"
              :before-upload="handleDeviceBeforeUpload"
              :on-remove="handleDeviceElUploadRemove"
              :show-file-list="false"
              :limit="5"
              :on-exceed="handleUploadExceed"
              multiple
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                {{ t('device.uploadText') }}
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  {{ t('device.uploadTip') }}
                </div>
              </template>
            </el-upload>
            
            <div class="attachment-list" v-if="displayFileList.length > 0">
              <div v-for="file in displayFileList" :key="file.uid" class="attachment-card">
                <div class="attachment-thumbnail">
                  <el-image v-if="isImage(file) && file.url" :src="file.url" fit="cover" class="thumbnail-image">
                    <template #error>
                      <div class="thumbnail-icon image-error">
                        <el-icon><WarningFilled /></el-icon>
                        <span>{{ t('common.imageError', '加载失败') }}</span>
                      </div>
                    </template>
                  </el-image>
                  <el-image v-else-if="isPdf(file) && file.thumbnail" :src="file.thumbnail" fit="cover" class="thumbnail-image">
                    <template #error>
                      <div class="thumbnail-icon image-error">
                        <el-icon><Document /></el-icon>
                        <span>{{ t('common.previewFailed', '预览失败') }}</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else-if="isPdf(file) && !file.thumbnail" class="thumbnail-icon loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>{{ t('common.loading', '加载中...') }}</span>
                  </div>
                  <div v-else class="thumbnail-icon">
                    <el-icon><Document /></el-icon>
                    <span class="thumbnail-ext">{{ getFileType(file.name) }}</span>
                  </div>
                </div>
                <div class="attachment-info">
                  <div class="attachment-name" :title="file.name">{{ file.name }}</div>
                  <div class="attachment-details">
                    <span v-if="getFileType(file.name)">{{ getFileType(file.name) }}</span>
                    <span v-if="file.size">{{ formatSize(file.size) }}</span>
                    <span v-if="file.uploadDate">{{ formatDateTime(file.uploadDate, 'YYYY-MM-DD') }}</span>
                  </div>
                </div>
                <div class="attachment-actions">
                  <el-tooltip :content="t('common.view')" placement="top">
                    <el-button link type="primary" @click="handleDevicePreview(file)" :disabled="!isPreviewable(file)"><el-icon><View /></el-icon></el-button>
                  </el-tooltip>
                  <el-tooltip :content="t('common.download')" placement="top">
                    <el-button link type="primary" @click="handleDownload(file)"><el-icon><Download /></el-icon></el-button>
                  </el-tooltip>
                  <el-tooltip :content="t('common.delete')" placement="top">
                    <el-button link type="danger" @click="triggerDeviceRemoveFile(file)"><el-icon><Delete /></el-icon></el-button>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="default">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="uploading" size="default">
          {{ t('common.save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { InfoFilled, UploadFilled, Document, Loading, View, Download, Delete, WarningFilled } from '@element-plus/icons-vue'
import { createDevice, updateDevice } from '@/api/device'
import { uploadFile } from '@/api/upload'
import { formatDateTime } from '@/utils/format'
import * as pdfjsLib from 'pdfjs-dist/build/pdf.mjs'
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.mjs?url'
import { getDictFields } from '@/api/dictionary'

// pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker

const { t } = useI18n()

// 添加缺失的i18n翻译项
if (!t('device.colorInfo') || !t('device.deviceDetails')) {
  // 在组件内部检查并添加缺失的翻译
  const deviceTranslations = {
    'zh-CN': {
      device: {
        colorInfo: '颜色',
        deviceDetails: '设备详情'
      }
    },
    'en': {
      device: {
        colorInfo: 'Color',
        deviceDetails: 'Device Details'
      }
    }
  }
}

// 自定义序列号验证器
const validateSn = (rule, value, callback) => {
  if (!value) {
    return callback(new Error(t('device.rules.snRequired')))
  }
  if (!/^[a-zA-Z0-9]{8,20}$/.test(value)) {
    return callback(new Error(t('device.rules.snInvalid')))
  }
  callback()
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceData: {
    type: Object,
    default: () => null
  },
  showStatusFieldOnCreate: {
    type: Boolean,
    default: false
  },
  context: {
    type: String,
    default: 'device-management' // 'device-management' or 'order-creation'
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const formRef = ref(null)
const loading = ref(false)
const uploading = ref(false)
const deviceUiFileList = ref([])
const colorOptions = ref([])
const brandOptions = ref([])

const displayFileList = computed(() => {
  return deviceUiFileList.value.map(file => ({
    ...file,
    url: isImage(file) && file.url
      ? file.url.replace('https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com', '/cos-proxy')
      : file.url
  }))
})

const isEdit = computed(() => !!props.deviceData)

const dialogTitle = computed(() => {
  return isEdit.value ? t('dialog.editDevice') : t('dialog.createDevice')
})

const autoGeneratedDeviceInfo = computed(() => {
  const { brand, model, deviceName } = formData;

  const selectedBrand = brandOptions.value.find(option => option.value === brand);
  const brandLabel = selectedBrand ? selectedBrand.label : brand;

  const finalBrand = brandLabel ? brandLabel.toUpperCase() : '';

  const brandAndModel = [finalBrand, model].filter(Boolean).join(' ');

  if (brandAndModel && deviceName) {
    return `${brandAndModel} (${deviceName})`;
  }
  
  if(brandAndModel) {
    return brandAndModel
  }

  if(deviceName) {
    return `(${deviceName})`
  }

  return ''
});

const getDefaultFormData = () => ({
  brand: '',
  model: '',
  deviceName: '',
  color: '',
  sn: '',
  imei1: '',
  imei2: '',
  status: props.context === 'order-creation' ? 'IN_STOCK' : '',
  syncStatus: true,
  remark: '',
  costAmount: '0.00',
  attachments: []
})

const formData = reactive({
  brand: '',
  model: '',
  deviceName: '',
  color: '',
  sn: '',
  imei1: '',
  imei2: '',
  status: props.context === 'order-creation' ? 'IN_STOCK' : '',
  syncStatus: true,
  remark: '',
  costAmount: '0',
  attachments: []
})

const validateImei1 = (rule, value, callback) => {
  if (value && value === formData.imei2) {
    callback(new Error(t('device.rules.imei1DuplicateOnImei2')))
  } else {
    callback()
  }
}

const validateImei2 = (rule, value, callback) => {
  if (value && value === formData.imei1) {
    callback(new Error(t('device.rules.imei2DuplicateOnImei1')))
  } else {
    callback()
  }
}

const validateCostAmount = (rule, value, callback) => {
  if (!value) {
    return callback(new Error(t('device.costAmountRequired')))
  }
  const amount = Number(value)
  if (isNaN(amount) || !Number.isInteger(amount) || amount < 0) {
    return callback(new Error(t('device.costAmountInvalid')))
  }
  callback()
}

const rules = {
  brand: [{ required: true, message: t('device.rules.brandRequired'), trigger: 'change' }],
  model: [
    { required: true, message: t('device.rules.modelRequired'), trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9- ]{8,20}$/, message: t('device.rules.modelFormat'), trigger: 'change' }
  ],
  deviceName: [{ required: true, message: t('device.rules.modelNumberRequired'), trigger: 'blur' }],
  color: [{ required: true, message: t('device.rules.colorRequired'), trigger: 'change' }],
  sn: [
    {
      required: true,
      validator: validateSn,
      trigger: 'change',
    },
  ],
  imei1: [
    { pattern: /^\d{15}$/, message: t('device.rules.imei1Format'), trigger: 'blur' },
    { validator: validateImei1, trigger: 'change' }
  ],
  imei2: [
    { pattern: /^\d{15}$/, message: t('device.rules.imei2Format', { '0': 15 }), trigger: 'blur' },
    { validator: validateImei2, trigger: 'change' }
  ],
  status: [{ required: true, message: t('device.rules.statusRequired'), trigger: 'change' }],
  costAmount: [
    { required: true, message: t('device.rules.costAmountRequired'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === '') {
          // Let the required rule handle it
          return callback()
        }
        const num = Number(value)
        if (isNaN(num) || !Number.isInteger(num) || num < 0) {
          callback(new Error(t('device.costAmountInvalid')))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ]
}

const handleDeviceBeforeUpload = async (rawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const isValidType = allowedTypes.includes(rawFile.type);
  if (!isValidType) {
    ElMessage.error(t('common.uploadInvalidFormat'));
    return false;
  }

  const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
  const isLt5M = rawFile.size < maxSizeInBytes;
  if (!isLt5M) {
    ElMessage.error(t('common.uploadExceedTip'));
    return false;
  }
  
  return true;
};

const handleUploadExceed = () => {
  ElMessage.error(t('device.uploadLimitExceeded'));
};

const handleDeviceAttachmentUpload = async (options) => {
  const { file, onSuccess, onError } = options
  uploading.value = true

  const fileInList = deviceUiFileList.value.find((f) => f.uid === file.uid)
  if (fileInList) {
    fileInList.status = 'uploading'
  }

  try {
    const uploadRes = await uploadFile(file, 'device')

    if (uploadRes.code === 1000 && uploadRes.data) {
      const serverResponseData = uploadRes.data
      const fileUrl = typeof serverResponseData === 'object' ? serverResponseData.url : serverResponseData
      const fileName =
        typeof serverResponseData === 'object'
          ? serverResponseData.name || serverResponseData.originalFilename || file.name
          : file.name

      formData.attachments.push({ fileName, fileUrl })

      const fileIndex = deviceUiFileList.value.findIndex((f) => f.uid === file.uid)
      if (fileIndex !== -1) {
        const updatedFile = {
          ...deviceUiFileList.value[fileIndex],
          url: fileUrl,
          status: 'success',
          name: fileName,
          uploadDate: Date.now()
        }
        deviceUiFileList.value.splice(fileIndex, 1, updatedFile)
        
        // 如果是PDF，立即生成缩略图
        if (isPdf(updatedFile)) {
          generatePdfThumbnail(updatedFile).then(thumbnailUrl => {
            const finalIndex = deviceUiFileList.value.findIndex(f => f.uid === updatedFile.uid);
            if (finalIndex !== -1 && thumbnailUrl) {
              deviceUiFileList.value[finalIndex] = { ...deviceUiFileList.value[finalIndex], thumbnail: thumbnailUrl };
            }
          });
        }
      }

      onSuccess(uploadRes, file)
      ElMessage.success(t('common.uploadSuccess', { fileName: fileName }))
    } else {
      throw new Error(uploadRes.message || 'File upload failed with no specific message')
    }
  } catch (error) {
    console.error('上传错误:', error)
    const fileIndex = deviceUiFileList.value.findIndex((f) => f.uid === file.uid)
    if (fileIndex !== -1) {
      deviceUiFileList.value[fileIndex].status = 'fail'
    }
    onError(error, file)
    ElMessage.error(
      t('common.uploadFailed', {
        fileName: file.name,
        error: error.message || '未知错误'
      })
    )
  } finally {
    uploading.value = false
  }
}

const triggerDeviceRemoveFile = (fileToRemove) => {
  const index = deviceUiFileList.value.findIndex(f => f.uid === fileToRemove.uid);
  if (index !== -1) {
    const removedFile = deviceUiFileList.value.splice(index, 1)[0];
    
    if (removedFile.status === 'success' && removedFile.url) {
      const attachmentIndex = formData.attachments.findIndex(att => att.fileUrl === removedFile.url);
      if (attachmentIndex !== -1) {
        formData.attachments.splice(attachmentIndex, 1);
      }
    }
  }
};

const handleDeviceElUploadRemove = (file) => {
  // This is called by El-Upload's internal remove. Our triggerDeviceRemoveFile will also be called.
  // This function might not need explicit action if triggerDeviceRemoveFile handles everything.
  // console.log('El-Upload on-remove called for:', file.name);
  // Make sure not to double remove if triggerDeviceRemoveFile already handled it.
};

const isPdf = (file) => {
  const fileName = file.name || '';
  return fileName.toLowerCase().endsWith('.pdf');
};

const generatePdfThumbnail = async (file) => {
  const originalUrl = file.url
  if (!originalUrl) return null

  const proxiedUrl = originalUrl.replace('https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com', '/cos-proxy')

  try {
    const loadingTask = pdfjsLib.getDocument({
      url: proxiedUrl,
      cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/cmaps/`,
      cMapPacked: true
    })
    const pdf = await loadingTask.promise
    const page = await pdf.getPage(1) // Get the first page
    const viewport = page.getViewport({ scale: 0.5 })
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    canvas.height = viewport.height
    canvas.width = viewport.width
    await page.render({ canvasContext: context, viewport: viewport }).promise
    return canvas.toDataURL('image/png')
  } catch (error) {
    console.error(`Error generating thumbnail for ${file.name}:`, error)
    return null
  }
}

const getFileSize = async (fileUrl) => {
  if (!fileUrl) return null
  try {
    const proxiedUrl = fileUrl.replace('https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com', '/cos-proxy')
    const response = await fetch(proxiedUrl, { method: 'HEAD' })
    if (response.ok) {
      const contentLength = response.headers.get('Content-Length')
      return contentLength ? parseInt(contentLength, 10) : null
    }
    return null
  } catch (error) {
    console.error(`Failed to get file size for ${fileUrl}:`, error)
    return null
  }
}

const resetFormAndPopulateData = () => {
  formRef.value?.clearValidate();

  if (props.deviceData) {
    isEdit.value = true;
    Object.assign(formData, {
      brand: props.deviceData.brand || '',
      model: props.deviceData.model || '',
      deviceName: props.deviceData.deviceName || '',
      color: props.deviceData.color || '',
      sn: props.deviceData.sn || '',
      imei1: props.deviceData.imei1 || '',
      imei2: props.deviceData.imei2 || '',
      status: props.deviceData.status || (props.context === 'order-creation' ? 'IN_STOCK' : ''),
      syncStatus: props.deviceData.syncStatus !== undefined ? props.deviceData.syncStatus : true,
      remark: props.deviceData.remark || '',
      costAmount: props.deviceData.costAmount !== undefined && props.deviceData.costAmount !== null ? props.deviceData.costAmount : '0.00'
    });

    if (props.deviceData.attachments && Array.isArray(props.deviceData.attachments)) {
      formData.attachments = JSON.parse(JSON.stringify(
        props.deviceData.attachments.map(att => {
          const backendAttachment = {
            fileName: att.fileName !== undefined ? att.fileName : att.name,
            fileUrl: att.fileUrl !== undefined ? att.fileUrl : att.url,
          };
          
          return backendAttachment;
        }).filter(Boolean)
      ));
      
      deviceUiFileList.value = props.deviceData.attachments.map(att => {
        const uid = att.uid || att.id || Date.now() + Math.random();
        const fileUrl = att.fileUrl || att.url;
        const fileName = att.fileName || att.name;
        
        if (fileUrl && fileName) {
          const fileObject = {
            name: fileName,
            url: fileUrl,
            uid: uid,
            status: 'success',
            size: att.fileSize,
            uploadDate: att.uploadTime,
            thumbnail: null,
            raw: { id: att.id, fileName: fileName, fileUrl: fileUrl }
          };

          if (!fileObject.size) {
            getFileSize(fileObject.url).then(size => {
              if (size) {
                const index = deviceUiFileList.value.findIndex(f => f.uid === fileObject.uid)
                if (index !== -1) {
                  deviceUiFileList.value[index] = { ...deviceUiFileList.value[index], size: size }
                }
              }
            })
          }

          if (isPdf(fileObject)) {
            generatePdfThumbnail(fileObject).then(thumbnailUrl => {
              const index = deviceUiFileList.value.findIndex(f => f.uid === fileObject.uid)
              if (index !== -1 && thumbnailUrl) {
                deviceUiFileList.value[index] = { ...deviceUiFileList.value[index], thumbnail: thumbnailUrl }
              }
            });
          }

          return fileObject;
        } else {
          console.warn('Skipping invalid attachment for UI:', att);
          return null;
        }
      }).filter(file => file !== null);
      
      console.log('[resetFormAndPopulateData] deviceUiFileList populated:', JSON.parse(JSON.stringify(deviceUiFileList.value)));
    } else {
      formData.attachments = [];
      deviceUiFileList.value = [];
      console.log('[resetFormAndPopulateData] No attachments in props or not an array. Both formData.attachments and deviceUiFileList are empty.');
    }
  } else {
    console.log('[resetFormAndPopulateData] Creation mode or no deviceData. Form reset to default.');
    Object.assign(formData, getDefaultFormData());
    deviceUiFileList.value = [];
    isEdit.value = false;
  }
};

const extractFileNameFromUrl = (url) => {
  if (!url) return '';
  try {
    const path = new URL(url).pathname;
    return path.substring(path.lastIndexOf('/') + 1);
  } catch (e) {
    const parts = url.split('/');
    return parts.pop() || parts.pop();
  }
};

watch(() => props.visible, (newVal) => {
  console.log(`--- CREATE DEVICE DIALOG: props.visible WATCH new: ${newVal}, old internalVisible: ${dialogVisible.value}`);
  dialogVisible.value = newVal;
  if (newVal) { 
    resetFormAndPopulateData();
  } else { 
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }
});
  
watch(() => formData.imei1, () => {
  formRef.value?.clearValidate('imei2');
  formRef.value?.validateField('imei1').catch(() => {});
  if (formData.imei1 && formData.imei2 && formData.imei1 !== formData.imei2) {
    formRef.value?.validateField('imei2').catch(() => {});
  }
})

watch(() => formData.imei2, () => {
  formRef.value?.clearValidate('imei1');
  formRef.value?.validateField('imei2').catch(() => {});
  if (formData.imei1 && formData.imei2 && formData.imei1 !== formData.imei2) {
    formRef.value?.validateField('imei1').catch(() => {});
  }
})

watch(() => props.deviceData, (newData) => {
  console.log('--- CREATE DEVICE DIALOG: props.deviceData WATCH new:', newData !== null, 'old:', props.deviceData !== null);
  if (dialogVisible.value) { 
    console.log('[Watch props.deviceData] Changed, dialog visible. New data:', JSON.parse(JSON.stringify(newData)));
    resetFormAndPopulateData();
  }
}, { deep: true });


onMounted(async () => {
  console.log('--- CREATE DEVICE DIALOG: onMounted called ---');
  console.log('--- CREATE DEVICE DIALOG: onMounted props.visible:', props.visible);
  console.log('--- CREATE DEVICE DIALOG: onMounted props.deviceData:', JSON.parse(JSON.stringify(props.deviceData || {})));
  
  try {
    const brandResponse = await getDictFields({ module: 'DEVICE', fieldCode: 'brand', enabled: true });
    if (brandResponse.data && brandResponse.data.records && brandResponse.data.records.length > 0) {
      brandOptions.value = brandResponse.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch brand options:", error);
  }

  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'color', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      colorOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch color options:", error);
  }

  if (props.visible && props.deviceData) {
    console.log('--- CREATE DEVICE DIALOG: onMounted calling resetFormAndPopulateData ---');
    resetFormAndPopulateData();
  }
});

const translateBackendMessage = (message) => {
  if (typeof message !== 'string') {
    return message;
  }
  const messageMap = {
    '该序列号已存在，请插入其他序列号': 'device.error.snExists',
    '该IMEI1已存在，请输入其他IMEI1': 'device.error.imei1Exists',
    '该IMEI2已存在，请输入其他IMEI2': 'device.error.imei2Exists',
    '操作失败': 'common.operationFailed'
  };
  const key = messageMap[message.trim()];
  return key ? t(key) : message;
};

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const isValid = await formRef.value.validate();

    if (!isValid) {
      console.log('表单校验未通过，阻止提交。');
      loading.value = false;
      return;
    }
    
    loading.value = true;
    
    const submitData = { 
      ...formData,
    }
    
    const deviceId = props.deviceData ? props.deviceData.id : null
    
    let res
    if (deviceId) {
      res = await updateDevice(deviceId, submitData)
    } else {
      res = await createDevice(submitData)
    }
    
    if (res.code === 1000) {
      ElMessage.success(deviceId ? t('dialog.success.edit') : t('dialog.success.create'))
      if (!deviceId && res.data && res.data.id) {
        emit('success', { ...submitData, id: res.data.id }); 
      } else if (!deviceId) {
        emit('success', submitData); 
      } else {
        emit('success', { ...props.deviceData, ...submitData, id: deviceId });
      }
      handleClose()
    } else {
      ElMessage.error(translateBackendMessage(res.message) || (deviceId ? t('dialog.error.edit') : t('dialog.error.create')))
    }
  } catch (error) {
    // This will catch validation errors (if thrown) and any other unexpected errors
    console.error('An error occurred during form submission:', error)
    // The interceptor already handles API error messages. 
    // This catch is for other errors, like validation.
    // So we don't need a generic error message here.
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  console.log('--- CREATE DEVICE DIALOG: internal close triggered --- emitting update:visible false');
  emit('update:visible', false)
}

const previewDeviceFileUrl = ref('');
const previewDeviceImageUrl = ref('');
const previewDeviceDialogVisible = ref(false);

const handleDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name || 'download';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } else {
    ElMessage.error(t('common.fileDownloadFailed'));
  }
};

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileType = (fileName) => {
  if (!fileName) return '';
  const extension = fileName.split('.').pop();
  return extension ? extension.toUpperCase() : '';
};

const handleCostAmountInput = (value) => {
  if (value === null || value === undefined) {
    formData.costAmount = '';
    return;
  }
  // 只保留数字
  const cleanValue = value.toString().replace(/\D/g, '');
  formData.costAmount = cleanValue;
}

const isImage = (file) => {
  const fileName = file.name || '';
  const fileUrl = file.url || '';
  const imageExtensions = ['jpeg', 'jpg', 'gif', 'png', 'webp', 'bmp'];
  const extension = (fileName.split('.').pop() || '').toLowerCase();
  return imageExtensions.includes(extension);
};

const isPreviewable = (file) => {
  const fileName = file.name || '';
  const previewableExtensions = ['jpeg', 'jpg', 'gif', 'png', 'webp', 'bmp', 'pdf'];
  const extension = (fileName.split('.').pop() || '').toLowerCase();
  return previewableExtensions.includes(extension);
};

const handleDevicePreview = (file) => {
  if (file.url) {
    window.open(file.url, '_blank');
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 8px;
  
  .el-dialog__header {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #dcdfe6;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 24px 0;
  }
  
  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #dcdfe6;
    display: flex;
    justify-content: flex-end;
  }
}

.device-form {
  padding: 0 40px;

  .el-row {
    margin-bottom: 16px;
  }

  .info-tip {
    font-size: 13px;
    color: #909399;
    margin: -8px 0 20px 160px;
    line-height: 1.4;
    padding-top: 4px;
  }

  :deep(.el-form-item) {
    margin-bottom: 22px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      padding-right: 20px;
    }

    .el-form-item__content {
      .el-input,
      .el-select {
        width: 100%;
      }

      .el-input__wrapper,
      .el-select__wrapper {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        height: 40px;
        line-height: 40px;
        
        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }

      .el-textarea__inner {
        min-height: 80px !important;
      }
    }
  }

  .upload-container {
    .upload-area {
      width: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      
      &:hover {
        border-color: var(--el-color-primary);
      }
      
      .el-icon--upload {
        font-size: 32px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .el-upload__text {
        color: #606266;
        
        em {
          color: var(--el-color-primary);
          font-style: normal;
        }
      }
    }

    .el-upload__tip {
      color: #909399;
      font-size: 13px;
      line-height: 1.4;
      margin-top: 8px;
    }
  }
}

.form-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  
  .el-button {
    min-width: 80px;
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 4px;
    
    &.el-button--default {
      border-color: #dcdfe6;
      color: #606266;
    }
    
    &.el-button--primary {
      background-color: #409EFF;
      border-color: #409EFF;
      color: #ffffff;
    }
    
    & + .el-button {
      margin-left: 12px;
    }
  }
}

.auto-generate-label {
  color: #999;
  font-weight: normal;
  font-size: 12px;
}

.el-form-item__description {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 4px;
}

.device-info-item .el-form-item__label {
  white-space: nowrap;
}
.device-info-item > .el-form-item__label {
  width: auto !important;
  white-space: nowrap;
}

.attachment-section {
  width: 100%;
  .upload-area {
    margin-bottom: 16px;
  }
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  }
}

.attachment-thumbnail {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  margin-right: 16px;
  border-radius: 6px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .thumbnail-image {
    width: 100%;
    height: 100%;
  }

  .thumbnail-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #909399;
    text-align: center;
    .el-icon {
      font-size: 24px;
    }
    .thumbnail-ext {
      font-size: 12px;
      font-weight: bold;
      margin-top: 4px;
    }
  }

  .image-error, .loading {
    font-size: 12px;
    span {
      margin-top: 4px;
    }
  }
}

.attachment-info {
  flex-grow: 1;
  overflow: hidden;
  .attachment-name {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 6px;
  }
  .attachment-details {
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.attachment-actions {
  flex-shrink: 0;
  margin-left: 16px;
  display: flex;
  gap: 8px;

  .el-button {
    font-size: 16px;
    padding: 6px;
    height: auto;
  }
}

.compact-label-item .el-form-item__content {
  margin-left: -70px;
}

.top-input-row .el-form-item {
  display: flex;
}
.top-input-row .el-form-item__label {
  flex-shrink: 0;
  width: auto !important;
  margin-right: 10px;
}
.top-input-row .el-form-item__content {
  flex-grow: 1;
  margin-left: 0 !important;
}

.long-label-item.el-form-item {
  display: flex;
}
.long-label-item .el-form-item__label {
  flex-shrink: 0;
  white-space: nowrap;
  width: auto !important;
  margin-right: 10px;
}
.long-label-item .el-form-item__content {
  flex-grow: 1;
  margin-left: 0 !important;
}

.unbreakable-label-row {
  display: flex;
  align-items: flex-start; /* Align to the top */
}

.unbreakable-label-row :deep(.el-form-item__label) {
  width: auto;
  flex-shrink: 0; /* Prevent label from shrinking */
  margin-right: 0px; /* Standard Element Plus spacing */
  white-space: nowrap; /* Prevent wrapping */
}

.unbreakable-label-row :deep(.el-form-item__content) {
  flex-grow: 1;
  margin-left: 0 !important;
}
</style>

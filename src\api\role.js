import request from '@/utils/request'

const API_PREFIX = '/cloud-service/role'

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 当前页码
 * @param {number} params.size - 每页数量
 * @param {number} params.enterpriseId - 企业ID
 * @param {string} [params.roleName] - 角色名称(可选)
 * @returns {Promise<Object>} 角色列表
 */
export function getRoleList(params) {
  console.log('Role API调用，参数:', params);
  return request({
    url: `${API_PREFIX}/List`,
    method: 'get',
    params
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @returns {Promise<Object>} 创建结果
 */
export function createRole(data) {
  return request({
    url: `${API_PREFIX}/add`,
    method: 'post',
    data
  })
}

/**
 * 更新角色信息
 * @param {Object} data - 角色数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateRole(data) {
  return request({
    url: `${API_PREFIX}/edit`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {string|number} id - 角色ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteRole(id) {
  console.log('删除角色的ID:', id)
  return request({
    url: `${API_PREFIX}/delete`,
    method: 'delete',
    params: { roleId: id }
  })
}

/**
 * 获取单个角色详情
 * @param {string} id - 角色ID
 * @returns {Promise<Object>} 角色详情
 */
export function getRoleDetail(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'get'
  })
}

/**
 * 获取所有权限列表
 * @param {number} enterpriseId - 企业ID
 * @returns {Promise<Object>} 权限列表
 */
export function getPermissionList(enterpriseId) {
  return request({
    url: '/cloud-service/permission/list',
    method: 'get',
    params: { enterpriseId }
  })
}

/**
 * 获取角色的权限
 * @param {string|number} roleId - 角色ID
 * @returns {Promise<Object>} 角色权限列表
 */
export function getRolePermissions(roleId) {
  return request({
    url: `${API_PREFIX}/permissions/${roleId}`,
    method: 'get'
  })
}

/**
 * 更新角色权限
 * @param {Object} data - 角色权限数据
 * @param {string|number} data.roleId - 角色ID
 * @param {Array<string|number>} data.permissionIds - 权限ID列表
 * @returns {Promise<Object>} 更新结果
 */
export function updateRolePermissions(data) {
  return request({
    url: `${API_PREFIX}/updatePermissions`,
    method: 'post',
    data
  })
} 
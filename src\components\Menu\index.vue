<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="false"
    :collapse-transition="false"
    router
    class="menu-container"
  >
    <el-menu-item index="/home">
      <el-icon><HomeFilled /></el-icon>
      <template #title>{{ t('menus.dashboard') }}</template>
    </el-menu-item>

    <el-sub-menu index="/device" popper-class="menu-popper">
      <template #title>
        <el-icon><Monitor /></el-icon>
        <span>{{ t('menus.device') }}</span>
      </template>
      <el-menu-item index="/device/list">
        <el-icon><List /></el-icon>
        {{ t('menus.deviceList') }}
      </el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="/customer" popper-class="menu-popper">
      <template #title>
        <el-icon><User /></el-icon>
        <span>{{ t('menus.customer') }}</span>
      </template>
      <el-menu-item index="/customer/list">
        <el-icon><List /></el-icon>
        {{ t('menus.customerList') }}
      </el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { HomeFilled, Monitor, User, List } from '@element-plus/icons-vue'

const { t } = useI18n()
const route = useRoute()

const isCollapse = ref(false)

const activeMenu = computed(() => {
  return route.path
})
</script>

<style lang="scss" scoped>
.menu-container {
  height: 100%;
  border-right: none;

  :deep(.el-menu-item) {
    &.is-active {
      background-color: var(--el-menu-hover-bg-color);
    }

    &:hover {
      background-color: var(--el-menu-hover-bg-color);
    }
  }

  :deep(.el-sub-menu__title) {
    &:hover {
      background-color: var(--el-menu-hover-bg-color);
    }
  }

  :deep(.el-sub-menu) {
    .el-sub-menu__icon-arrow {
      right: 20px;
      left: auto;
    }
  }

  .el-icon {
    margin-right: 12px;
    width: 1em;
    height: 1em;
  }
}

:deep(.el-menu--popup) {
  min-width: 200px;
}
</style> 
import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';
import './styles/index.scss';
import './router/permission';
// import 'tailwindcss/tailwind.css';
import i18n from './locales';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import icons from './icons'; // 导入图标注册

// 配置 dayjs 及其插件
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import updateLocale from 'dayjs/plugin/updateLocale';
import relativeTime from 'dayjs/plugin/relativeTime';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import isBetween from 'dayjs/plugin/isBetween';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import arraySupport from 'dayjs/plugin/arraySupport';

// 加载所有需要的插件
dayjs.extend(duration);
dayjs.extend(updateLocale);
dayjs.extend(relativeTime);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(isBetween);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(quarterOfYear);
dayjs.extend(arraySupport);
dayjs.locale('zh-cn');

// 将 dayjs 设置到全局，让 Element Plus 可以使用同一个实例
window.dayjs = dayjs;

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(i18n);
app.use(icons); // 注册图标

// 将 dayjs 注入到 app 中以便在组件中使用
app.config.globalProperties.$dayjs = dayjs;

// 添加全局调试函数，确保i18n正常工作
app.config.globalProperties.$i18nDebug = function(key) {
  const value = i18n.global.t(key);
  console.log(`[i18n debug] ${key} => ${value}`);
  return value;
};

app.mount('#app');
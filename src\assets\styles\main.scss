/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global variables */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #42b983;
  --accent-color: #3498db;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --success-color: #2ecc71;
  --text-color: #333;
  --bg-color: #f8f9fa;
}

/* Global styles */
html, body {
  font-family: 'Inter', sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
  line-height: 1.6;
  height: 100%;
  overflow: hidden;
}

/* Custom utility classes */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md text-white font-medium transition-all duration-200;
  }
  
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700;
  }
  
  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700;
  }
  
  .btn-success {
    @apply bg-green-600 hover:bg-green-700;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700;
  }
}

/* Element Plus overrides */
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: color-mix(in srgb, var(--primary-color) 85%, white);
  border-color: color-mix(in srgb, var(--primary-color) 85%, white);
}

.el-table {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f9fafb;
}

/* Animation classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
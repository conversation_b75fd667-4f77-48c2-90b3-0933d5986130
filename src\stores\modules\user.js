import { defineStore } from 'pinia';
import { storageLocal } from '@pureadmin/utils';
import { getToken, setToken, removeToken, parseJwt, saveUserInfo } from '@/utils/auth';
import { login, getUserInfo, getCaptcha } from '@/api/user';
import { getEnterpriseInfo } from '@/api/enterprise';
import { encrypt, encryptFields } from '@/utils/encrypt'
import { stubTrue } from 'lodash-es';

// Mock开关 - 设为true时使用mock数据，false时使用真实API
const USE_MOCK_USER_INFO = false;

export const useUserStore = defineStore({
  id: 'pure-user',
  state: () => ({
    token: getToken(),
    userInfo: storageLocal().getItem('userInfo') || {},
    enterpriseInfo: null,
    roles: [],
    username: '',
    avatar: ''
  }),
  actions: {
    // 获取验证码
    getCaptcha() {
      return new Promise((resolve, reject) => {
        getCaptcha()
          .then(response => {
            // 返回完整响应，包含响应头
            resolve(response);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 登录
    login(userInfo) {
      const { username, password, code, uuid } = userInfo;
      return new Promise((resolve, reject) => {
        login({ username: username.trim(), password: encrypt(password), code, uuid })
          .then(async response => {
            const { data } = response;
            this.token = data.token;
            setToken(data.token);
            
            // 解析token并保存用户信息
            try {
              const decodedToken = parseJwt(data.token);
              console.log('解析token成功:', decodedToken);
              
              // 保存解析出的用户信息到localStorage
              if (decodedToken) {
                const userInfo = {
                  id: decodedToken.id || decodedToken.userId || '',
                  username: decodedToken.username || decodedToken.sub || '',
                  roles: decodedToken.roles || [],
                  enterpriseId: decodedToken.enterpriseId || '',
                  permissions: decodedToken.permissions || [],
                  exp: decodedToken.exp
                };
                
                // 保存到Pinia store
                this.userInfo = userInfo;
                
                // 保存到localStorage
                saveUserInfo(userInfo);
                console.log('用户信息已保存到localStorage');

                // 获取企业信息
                try {
                  const enterpriseRes = await getEnterpriseInfo();
                  if (enterpriseRes.code === 1000) {
                    this.enterpriseInfo = enterpriseRes.data;
                    console.log('企业信息获取成功:', enterpriseRes.data);
                  } else {
                    console.error('获取企业信息失败:', enterpriseRes.message);
                  }
                } catch (error) {
                  console.error('获取企业信息出错:', error);
                }
              }
            } catch (error) {
              console.error('处理token信息失败:', error);
            }
            
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    getUserInfo() {
      return new Promise((resolve, reject) => {
        // 使用真实API
        console.log('[UserStore] 使用真实API获取用户信息');
        getUserInfo({
          productType: 1
        })
          .then(response => {
            const { data } = response;
            
            if (!data) {
              reject('验证失败，请重新登录');
              return;
            }
            
            // 如果返回的数据结构不正确，使用 mock 数据
            if (!data.userInfo || !data.perms) {
              console.warn('返回数据结构不正确，使用 mock 数据');
              data.userInfo = {
                userId: 1,
                username: 'admin',
                email: '<EMAIL>',
                mobile: '13800138000'
              };
              data.perms = ['sys:user:list', 'sys:user:add', 'sys:user:edit', 'sys:user:delete'];
            }
            
            // 从权限列表生成角色
            const roles = data.perms ? ['admin'] : ['user'];
            const { name, avatar } = data.userInfo || {};
            
            this.roles = roles;
            this.username = name || data.userInfo?.username;
            this.avatar = avatar;
            
            // 合并从token解析的信息和API返回的信息
            const mergedInfo = { 
              ...this.userInfo, 
              ...data.userInfo,
              roles,
              permissions: data.perms || []
            };
            this.userInfo = mergedInfo;
            
            // 缓存用户信息
            saveUserInfo(mergedInfo);
            resolve({ ...data, roles });
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 登出
    logout() {
      return new Promise((resolve, reject) => {
        this.resetUserState();
        resolve();
      });
    },

    // 重置令牌
    resetToken() {
      return new Promise(resolve => {
        this.resetUserState();
        resolve();
      });
    },

    // 重置用户状态
    resetUserState() {
      this.token = '';
      this.roles = [];
      this.username = '';
      this.avatar = '';
      this.userInfo = {};
      this.enterpriseInfo = null;
      removeToken();
      localStorage.removeItem('userInfo');
    },
    
    // 获取当前mock状态（调试用）
    getMockStatus() {
      return USE_MOCK_USER_INFO;
    }
  }
}); 
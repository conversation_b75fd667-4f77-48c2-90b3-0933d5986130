import request from '@/utils/request'

/**
 * 上传文件
 * @param {File} file - 要上传的文件
 * @param {string} directory - 上传目录
 * @returns {Promise<string>} - 返回文件URL
 */
export function uploadFile(file, directory = 'test') {
  console.log('调用上传文件API:', { fileName: file.name, directory })
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/crm-service/api/oss/upload',
    method: 'post',
    params: { directory },
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

/**
 * 下载OSS文件
 * @param {string} fileUrl - OSS文件的完整URL
 * @returns {Promise<{blob: Blob, filename: string}>} - 包含文件Blob和文件名的Promise
 */
export async function downloadOssFile(fileUrl) {
  const downloadApiUrl = `/crm-service/api/oss/download?fileUrl=${encodeURIComponent(fileUrl)}`;

  try {
    // 使用 request 工具来发送请求，request 工具会自动带上 token
    const response = await request({
      url: downloadApiUrl,
      method: 'get',
      responseType: 'blob' // 告诉 axios 返回二进制数据
    });

    // response.data 就是 Blob 对象
    const blob = response.data;

    // 尝试从响应头中获取文件名
    const contentDisposition = response.headers['content-disposition']; // axios 的 headers 是小写
    let filename = 'downloaded-file';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename\*?=['"]?([^'"]*?)['"]?(?:;|$)/);
      if (filenameMatch && filenameMatch[1]) {
        try {
          filename = decodeURIComponent(filenameMatch[1].replace(/^UTF-8''/, ''));
        } catch (e) {
          filename = decodeURIComponent(filenameMatch[1]);
        }
      }
    } else {
      const urlParts = new URL(fileUrl);
      filename = urlParts.pathname.substring(urlParts.pathname.lastIndexOf('/') + 1);
    }
    
    return { blob, filename };
  } catch (error) {
    console.error('下载OSS文件失败:', error);
    throw error; // 重新抛出错误以便调用方处理
  }
} 
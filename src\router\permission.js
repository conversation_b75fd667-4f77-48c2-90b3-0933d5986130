import router from '@/router';
import { ElMessage } from 'element-plus';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import { useUserStore } from '@/stores/modules/user';
import { usePermissionStore } from '@/stores/modules/permission';

NProgress.configure({ showSpinner: false });

const whiteList = ['/login']; // 白名单

router.beforeEach(async (to, from, next) => {
    NProgress.start();

    const userStore = useUserStore();
    // const permissionStore = usePermissionStore();

    // 判断是否有token
    const hasToken = getToken();

    if (hasToken) {
        if (to.path === '/login') {
            // 已登录，重定向到首页
            next({ path: '/' });
            NProgress.done();
        } else {
            // 判断是否已获取用户信息
            const hasRoles = userStore.roles && userStore.roles.length > 0;

            if (hasRoles) {
                next();
            } else {
                try {
                    // 获取用户信息
                    const { roles } = await userStore.getUserInfo();

                    // 根据角色生成可访问路由
                    // const accessRoutes = await permissionStore.generateRoutes(roles);

                    // 动态添加路由
                    // accessRoutes.forEach(route => {
                    //     router.addRoute(route);
                    // });

                    // 确保路由完全加载
                    next({ ...to, replace: true });
                } catch (error) {
                    // 移除token并重定向到登录页
                    await userStore.resetToken();
                    ElMessage.error(error || '发生错误，请重新登录');
                    next(`/login?redirect=${to.path}`);
                    NProgress.done();
                }
            }
        }
    } else {
        // 没有token
        if (whiteList.includes(to.path)) {
            // 在白名单中，直接进入
            next();
        } else {
            // 没有权限，重定向到登录页
            next(`/login?redirect=${to.path}`);
            NProgress.done();
        }
    }
});

router.afterEach(() => {
    NProgress.done();
});
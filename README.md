# 订单管理系统

这是一个全功能的订单管理系统，专为设备租赁和分期付款业务设计，可帮助企业高效管理订单、客户和设备信息。

## 功能特性

- 📱 **设备管理**：管理手机、平板等设备的库存、状态和信息
- 👤 **客户管理**：客户档案管理，包含联系信息、证件信息等
- 📋 **订单管理**：支持租赁和分期两种订单模式
- 💰 **付款管理**：灵活的付款计划，支持多种付款方式
- 📊 **统计报表**：多维度数据统计和可视化图表
- 🔔 **通知提醒**：自动发送付款提醒、逾期提醒等
- 🌐 **国际化**：支持多语言切换
- 🚀 **响应式设计**：适配桌面端和移动端

## 多语言字段选项功能

### 功能说明
系统支持多语言字段选项，用户输入的选项会自动转换为包含中文和英文的多语言格式。

### 数据格式
字段选项支持以下格式：
- **简单文本格式**：`"设备在途,暂缓收款,审核中"`
- **多语言对象格式**：
```json
[
  {
    "code": "DEVICE_IN_TRANSIT",
    "zh_CN": "设备在途",
    "en_US": "Device in Transit"
  },
  {
    "code": "PAYMENT_SUSPENDED",
    "zh_CN": "暂缓收款",
    "en_US": "Payment Suspended"
  }
]
```

### Code 生成规则
- **已有选项**：使用接口返回的 `value` 作为 `code`
- **新增选项**：根据中文文本生成，规则为：英文大写，多文字用下划线拼接
  - 例如：`"设备在途"` → `"DEVICE_IN_TRANSIT"`
  - 例如：`"暂缓收款"` → `"PAYMENT_SUSPENDED"`

### 使用方式
1. **新增字段时**：用户输入中文选项，系统自动翻译为英文并保存为多语言格式
2. **编辑字段时**：支持编辑多语言选项，根据当前语言显示对应文本
3. **显示时**：根据用户当前语言设置显示对应的文本

### 翻译服务
系统使用多个免费的翻译API作为备用方案：
- LibreTranslate（主要）
- Google Translate（备用）
- 如果翻译失败，会使用原文作为英文翻译

所有翻译服务都是免费的，无需API密钥。

## 项目结构

```
src/
├── api/            # API接口封装
├── assets/         # 静态资源文件
├── components/     # 通用组件
├── config/         # 全局配置
├── layout/         # 布局组件
├── locales/        # 国际化语言文件
├── router/         # 路由配置
├── stores/         # 状态管理
├── styles/         # 全局样式
├── utils/          # 工具函数
│   └── translate.js # 翻译工具函数
└── views/          # 页面视图
```

## 订单功能模块

| 模块名称 | 状态 | 描述 |
|---------|------|------|
| 订单列表 | ✅ 已完成 | 展示所有订单，支持多条件筛选、排序和分页 |
| 订单详情 | ✅ 已完成 | 查看订单的详细信息，包括状态、付款计划等 |
| 创建订单 | ✅ 已完成 | 支持创建租赁和分期两种类型的订单 |
| 状态更新 | ✅ 已完成 | 更新订单状态，支持标记坏账等操作 |
| 付款管理 | ✅ 已完成 | 记录付款信息，支持多种付款方式 |
| 附件管理 | ✅ 已完成 | 上传、下载和管理订单相关文件 |
| 批量操作 | ⚠️ 开发中 | 支持批量更新状态、导出等操作 |
| 数据导出 | ⚠️ 开发中 | 支持导出Excel和CSV格式数据 |

## 安装说明

### 系统要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 pnpm >= 7.0.0

### 安装步骤

1. 克隆代码仓库
   ```bash
   git clone <repository-url>
   cd crm-web
   ```

2. 安装依赖
   ```bash
   npm install
   # 或
   pnpm install
   ```

3. 启动开发服务器
   ```bash
   npm run dev
   # 或
   pnpm run dev
   ```

4. 编译生产版本
   ```bash
   npm run build
   # 或
   pnpm run build
   ```

## 技术栈

- **前端框架**：Vue 3 + Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **国际化**：Vue I18n
- **HTTP客户端**：Axios
- **样式预处理器**：SCSS
- **工具类**：TailwindCSS

## 开发进度

- [x] 完成基础框架搭建
- [x] 实现国际化配置
- [x] 完成设备管理模块
- [x] 完成客户管理模块
- [x] 完成订单列表功能
- [x] 完成订单详情功能
- [x] 完成订单创建功能
- [x] 完成订单状态更新功能
- [x] 完成订单付款管理功能
- [x] 完成订单附件管理功能
- [x] 完成多语言字段选项功能
- [ ] 完成批量操作功能
- [ ] 完成数据导出功能
- [ ] 完成权限控制系统
- [ ] 完成通知提醒功能
- [ ] 完成统计报表功能

## 下一步计划

1. 完善批量操作功能，支持批量更新状态和导出
2. 实现更高级的数据统计和图表可视化
3. 增加权限控制系统
4. 集成消息通知系统
5. 优化移动端适配

# nginx 新增配置
location /cos-proxy/ {
   proxy_pass https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com/;

   # 重写请求头，让目标服务器能正确处理
   proxy_set_header Host mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com;
   proxy_set_header Referer ""; # 有些资源服务器会检查Referer，置空可以绕过
   proxy_set_header Origin "";
}
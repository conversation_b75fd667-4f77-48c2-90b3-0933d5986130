import axios from 'axios';
import { ElMessage } from 'element-plus';
import { getToken, removeToken } from '@/utils/auth';
import { isSuccess } from '@/utils/auth';
import router from '@/router';
import { getLanguage } from '@/locales'; // 导入getLanguage函数

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/', // 配置API基础URL
  timeout: 30000
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('[RequestUtil] Interceptor Request Config:', JSON.parse(JSON.stringify(config)));
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['token'] = `Bearer ${token}`;
    }
    // 设置Accept-Language请求头
    const lang = getLanguage();
    if (lang) {
      config.headers['Accept-Language'] = lang;
    }
    return config;
  },
  error => {
    console.error('请求错误：', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果请求配置中指定了responseType为blob，则直接返回response，不做任何处理
    if (response.config.responseType === 'blob') {
      return response;
    }

    const res = response.data;
    
    // 处理特定业务错误，例如400错误带errorField
    if (res.code === 400 && res.data && res.data.errorField) {
      // 不再显示全局ElMessage，交由业务组件处理字段错误
      // 也不再reject，直接返回res让业务组件的try-catch捕获并处理
      return res;
    }

    // 处理token过期的情况
    if (res.code === 10012) {
      ElMessage.error('登录已过期，请重新登录');
      // 清除token
      removeToken();
      // 跳转到登录页
      router.push('/login');
      return Promise.reject(new Error('登录已过期，请重新登录'));
    }

    if (!isSuccess(res.code)) {
      ElMessage.error(res.message || '请求失败');
      return Promise.reject(new Error(res.message || '请求失败'));
    }
    return res;
  },
  error => {
    console.error('响应错误：', error);
    
    // 处理HTTP 401未授权错误
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录');
      // 清除token
      removeToken();
      // 跳转到登录页，保留当前路径用于登录后跳转
      const currentPath = router.currentRoute.value.fullPath;
      if (currentPath !== '/login') {
        router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
      }
      return Promise.reject(new Error('登录已过期，请重新登录'));
    }
    
    // 处理其他HTTP错误状态码
    if (error.response) {
      switch (error.response.status) {
        case 400:
          // 如果是400错误且包含errorField，则不显示ElMessage，允许业务逻辑自行处理
          if (error.response.data?.errorField) {
            // 不执行任何ElMessage.error
          } else {
            // 其他400错误，显示通用提示
            ElMessage.error(error.response.data?.message || error.message || '请求失败');
          }
          break; // 处理完400后跳出
        case 403:
          ElMessage.error('没有权限访问');
          break;
        case 404:
          ElMessage.error('请求的资源不存在');
          break;
        case 500:
          ElMessage.error('服务器内部错误');
          break;
        default:
          ElMessage.error(error.response.data?.message || error.message || '请求失败');
      }
    } else if (error.request) {
      // 网络错误，不再显示全局 ElMessage，而是将错误 reject 出去，让业务代码捕获
      // ElMessage.error('网络连接失败，请检查网络');
    } else {
      ElMessage.error(error.message || '请求失败');
    }
    
    return Promise.reject(error);
  }
);

export default service;
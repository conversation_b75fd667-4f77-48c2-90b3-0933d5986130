<template>
  <el-card shadow="never" class="summary-card">
    <div class="card-content">
      <div class="card-title">{{ title }}</div>
      <div class="card-value" :style="{ color: valueColor }">
        {{ formatCurrency(value) }}
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  valueColor: {
    type: String,
    default: '#303133' // Default text color
  }
});

// Helper to format currency (can be moved to a utils file)
const formatCurrency = (val) => {
  if (val === null || val === undefined) return '-';
  return `¥${Number(val).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};
</script>

<style lang="scss" scoped>
.summary-card {
  :deep(.el-card__body) {
    padding: 16px;
    text-align: center;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .card-title {
    font-size: 14px;
    color: #606266;
  }

  .card-value {
    font-size: 22px;
    font-weight: 600;
  }
}
</style> 
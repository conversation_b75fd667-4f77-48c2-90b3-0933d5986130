<template>
  <div class="company-info-page">
    <div class="page-header">
      <div class="header-breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t('menus.pureHome') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menus.system') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('system.companyInfo') }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-title">{{ $t('system.companyInfo') }}</div>
    </div>

    <div class="page-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card shadow-card" v-loading="loading">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <span class="card-title">{{ $t('system.basicInfo') }}</span>
              <el-tag size="small" type="info" class="update-tag" v-if="companyInfo.lastUpdateTime">
                {{ $t('system.lastUpdated') }}: {{ formatDateTime(companyInfo.updateTime) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="info-container">
          <!-- Logo上传区域 -->
          <div class="logo-upload-section">
            <div class="logo-container">
              <el-tooltip :content="$t('system.viewLargerLogo')" placement="top" v-if="isValidImageUrl(companyInfo.logoUrl)">
                <div class="logo-preview" @click="previewLogo">
                  <img v-if="isValidImageUrl(companyInfo.logoUrl)" :src="companyInfo.logoUrl" alt="公司Logo" class="company-logo" />
                  <div v-else class="logo-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </div>
              </el-tooltip>
              <div v-else class="logo-preview">
                <div class="logo-placeholder">
                  <el-icon size="30"><Picture /></el-icon>
                  <div class="placeholder-text">{{ $t('system.uploadLogo') }}</div>
                </div>
              </div>
            </div>
            
            <div class="logo-actions">
              <div class="upload-button">
                <el-upload
                  :action="uploadAction"
                  :show-file-list="false"
                  :on-success="handleLogoSuccess"
                  :before-upload="beforeLogoUpload"
                  :headers="uploadHeaders"
                  :http-request="customUploadLogo"
                >
                  <el-button type="primary" :icon="Upload" size="small" class="upload-btn">
                    {{ companyInfo.logoUrl ? $t('system.changeLogo') : $t('system.uploadLogo') }}
                  </el-button>
                </el-upload>
              </div>
              
              <el-button 
                v-if="companyInfo.logoUrl" 
                type="danger" 
                :icon="Delete" 
                size="small" 
                @click="handleDeleteLogo"
                class="delete-btn"
              >
                {{ $t('system.deleteLogo') }}
              </el-button>
            </div>
            
            <div class="upload-tips">
              <div class="tip-item"><el-icon><InfoFilled /></el-icon> {{ $t('system.logoSizeRequirement') }}: 120x120px</div>
              <div class="tip-item"><el-icon><Document /></el-icon> {{ $t('system.logoFormatSupport') }}: PNG, JPG, JPEG</div>
              <div class="tip-item"><el-icon><Download /></el-icon> {{ $t('system.logoSizeLimit') }}: ≤ 5MB</div>
            </div>
          </div>

          <!-- 基本信息展示区域 -->
          <div class="info-section">
            <!-- 企业名称 -->
            <div class="info-item">
              <div class="info-label">{{ $t('system.companyName') }}</div>
              <div class="info-content">{{ companyInfo.name || '-' }}</div>
              <div class="info-action">
                <el-button type="primary" :icon="Edit" size="small" plain @click="handleEdit('name')">
                  {{ $t('common.edit') }}
                </el-button>
              </div>
            </div>

            <!-- 地址 -->
            <div class="info-item">
              <div class="info-label">{{ $t('system.address') }}</div>
              <div class="info-content">{{ companyInfo.address || '-' }}</div>
              <div class="info-action">
                <el-button type="primary" :icon="Edit" size="small" plain @click="handleEdit('address')">
                  {{ $t('common.edit') }}
                </el-button>
              </div>
            </div>

            <!-- 电话 -->
            <div class="info-item">
              <div class="info-label">{{ $t('system.phone') }}</div>
              <div class="info-content">{{ formatPhone(companyInfo.phone) || '-' }}</div>
              <div class="info-action">
                <el-button type="primary" :icon="Edit" size="small" plain @click="handleEdit('phone')">
                  {{ $t('common.edit') }}
                </el-button>
              </div>
            </div>

            <!-- 邮箱 -->
            <div class="info-item">
              <div class="info-label">{{ $t('system.email') }}</div>
              <div class="info-content">{{ companyInfo.email || '-' }}</div>
              <div class="info-action">
                <el-button type="primary" :icon="Edit" size="small" plain @click="handleEdit('email')">
                  {{ $t('common.edit') }}
                </el-button>
              </div>
            </div>

            <!-- 结算币种 -->
            <div class="info-item">
              <div class="info-label">{{ $t('system.settlementCurrency') }}</div>
              <div class="info-content">{{ formatCurrency(companyInfo.currency) || '-' }}</div>
              <div class="info-action">
                <el-button type="primary" :icon="Edit" size="small" plain @click="handleEdit('currency')">
                  {{ $t('common.edit') }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 社交媒体账号 -->
      <el-card class="info-card social-card shadow-card" v-loading="socialLoading">
        <template #header>
          <div class="card-header">
            <span class="card-title">{{ $t('system.socialMedia') }}</span>
            <el-button type="primary" :icon="Plus" size="small" @click="handleAddSocialAccount">
              {{ $t('system.addSocialAccount') }}
            </el-button>
          </div>
        </template>

        <div class="social-accounts-container">
          <el-empty 
            v-if="!companyInfo.socialMediaList || companyInfo.socialMediaList.length === 0"
            :description="$t('system.noSocialAccounts')"
            :image-size="100"
          />
          <div v-else class="social-accounts-list">
            <div v-for="(account, index) in companyInfo.socialMediaList" :key="index" class="social-account-item">
              <div class="social-account-icon" :class="'platform-' + account.platform">
                <el-icon v-if="account.platform === 'wechat'"><WechatLogo /></el-icon>
                <el-icon v-else-if="account.platform === 'linkedin'"><LinkedinLogo /></el-icon>
                <el-icon v-else-if="account.platform === 'facebook'"><FacebookLogo /></el-icon>
                <el-icon v-else-if="account.platform === 'twitter'"><TwitterLogo /></el-icon>
                <el-icon v-else><Link /></el-icon>
              </div>
              <div class="social-account-info">
                <div class="social-account-type">{{ getSocialPlatformName(account.platform) }}</div>
                <div class="social-account-name">
                  <a :href="account.url" target="_blank" rel="noopener noreferrer">
                    {{ account.name || account.url }}
                  </a>
                </div>
              </div>
              <div class="social-account-actions">
                <el-button 
                  type="danger" 
                  :icon="Delete"
                  size="small" 
                  circle
                  @click="handleDeleteSocialAccount(index)"
                >
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="getEditDialogTitle()"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="edit-form">
        <el-form 
          ref="editFormRef" 
          :model="editForm" 
          :rules="formRules"
          label-width="120px"
          label-position="left"
        >
          <el-form-item v-if="currentEditField === 'name'" :label="$t('system.companyName')" prop="value">
            <el-input v-model="editForm.value" maxlength="100" show-word-limit />
          </el-form-item>
          
          <el-form-item v-if="currentEditField === 'address'" :label="$t('system.address')" prop="value">
            <el-input v-model="editForm.value" type="textarea" :rows="3" maxlength="200" show-word-limit />
          </el-form-item>
          
          <el-form-item v-if="currentEditField === 'phone'" :label="$t('system.phone')" prop="value">
            <el-input v-model="editForm.value" />
          </el-form-item>
          
          <el-form-item v-if="currentEditField === 'email'" :label="$t('system.email')" prop="value">
            <el-input v-model="editForm.value" />
          </el-form-item>
          
          <el-form-item v-if="currentEditField === 'currency'" :label="$t('system.settlementCurrency')" prop="value">
            <el-select v-model="editForm.value" style="width: 100%;">
              <el-option :label="$t('system.currencyCNY')" value="CNY" />
              <el-option :label="$t('system.currencyUSD')" value="USD" />
              <el-option :label="$t('system.currencyEUR')" value="EUR" />
              <el-option :label="$t('system.currencyGBP')" value="GBP" />
            </el-select>
          </el-form-item>
          
          <template v-if="currentEditField === 'social'">
            <el-form-item :label="$t('system.platform')" prop="platform">
              <el-select v-model="editForm.platform" style="width: 100%;">
                <el-option :label="$t('system.wechat')" value="wechat" />
                <el-option :label="$t('system.linkedin')" value="linkedin" />
                <el-option :label="$t('system.facebook')" value="facebook" />
                <el-option :label="$t('system.twitter')" value="twitter" />
              </el-select>
            </el-form-item>
            
            <el-form-item :label="$t('system.accountName')" prop="name">
              <el-input v-model="editForm.name" />
            </el-form-item>
            
            <el-form-item :label="$t('system.accountUrl')" prop="url">
              <el-input v-model="editForm.url" :placeholder="$t('system.accountUrlPlaceholder')" />
            </el-form-item>
          </template>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEdit">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSaveEdit" :loading="saveLoading">{{ $t('common.save') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Logo预览对话框 -->
    <el-dialog
      v-model="logoPreviewVisible"
      :title="$t('system.logoPreview')"
      width="400px"
      center
    >
      <div class="logo-preview-container">
        <img v-if="companyInfo.logoUrl" :src="companyInfo.logoUrl" alt="公司Logo" class="logo-preview-image" />
      </div>
    </el-dialog>

    <!-- 删除社交媒体账号确认对话框 -->
    <el-dialog
      v-model="deleteSocialAccountDialogVisible"
      :title="$t('system.confirmDelete')"
      width="400px"
    >
      <div class="confirm-delete-content">
        {{ $t('system.confirmDeleteSocialAccount') }}
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteSocialAccountDialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmDeleteSocialAccount" :loading="deleteLoading">{{ $t('common.delete') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Edit, Upload, Plus, Delete, Picture, Link, InfoFilled, Document, Download } from '@element-plus/icons-vue';
import WechatLogo from '@/components/icons/WechatLogo.vue';
import LinkedinLogo from '@/components/icons/LinkedinLogo.vue';
import FacebookLogo from '@/components/icons/FacebookLogo.vue';
import TwitterLogo from '@/components/icons/TwitterLogo.vue';
import { getEnterpriseInfo, updateEnterpriseInfo, uploadLogo, getEnterpriseSocialMediaList, saveEnterpriseSocialMedia, deleteEnterpriseSocialMedia, batchUpdateSocialMedia, deleteLogo } from '@/api/enterprise';
import { getToken } from '@/utils/auth';

const { t } = useI18n();
const editFormRef = ref(null);

// 加载状态
const loading = ref(false);
const socialLoading = ref(false);

// 企业信息数据
const companyInfo = reactive({
  id: null,
  name: '',
  address: '',
  phone: '',
  email: '',
  logoUrl: '',
  currency: 'CNY',
  createTime: '',
  updateTime: '',
  lastUpdateTime: '',
  socialMediaList: []
});

// 上传相关
const uploadAction = '';  // 不使用action属性，改用自定义上传
const uploadHeaders = {
  Authorization: `Bearer ${getToken()}`
};

// 编辑相关
const editDialogVisible = ref(false);
const currentEditField = ref('');
const editForm = reactive({
  value: '',
  platform: 'wechat',
  name: '',
  url: ''
});
const saveLoading = ref(false);

// Logo预览相关
const logoPreviewVisible = ref(false);

// 删除社交媒体账号相关
const deleteSocialAccountDialogVisible = ref(false);
const currentSocialAccountIndex = ref(-1);
const deleteLoading = ref(false);

// 表单验证规则
const formRules = {
  value: [
    { required: true, message: t('system.fieldRequired'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (currentEditField.value === 'name') {
          if (!value) {
            callback(new Error(t('system.companyNameRequired')));
          } else if (value.length < 2 || value.length > 100) {
            callback(new Error(t('system.companyNameLengthError')));
          } else {
            callback();
          }
        } else if (currentEditField.value === 'address') {
          if (!value) {
            callback(new Error(t('system.addressRequired')));
          } else if (value.length < 5 || value.length > 200) {
            callback(new Error(t('system.addressLengthError')));
          } else {
            callback();
          }
        } else if (currentEditField.value === 'phone') {
          if (!value) {
            callback(new Error(t('system.phoneRequired')));
          } else {
            // 支持多种电话格式:
            // 1. 中国大陆手机号: 1xx-xxxx-xxxx 或 1xxxxxxxxxx 或 1xx xxxx xxxx
            // 2. 座机号码: xxx-xxxxxxxx 或 xxxx-xxxxxxx 或 区号(xxxx)xxxxxxx
            // 3. 国际格式: +xx-xxx-xxxxxxx 或 +xxxxxxxxxxx
            const mobileReg = /^1[3-9]\d{9}$/;                         // 1xxxxxxxxxx
            const mobileDashReg = /^1[3-9]\d{2}[-\s]\d{4}[-\s]\d{4}$/; // 1xx-xxxx-xxxx 或 1xx xxxx xxxx
            const telReg = /^\d{3,4}-\d{7,8}$/;                        // xxx-xxxxxxxx 或 xxxx-xxxxxxx
            const telBracketReg = /^\(\d{3,4}\)\d{7,8}$/;              // (xxx)xxxxxxxx 或 (xxxx)xxxxxxx
            const intlReg = /^\+\d{1,3}[-\s]\d{3,4}[-\s]\d{7,8}$/;     // +xx-xxx-xxxxxxx 或 +xx xxx xxxxxxx
            const intlSimpleReg = /^\+\d{6,18}$/;                       // +xxxxxxxxxxx
            
            if (!mobileReg.test(value) && !mobileDashReg.test(value) && 
                !telReg.test(value) && !intlReg.test(value) && 
                !telBracketReg.test(value) && !intlSimpleReg.test(value)) {
              callback(new Error(t('system.phoneFormatError')));
            } else {
              callback();
            }
          }
        } else if (currentEditField.value === 'email') {
          if (!value) {
            callback(new Error(t('system.emailRequired')));
          } else {
            // 增强邮箱验证，支持更多有效域名和子域名
            const emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+((\.[a-zA-Z0-9-]+)*\.([a-zA-Z0-9]{2,}))$/;
            if (!emailReg.test(value)) {
              callback(new Error(t('system.emailFormatError')));
            } else {
              callback();
            }
          }
        } else if (currentEditField.value === 'currency') {
          if (!value) {
            callback(new Error(t('system.currencyRequired')));
          } else {
            // 验证币种是否有效
            const validCurrencies = ['CNY', 'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'HKD'];
            if (!validCurrencies.includes(value)) {
              callback(new Error(t('system.invalidCurrency')));
            } else {
              callback();
            }
          }
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  platform: [
    { required: true, message: t('system.platformRequired'), trigger: 'change' }
  ],
  url: [
    { required: true, message: t('system.urlRequired'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error(t('system.urlRequired')));
        } else {
          // URL验证规则增强，支持内部链接和特殊协议
          try {
            // 处理特殊协议开头的URL
            const specialProtocols = ['weixin://', 'wechat://', 'linkedin://', 'fb://', 'twitter://'];
            const isSpecialProtocol = specialProtocols.some(protocol => value.startsWith(protocol));
            
            // 处理相对路径
            const isRelativePath = value.startsWith('/') && !value.startsWith('//');
            
            // 处理标准URL
            let isValidUrl = false;
            if (!isSpecialProtocol && !isRelativePath) {
              // 尝试使用URL构造函数验证
              try {
                // 对于不带协议的URL，先添加https://再验证
                const urlToCheck = value.match(/^(https?:\/\/|www\.|\/\/)/) ? value : `https://${value}`;
                new URL(urlToCheck);
                isValidUrl = true;
              } catch (e) {
                isValidUrl = false;
              }
            }
            
            if (isSpecialProtocol || isRelativePath || isValidUrl) {
              callback();
            } else {
              callback(new Error(t('system.invalidUrl')));
            }
          } catch (e) {
            callback(new Error(t('system.invalidUrl')));
          }
        }
      },
      trigger: 'blur'
    }
  ]
};

// 自定义Logo上传
const customUploadLogo = async (options) => {
  try {
    const res = await uploadLogo(options.file);
    console.log('上传Logo响应:', res);
    
    if (res.code === 1000) {
      // 直接处理成功逻辑，不再通过回调
      companyInfo.logoUrl = res.data;
      // 更新企业信息
      updateEnterpriseBasicInfo({
        id: companyInfo.id,
        logoUrl: res.data
      });
      ElMessage.success(t('system.uploadSuccess'));
      
      // 只通知el-upload组件上传成功，不传递响应数据
      options.onSuccess();
    } else {
      ElMessage.error(res.message || t('system.logoUploadFailed'));
      options.onError(new Error(res.message || '上传失败'));
    }
  } catch (error) {
    console.error('上传Logo失败:', error);
    ElMessage.error(t('system.logoUploadFailed'));
    options.onError(error);
  }
};

// Logo上传处理 - 这个函数保留但不再使用，以兼容可能的其他调用
const handleLogoSuccess = (res) => {
  console.log('handleLogoSuccess被调用，但已不再使用:', res);
  // 空函数，不执行任何操作，防止重复处理
};

// 检查图片URL是否有效
const isValidImageUrl = (url) => {
  return url && typeof url === 'string' && (
    url.startsWith('http://') || 
    url.startsWith('https://') || 
    url.startsWith('data:image/')
  );
};

const beforeLogoUpload = (file) => {
  const isImageType = ['image/jpeg', 'image/png'].includes(file.type);
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImageType) {
    ElMessage.error(t('system.logoTypeError'));
    return false;
  }
  
  if (!isLt5M) {
    ElMessage.error(t('system.logoSizeError'));
    return false;
  }
  
  return true;
};

const handleDeleteLogo = () => {
  ElMessageBox.confirm(
    t('system.deleteLogoConfirm'),
    t('system.confirmDelete'),
    {
      confirmButtonText: t('common.delete'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        loading.value = true;
        // 使用专门的删除Logo接口
        const res = await deleteLogo();
        if (res.code === 1000) {
          // 删除成功后，更新本地数据
          companyInfo.logoUrl = '';
          ElMessage.success(t('system.logoDeleteSuccess'));
        } else {
          ElMessage.error(res.message || t('system.logoDeleteFailed'));
        }
      } catch (error) {
        console.error('删除Logo失败:', error);
        ElMessage.error(t('system.logoDeleteFailed'));
      } finally {
        loading.value = false;
      }
    })
    .catch(() => {
      // 取消删除操作
    });
};

// 预览Logo
const previewLogo = () => {
  if (companyInfo.logoUrl) {
    logoPreviewVisible.value = true;
  }
};

// 编辑处理
const handleEdit = (field) => {
  currentEditField.value = field;
  
  switch (field) {
    case 'name':
      editForm.value = companyInfo.name;
      break;
    case 'address':
      editForm.value = companyInfo.address;
      break;
    case 'phone':
      editForm.value = companyInfo.phone;
      break;
    case 'email':
      editForm.value = companyInfo.email;
      break;
    case 'currency':
      editForm.value = companyInfo.currency;
      break;
  }
  
  editDialogVisible.value = true;
};

const handleAddSocialAccount = () => {
  currentEditField.value = 'social';
  editForm.platform = 'wechat';
  editForm.name = '';
  editForm.url = '';
  editDialogVisible.value = true;
};

const getEditDialogTitle = () => {
  const titleMap = {
    name: t('system.editCompanyName'),
    address: t('system.editAddress'),
    phone: t('system.editPhone'),
    email: t('system.editEmail'),
    currency: t('system.editCurrency'),
    social: t('system.addSocialAccount')
  };
  
  return titleMap[currentEditField.value] || t('common.edit');
};

const cancelEdit = () => {
  editDialogVisible.value = false;
  
  // 延迟重置表单，避免表单校验错误信息闪现
  setTimeout(() => {
    editFormRef.value?.resetFields();
  }, 300);
};

const handleSaveEdit = async () => {
  if (!editFormRef.value) return;
  
  try {
    await editFormRef.value.validate();
    saveLoading.value = true;
    
    if (currentEditField.value === 'social') {
      // 添加社交账号
      const socialMedia = {
        enterpriseId: companyInfo.id,
        platform: editForm.platform,
        url: editForm.url
      };
      
      if (editForm.name) {
        socialMedia.name = editForm.name;
      }
      
      try {
        const res = await saveEnterpriseSocialMedia(socialMedia);
        if (res.code === 1000) {
          // 添加成功后重新获取社交媒体列表
          await fetchSocialMediaList();
          ElMessage.success(t('system.socialAccountAddSuccess'));
        } else {
          ElMessage.error(res.message || t('system.socialAccountAddFailed'));
        }
      } catch (error) {
        console.error('添加社交媒体账号失败:', error);
        ElMessage.error(t('system.socialAccountAddFailed'));
      }
    } else {
      // 更新公司基本信息
      const updateData = { 
        id: companyInfo.id,
        [currentEditField.value]: editForm.value 
      };
      
      try {
        await updateEnterpriseBasicInfo(updateData);
        ElMessage.success(t('system.updateSuccess'));
      } catch (error) {
        console.error('更新企业信息失败:', error);
        ElMessage.error(t('system.updateFailed'));
      }
    }
    
    editDialogVisible.value = false;
    saveLoading.value = false;
    
    // 重置表单
    setTimeout(() => {
      editFormRef.value?.resetFields();
    }, 300);
  } catch (error) {
    saveLoading.value = false;
    console.error('表单验证失败:', error);
  }
};

// 更新企业基本信息
const updateEnterpriseBasicInfo = async (data) => {
  try {
    loading.value = true;
    // 只传递id和被修改的字段，而不是整个对象
    const updateData = { id: companyInfo.id };
    
    // 如果是通过handleSaveEdit调用(传入整个对象)，则从currentEditField获取字段名
    if (currentEditField.value && currentEditField.value !== 'social') {
      updateData[currentEditField.value] = data[currentEditField.value];
    } 
    // 如果是通过handleLogoSuccess或handleDeleteLogo调用(直接传入字段)，则使用传入的数据
    else if (Object.keys(data).length <= 2) { // 只有id和一个字段
      Object.assign(updateData, data);
    }
    
    console.log('更新企业信息，发送数据:', updateData);
    const res = await updateEnterpriseInfo(updateData);
    if (res.code === 1000) {
      // 更新成功后重新获取企业信息
      await fetchCompanyInfo();
    } else {
      ElMessage.error(res.message || t('system.updateFailed'));
    }
  } catch (error) {
    console.error('更新企业信息失败:', error);
    ElMessage.error(t('system.updateFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理删除社交媒体账号
const handleDeleteSocialAccount = (index) => {
  currentSocialAccountIndex.value = index;
  deleteSocialAccountDialogVisible.value = true;
};

const confirmDeleteSocialAccount = async () => {
  if (currentSocialAccountIndex.value < 0) return;
  
  deleteLoading.value = true;
  
  try {
    const socialAccount = companyInfo.socialMediaList[currentSocialAccountIndex.value];
    const res = await deleteEnterpriseSocialMedia(socialAccount.id);
    
    if (res.code === 1000) {
      // 删除成功后重新获取社交媒体列表
      await fetchSocialMediaList();
      ElMessage.success(t('system.socialAccountDeleteSuccess'));
    } else {
      ElMessage.error(res.message || t('system.socialAccountDeleteFailed'));
    }
  } catch (error) {
    console.error('删除社交媒体账号失败:', error);
    ElMessage.error(t('system.socialAccountDeleteFailed'));
  } finally {
    deleteLoading.value = false;
    deleteSocialAccountDialogVisible.value = false;
    currentSocialAccountIndex.value = -1;
  }
};

// 格式化币种显示
const formatCurrency = (currency) => {
  const currencyMap = {
    'CNY': '人民币 (CNY)',
    'USD': '美元 (USD)',
    'EUR': '欧元 (EUR)',
    'GBP': '英镑 (GBP)'
  };
  
  return currencyMap[currency] || currency;
};

// 格式化电话号码显示
const formatPhone = (phone) => {
  // 简单的手机号格式化: 138-0183-1675
  if (phone && phone.length === 11) {
    return `${phone.substring(0, 3)}-${phone.substring(3, 7)}-${phone.substring(7)}`;
  }
  return phone;
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '';
  
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 获取社交平台名称
const getSocialPlatformName = (type) => {
  const platformMap = {
    'wechat': '微信',
    'linkedin': 'LinkedIn',
    'facebook': 'Facebook',
    'twitter': 'Twitter'
  };
  
  return platformMap[type] || type;
};

// 获取企业信息
const fetchCompanyInfo = async () => {
  try {
    loading.value = true;
    
    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 15000);
    });
    
    // 调用API获取企业信息
    const apiPromise = getEnterpriseInfo();
    
    // 使用Promise.race实现超时处理
    const res = await Promise.race([apiPromise, timeoutPromise]);
    
    if (res && res.code === 1000 && res.data) {
      // 使用解构赋值更新企业信息
      const {
        id, name, address, phone, email, logoUrl, 
        currency = 'CNY', // 默认值
        createTime, updateTime
      } = res.data;
      
      // 更新响应式对象
      Object.assign(companyInfo, {
        id, name, address, phone, email, logoUrl,
        currency: currency || 'CNY',
        createTime,
        updateTime,
        lastUpdateTime: updateTime || createTime // 确保lastUpdateTime有值
      });
      
      // 确保必需的字段有值
      validateAndFormatData();
      
      // 同时获取社交媒体账号列表
      await fetchSocialMediaList();
    } else {
      handleApiError(res, '获取企业信息失败');
    }
  } catch (error) {
    handleError(error, '获取企业信息失败');
  } finally {
    loading.value = false;
  }
};

// 验证并格式化数据
const validateAndFormatData = () => {
  // 确保每个字段都有默认值
  if (!companyInfo.name) companyInfo.name = '';
  if (!companyInfo.address) companyInfo.address = '';
  if (!companyInfo.phone) companyInfo.phone = '';
  if (!companyInfo.email) companyInfo.email = '';
  if (!companyInfo.logoUrl) companyInfo.logoUrl = '';
  if (!companyInfo.currency) companyInfo.currency = 'CNY';
  
  // 检查logoUrl是否有效
  if (companyInfo.logoUrl && !isValidImageUrl(companyInfo.logoUrl)) {
    console.warn('Logo URL可能无效:', companyInfo.logoUrl);
  }
};

// 获取社交媒体账号列表
const fetchSocialMediaList = async () => {
  try {
    socialLoading.value = true;
    
    if (!companyInfo.id) {
      console.warn('企业ID为空，无法获取社交媒体账号');
      companyInfo.socialMediaList = [];
      return;
    }
    
    const res = await getEnterpriseSocialMediaList(companyInfo.id);
    
    if (res && res.code === 1000) {
      companyInfo.socialMediaList = Array.isArray(res.data) ? res.data : [];
      
      // 确保每个社交媒体账号都有有效的URL
      companyInfo.socialMediaList.forEach(item => {
        if (!item.url) {
          item.url = '#';
        }
        
        // 确保有显示名称
        if (!item.name) {
          item.name = item.platform || getSocialPlatformName(item.platform) || '社交账号';
        }
      });
    } else {
      handleApiError(res, '获取社交媒体账号失败');
      companyInfo.socialMediaList = [];
    }
  } catch (error) {
    handleError(error, '获取社交媒体账号失败');
    companyInfo.socialMediaList = [];
  } finally {
    socialLoading.value = false;
  }
};

// 通用API错误处理
const handleApiError = (response, defaultMessage) => {
  const message = response && response.message 
    ? response.message 
    : t('system.requestFailed');
    
  console.error(`${defaultMessage}:`, response);
  ElMessage.error(message);
  
  // 特定错误码处理
  if (response && response.code) {
    if (response.code === 4004) {
      console.warn('资源未找到，可能需要创建新记录');
    } else if (response.code === 5001) {
      console.warn('服务器内部错误，可能需要重试');
    }
  }
};

// 通用错误处理
const handleError = (error, context) => {
  console.error(`${context}:`, error);
  
  let errorMessage = t('system.requestFailed');
  
  // 根据错误类型提供更详细的错误信息
  if (error.message === '请求超时') {
    errorMessage = t('system.requestTimeout');
  } else if (error.message && error.message.includes('Network Error')) {
    errorMessage = t('system.networkError');
  } else if (error.response && error.response.status) {
    // HTTP状态码错误
    switch (error.response.status) {
      case 401:
        errorMessage = t('system.unauthorizedError');
        break;
      case 403:
        errorMessage = t('system.forbiddenError');
        break;
      case 404:
        errorMessage = t('system.notFoundError');
        break;
      case 500:
        errorMessage = t('system.serverError');
        break;
    }
  }
  
  ElMessage.error(errorMessage);
};

onMounted(() => {
  fetchCompanyInfo();
});
</script>

<style lang="scss" scoped>
.company-info-page {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    .header-title {
      font-size: 24px;
      font-weight: 600;
      margin-top: 15px;
      color: #303133;
    }
  }
  
  .shadow-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .card-title {
          font-weight: 600;
          font-size: 16px;
          color: #303133;
        }
        
        .update-tag {
          margin-left: 10px;
          font-size: 12px;
        }
      }
    }
  }
  
  .info-container {
    display: flex;
    flex-direction: column;
    
    @media (min-width: 768px) {
      flex-direction: row;
    }
  }
  
  .logo-upload-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    
    @media (min-width: 768px) {
      width: 260px;
      margin-right: 40px;
      margin-bottom: 0;
    }
    
    .logo-container {
      margin-bottom: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    
    .logo-preview {
      cursor: pointer;
      
      .company-logo, .logo-placeholder {
        width: 140px;
        height: 140px;
        border-radius: 8px;
        object-fit: cover;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e4e7ed;
        background-color: #f5f7fa;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 15px;
        transition: all 0.3s;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
        }
        
        .el-icon {
          font-size: 40px;
          color: #c0c4cc;
          margin-bottom: 8px;
        }
        
        .placeholder-text {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .logo-actions {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 15px;
      width: 100%;
      
      .upload-btn, .delete-btn {
        min-width: 120px;
      }
    }
    
    .upload-tips {
      width: 100%;
      font-size: 12px;
      color: #909399;
      background-color: #ecf5ff;
      padding: 10px;
      border-radius: 4px;
      
      .tip-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-icon {
          margin-right: 5px;
          color: #409eff;
        }
      }
    }
  }
  
  .info-section {
    flex: 1;
    
    .info-item {
      display: flex;
      padding: 16px 0;
      border-bottom: 1px solid #ebeef5;
      
      &:first-child {
        padding-top: 0;
      }
      
      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }
      
      .info-label {
        width: 180px;
        color: #606266;
        font-weight: 600;
        flex-shrink: 0;
      }
      
      .info-content {
        flex: 1;
        color: #303133;
        word-break: break-all;
      }
      
      .info-action {
        width: 100px;
        text-align: right;
        flex-shrink: 0;
        
        .el-button {
          min-width: 85px;
        }
      }
    }
  }
  
  .social-card {
    .social-accounts-container {
      min-height: 150px;
    }
    
    .social-accounts-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 16px;
      
      .social-account-item {
        display: flex;
        align-items: center;
        padding: 18px;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        
        &:hover {
          background-color: #ecf5ff;
          transform: translateY(-2px);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          
          .social-account-actions {
            opacity: 1;
          }
        }
        
        .social-account-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background-color: #409eff;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 12px;
          
          &.platform-wechat {
            background-color: #07C160;
          }
          
          &.platform-linkedin {
            background-color: #0A66C2;
          }
          
          &.platform-facebook {
            background-color: #1877F2;
          }
          
          &.platform-twitter {
            background-color: #1DA1F2;
          }
          
          .el-icon {
            font-size: 22px;
          }
        }
        
        .social-account-info {
          flex: 1;
          overflow: hidden;
          
          .social-account-type {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .social-account-name {
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            
            a {
              color: #409eff;
              text-decoration: none;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
        
        .social-account-actions {
          opacity: 0;
          transition: opacity 0.3s;
        }
      }
    }
  }
  
  .logo-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .logo-preview-image {
      max-width: 300px;
      max-height: 300px;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
  
  .confirm-delete-content {
    font-size: 16px;
    color: #606266;
    text-align: center;
    margin: 20px 0;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  
  @media (max-width: 992px) {
    .info-item {
      flex-direction: column;
      
      .info-label {
        width: 100% !important;
        margin-bottom: 8px;
      }
      
      .info-content {
        margin-bottom: 8px;
      }
      
      .info-action {
        width: 100% !important;
        text-align: left !important;
      }
    }
  }
}
</style> 
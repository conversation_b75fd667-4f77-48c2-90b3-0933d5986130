<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isBatch ? $t('order.statusUpdates.batchTitle') : $t('order.statusUpdates.title')"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
    class="order-status-update-dialog"
    @open="onDialogOpen"
  >
    <div class="status-update-content">
      <!-- 变更状态为 -->
      <div class="select-item">
        <div class="item-label">{{ $t('order.list.financialStatus') }}</div>
        <el-select v-model="form.newOrderStatus" :placeholder="$t('device.batchChangeStatus.targetStatusPlaceholder')" clearable class="status-select">
          <el-option
            v-for="item in computedFinancialStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <!-- 标记为坏账 -->
      <div class="select-item">
        <div class="item-label" v-if="isBatch">{{ $t('order.statusUpdates.markAsBadDebt') }}</div>
        <template v-if="!isBatch">
          <el-checkbox v-model="isMarkedAsBadDebtComputed">{{ $t('order.statusUpdates.markAsBadDebt') }}</el-checkbox>
          <div class="checkbox-description">
            {{ $t('order.statusUpdates.badDebtCheckboxDescription') }}
          </div>
        </template>
        <template v-else>
          <el-select v-model="form.markAsBadDebtAction" :placeholder="$t('common.pleaseSelect')" class="status-select">
            <el-option :label="$t('order.statusUpdates.noAction')" value="NO_ACTION" />
            <el-option :label="$t('order.statusUpdates.yes')" value="YES" />
            <el-option :label="$t('order.statusUpdates.no')" value="NO" />
          </el-select>
        </template>
      </div>
    
      <!-- 批量操作确认区域和订单预览列表 -->
      <div v-if="isBatch && orderIds.length > 0" class="batch-summary-section">
        <div class="batch-count-info">
          {{ $t('order.statusUpdates.selectedOrdersCount', { count: orderIds.length }) }}
        </div>
        <div class="order-preview-list-wrapper">
          <!-- <div class="item-label">{{ $t('order.statusUpdate.orderPreviewList') }}</div> -->
          <div class="order-cards-container">
            <div v-for="order in selectedOrdersInfo" :key="order.id" class="order-card">
              <div class="card-header">
                <span class="order-id">{{ order.orderId }}</span>
                <span class="order-status-tag" :class="getDynamicStatusClass(order.statusText)">{{ order.statusText }}</span>
              </div>
              <div class="card-body">
                <span class="order-type">{{ formatOrderType(order.type) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <!-- <div class="warning-text">
        {{ $t('order.statusUpdates.warning') }}
      </div> -->
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('order.statusUpdates.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isBatch ? $t('order.statusUpdates.confirmUpdate') : $t('order.statusUpdates.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { batchUpdateOrderStatus } from '@/api/order'; // Import the new API function

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  // For single order update
  orderId: { // This is the internal DB ID for a single order
    type: [String, Number],
    default: ''
  },
  orderStatus: { // Used for initial state in single mode
    type: String,
    default: 'NORMAL'
  },
  orderFinancialStatus: {
    type: String,
    default: ''
  },
  isOriginalBadDebt: { // Renamed from isBadDebt to avoid confusion with form state
    type: Boolean,
    default: false
  },
  // For batch order update
  isBatch: {
    type: Boolean,
    default: false
  },
  orderIds: { // List of internal DB IDs for batch mode
    type: Array,
    default: () => []
  },
  // New prop for batch mode order details
  selectedOrdersInfo: {
    type: Array,
    default: () => [] // [{ id, orderId, orderType, orderStatus, badDebtFlag }]
  },
  financialStatusOptions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'success']);

const computedFinancialStatusOptions = computed(() => {
  if (props.isBatch) {
    return [{ value: 'NO_ACTION', label: t('order.statusUpdates.noAction') }, ...props.financialStatusOptions];
  }
  return props.financialStatusOptions;
});

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const submitting = ref(false);

const form = ref({
  newOrderStatus: '', // New field for target order status in batch mode
  markAsBadDebtAction: 'NO_ACTION', // New field for mark as bad debt: 'NO_ACTION', 'YES', 'NO'
  reason: '', // Assuming this is still used for reason (though not in UI yet)
  recalculatePenaltyOnCancelBadDebt: undefined // Retain for unmarking bad debt confirmation
});

// Computed property for checkbox in single mode
const isMarkedAsBadDebtComputed = computed({
  get: () => form.value.markAsBadDebtAction === 'YES',
  set: (val) => {
    form.value.markAsBadDebtAction = val ? 'YES' : 'NO';
  }
});

// Helper to format order type (e.g., "INSTALLMENT" to "分期")
const formatOrderType = (type) => {
  if (!type) return '';
  const typeLower = type.toLowerCase(); // Ensure input is lowercase for consistent comparison

  if (typeLower.includes('rental') || typeLower.includes('租赁')) {
    return t('order.types.rental'); // Translate to '租赁'
  } else if (typeLower.includes('installment')) {
    return t('order.types.installment'); // Translate to '分期'
  }
  // Fallback if no match, return original (or a default '未知' if preferred)
  return type; // Fallback to raw type
};

// Helper to determine status class based on statusText
const getDynamicStatusClass = (statusText) => {
  if (statusText.includes(t('order.status.NORMAL').split(' ')[0])) {
    return 'status-NORMAL';
  } else if (statusText.includes(t('order.status.OVERDUE').split(' ')[0])) {
    return 'status-OVERDUE';
  } else if (statusText.includes(t('order.status.COMPLETED'))) {
    return 'status-COMPLETED';
  }
  return 'status-UNKNOWN';
};

const onDialogOpen = () => {
  // Reset form values
  form.value.newOrderStatus = '';
  form.value.reason = '';
  form.value.recalculatePenaltyOnCancelBadDebt = undefined;

  // For single mode, initialize with current order's bad debt state and status
  if (!props.isBatch) {
    // If not batch, 'markAsBadDebtAction' is controlled by a checkbox,
    // so it should be 'YES' or 'NO' initially based on 'isOriginalBadDebt'.
    form.value.markAsBadDebtAction = props.isOriginalBadDebt ? 'YES' : 'NO';
    form.value.newOrderStatus = props.orderFinancialStatus || '';
  } else {
    // For batch mode, it's a select, default to 'NO_ACTION'
    form.value.markAsBadDebtAction = 'NO_ACTION';
    form.value.newOrderStatus = 'NO_ACTION';
  }
};

// Watch for changes in markAsBadDebtAction for immediate confirmation on un-mark
watch(() => form.value.markAsBadDebtAction, async (newVal, oldVal) => {
  // Trigger confirmation only when changing from a marked/no-action state to unmark (NO)
  const isUnmarkingBadDebt = newVal === 'NO';
  const wasMarkedBadDebtBeforeChange = props.isBatch
    ? props.selectedOrdersInfo.some(o => o.badDebtFlag)
    : props.isOriginalBadDebt;

  if (isUnmarkingBadDebt && wasMarkedBadDebtBeforeChange && (oldVal === 'YES' || oldVal === 'NO_ACTION')) {
    try {
      const result = await ElMessageBox.confirm(
        t('order.statusUpdates.recalculatePenaltyQuestion'),
        t('order.statusUpdates.cancelBadDebtTitle'),
        {
          confirmButtonText: t('common.yes'),
          cancelButtonText: t('common.no'),
          type: 'warning',
          distinguishCancelAndClose: true
        }
      );
      if (result === 'confirm') {
        form.value.recalculatePenaltyOnCancelBadDebt = true;
      } else {
        form.value.recalculatePenaltyOnCancelBadDebt = false;
      }
    } catch (e) {
      // User clicked 'Close' (x button) or pressed Esc, or clicked outside the box
      // Revert the action back to the previous state
      form.value.markAsBadDebtAction = oldVal;
      form.value.recalculatePenaltyOnCancelBadDebt = undefined;
    }
  }
  // If changing to YES or NO_ACTION, clear the flag
  if (newVal !== 'NO') {
     form.value.recalculatePenaltyOnCancelBadDebt = undefined;
  }
});

// Closing dialog
const handleClose = () => {
  if (submitting.value) return; // Prevent closing while submitting
  dialogVisible.value = false;
};

// Submit form
const handleSubmit = async () => {
  const idsToUpdate = props.isBatch ? props.orderIds : (props.orderId ? [props.orderId] : []);
  if (idsToUpdate.length === 0) {
    ElMessage.warning(t('order.batch.noSelection'));
    return;
  }

  console.log(props);
  let hasNonOverdue = false;

  if(props.isBatch) {
    hasNonOverdue = props.selectedOrdersInfo.some(o => o.status !== 'OVERDUE');
  }else{
    hasNonOverdue = props.orderStatus !== 'OVERDUE';
  }

  if (hasNonOverdue && form.value.markAsBadDebtAction === 'YES') {
    ElMessage.error(t('order.batch.onlyOverdueForBadDebt'));
    return;
  }

  // Payload construction
  const payload = {
    orderIds: idsToUpdate,
  };

  // Map markAsBadDebtAction to markAsBadDebt boolean/null for API
  if (form.value.markAsBadDebtAction === 'YES') {
    payload.markAsBadDebt = true;
  } else if (form.value.markAsBadDebtAction === 'NO') {
    payload.markAsBadDebt = false;
    // Only include recalculatePenaltyOnCancelBadDebt if markAsBadDebt is false
    if (form.value.recalculatePenaltyOnCancelBadDebt !== undefined) {
      payload.recalculatePenaltyOnCancelBadDebt = form.value.recalculatePenaltyOnCancelBadDebt;
    }
  } else { // 'NO_ACTION'
    payload.markAsBadDebt = null; // API expects null for no change
  }

  // Add newOrderStatus if selected
  if (form.value.newOrderStatus && form.value.newOrderStatus !== 'NO_ACTION') {
    payload.newFinancialStatus = form.value.newOrderStatus;
  }

  // Check if any actual changes are selected
  // If only orderIds exists in payload, it means no operation was selected
  if (Object.keys(payload).length === 1 && payload.orderIds.length > 0) {
      ElMessage.info(t('order.statusUpdates.noOperation'));
      return;
  }

  // Confirmation for marking as bad debt (only if changing to bad debt)
  if (form.value.markAsBadDebtAction === 'YES') { // If explicitly choosing YES
    // Check if any of the orders were NOT originally bad debt, then confirm.
    const needsConfirmation = props.isBatch
      ? props.selectedOrdersInfo.some(o => !o.badDebtFlag)
      : !props.isOriginalBadDebt;

    if (needsConfirmation) {
      try {
        await ElMessageBox.confirm(
          t('order.statusUpdates.badDebtConfirm'),
          t('order.statusUpdates.badDebtTitle'),
          { confirmButtonText: t('common.confirm'), cancelButtonText: t('common.cancel'), type: 'warning' }
        );
      } catch (e) { return; } // User cancelled
    }
  }
  
  submitting.value = true;
  try {
    const res = await batchUpdateOrderStatus(payload); // Assuming this API handles both single/batch and new payload structure

    if (res.code === 1000) {
      // Success message based on batch or single
      if (props.isBatch) {
         ElMessage.success(t('order.batch.statusUpdateAllSuccess', { successCount: idsToUpdate.length }));
      } else {
         ElMessage.success(t('order.statusUpdates.success'));
      }
      emit('success');
      dialogVisible.value = false;
    } else {
      ElMessage.error(res.message || t('order.statusUpdates.failed'));
    }
  } catch (error) {
    console.error('Error updating order status:', error);
    ElMessage.error((error.message || error.msg) || t('order.statusUpdates.failed'));
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.order-status-update-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 20px 10px;
  }

  .status-update-content {
    .select-item {
      margin-bottom: 20px;
      
      .item-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .status-select {
        width: 100%;
      }
    }
    
    .checkbox-description {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
      line-height: 1.5;
    }

    .batch-summary-section {
      margin-bottom: 20px;

      .batch-count-info {
        font-size: 14px;
        color: #409EFF;
        background-color: #ecf5ff;
        border-radius: 4px;
        padding: 10px;
        line-height: 1.4;
        margin-bottom: 15px;
        text-align: center;
      }

      .order-preview-list-wrapper {
        .item-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }

        .order-cards-container {
          display: flex;
          flex-direction: column;
          gap: 10px;
          max-height: 150px;
          overflow-y: auto;
          padding: 5px;
          border: 1px solid #ebeef5;
          border-radius: 4px;
        }

        .order-cards-container .order-card {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 10px;
          background-color: #fff;
          font-size: 14px;
          line-height: 1.5;
        }

        .order-cards-container .order-card .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;
        }

        .order-cards-container .order-card .order-id {
          font-weight: bold;
          font-size: 16px;
          color: #303133;
        }

        .order-cards-container .order-card .order-status-tag {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 10px;
          white-space: nowrap;
        }

        .order-cards-container .order-card .order-status-tag.status-NORMAL {
          background-color: #e6f7d6;
          color: #52c41a;
        }
        .order-cards-container .order-card .order-status-tag.status-OVERDUE {
          background-color: #fff1f0;
          color: #ff4d4f;
        }
        .order-cards-container .order-card .order-status-tag.status-COMPLETED {
          background-color: #f0f2f5;
          color: #909399;
        }

        .order-cards-container .order-card .card-body {
          // No specific styling, just a container for order-type
        }

        .order-cards-container .order-card .order-type {
          font-size: 14px;
          color: #606266;
        }
      }
    }

    .warning-text {
      font-size: 12px;
      color: #E6A23C;
      background-color: #fdf6ec;
      border-radius: 4px;
      padding: 10px;
      line-height: 1.4;
      margin-bottom: 10px;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style> 
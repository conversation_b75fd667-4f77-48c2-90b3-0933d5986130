# 后端API附件字段检查清单

## 必需的附件字段

所有详情接口返回的附件数据都必须包含以下字段：

### 基础字段（必需）
- `id` - 附件ID（字符串）
- `fileName` - 文件名（字符串）
- `fileUrl` - 文件URL（字符串）

### 优化字段（强烈建议）
- `thumbnailUrl` - 缩略图URL（字符串，可为null）
- `fileSize` - 文件大小，单位字节（数字）
- `fileType` - 文件类型（字符串，如"PDF", "JPG", "PNG"等）
- `uploadTime` - 上传时间（ISO格式字符串，如"2024-01-15T10:30:00Z"）

## 需要检查的API接口

### 1. 设备相关接口
- ✅ **设备详情接口** - `GET /api/devices/{id}`
- ✅ **设备新增接口** - `POST /api/devices`
- ✅ **设备编辑接口** - `PUT /api/devices/{id}`
- ✅ **文件上传接口** - `POST /api/upload` (context: 'device')

### 2. 客户相关接口
- ✅ **客户详情接口** - `GET /api/customers/{id}`
- ✅ **客户新增接口** - `POST /api/customers`
- ✅ **客户编辑接口** - `PUT /api/customers/{id}`
- ✅ **文件上传接口** - `POST /api/upload` (context: 'customer')

### 3. 订单相关接口
- ✅ **订单详情接口** - `GET /api/orders/{id}`
- ✅ **订单附件接口** - `GET /api/orders/{id}/attachments`
- ✅ **文件上传接口** - `POST /api/upload` (context: 'order_attachments')

## API响应格式示例

### 详情接口响应格式
```json
{
  "code": 1000,
  "message": "success",
  "data": {
    "id": "device_001",
    "deviceName": "设备名称",
    "attachments": [
      {
        "id": "att_001",
        "fileName": "设备说明书.pdf",
        "fileUrl": "https://example.com/files/device_manual.pdf",
        "thumbnailUrl": "https://example.com/thumbnails/device_manual_thumb.jpg",
        "fileSize": 2048576,
        "fileType": "PDF",
        "uploadTime": "2024-01-15T10:30:00Z"
      },
      {
        "id": "att_002", 
        "fileName": "设备照片.jpg",
        "fileUrl": "https://example.com/files/device_photo.jpg",
        "thumbnailUrl": null,
        "fileSize": 1536000,
        "fileType": "JPG",
        "uploadTime": "2024-01-15T11:00:00Z"
      }
    ]
  }
}
```

### 文件上传接口响应格式
```json
{
  "code": 1000,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/files/uploaded_file.pdf",
    "name": "uploaded_file.pdf",
    "originalFilename": "原始文件名.pdf",
    "thumbnailUrl": "https://example.com/thumbnails/uploaded_file_thumb.jpg",
    "fileSize": 2048576,
    "fileType": "PDF",
    "uploadTime": "2024-01-15T12:00:00Z"
  }
}
```

## 前端已更新的组件

### 设备相关
- ✅ `src/components/Device/CreateDeviceDialog.vue` - 新增/编辑设备
- ✅ `src/views/device/index.vue` - 设备详情页面
- ✅ `src/components/order/tabs/DeviceInfoTab.vue` - 订单中的设备信息

### 客户相关
- ✅ `src/components/customer/CreateCustomerDialog.vue` - 新增/编辑客户
- ✅ `src/views/customer/index.vue` - 客户详情页面
- ✅ `src/components/order/tabs/CustomerInfoTab.vue` - 订单中的客户信息

### 订单相关
- ✅ `src/components/order/AttachmentManager.vue` - 订单附件管理
- ✅ `src/components/order/tabs/OrderInfoTab.vue` - 订单信息标签页

### 通用组件
- ✅ `src/components/common/AttachmentList.vue` - 通用附件列表组件

## 字段说明

### thumbnailUrl
- **PDF文件**：后端生成的PDF首页缩略图URL
- **图片文件**：可以为null，前端直接使用原图
- **其他文件**：为null，前端显示文件类型图标

### fileSize
- 单位：字节（bytes）
- 用于前端显示格式化的文件大小（如"2.0 MB"）

### fileType
- 建议使用大写格式（如"PDF", "JPG", "PNG", "DOCX"等）
- 用于前端显示文件类型标识

### uploadTime
- 格式：ISO 8601格式（如"2024-01-15T10:30:00Z"）
- 用于前端显示上传时间

## 优化效果

通过提供完整的附件字段，前端可以：
- ✅ 直接显示缩略图，无需下载文件生成
- ✅ 直接显示文件大小，无需额外请求
- ✅ 直接显示文件类型，无需前端判断
- ✅ 直接显示上传时间，提供完整信息
- ✅ 减少网络请求，提升性能
- ✅ 提升用户体验，减少加载时间

## 注意事项

1. **向后兼容**：如果某些字段暂时无法提供，可以返回null或默认值
2. **缩略图生成**：建议后端在文件上传时异步生成缩略图
3. **文件大小**：确保返回准确的字节数
4. **时间格式**：统一使用ISO 8601格式
5. **错误处理**：如果缩略图生成失败，thumbnailUrl应为null而不是错误的URL

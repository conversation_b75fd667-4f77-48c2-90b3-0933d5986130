<template>
  <div class="device-container">
    <!-- 面包屑 -->
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ t('menus.pureHome') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('menus.device') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('menus.deviceList') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="queryParams" class="search-form" @submit.prevent="handleQuery">
        <el-form-item>
          <el-input
            v-model="queryParams.keyword"
            :placeholder="t('device.search.placeholder')"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @clear="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.status"
            :placeholder="t('device.search.status')"
            clearable
            style="width: 140px"
            @change="handleQuery"
            popper-class="device-status-dropdown"
          >
            <el-option :label="t('common.all')" value="" />
            <el-option :label="t('device.status.IN_STOCK')" value="IN_STOCK" />
            <el-option :label="t('device.status.IN_USE')" value="IN_USE" />
            <el-option :label="t('device.status.UNDER_REPAIR')" value="UNDER_REPAIR" />
            <el-option :label="t('device.status.IN_TRANSIT')" value="IN_TRANSIT" />
            <el-option :label="t('device.status.SCRAPPED')" value="SCRAPPED" />
            <el-option :label="t('device.status.LOST')" value="LOST" />
            <el-option :label="t('device.status.PREPARING')" value="PREPARING" />
            <el-option :label="t('device.status.SOLD')" value="SOLD" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.brand"
            :placeholder="t('device.search.brand')"
            clearable
            style="width: 120px"
            @change="handleQuery"
          >
            <el-option :label="t('common.all')" value="" />
            <el-option
              v-for="item in brandOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.color"
            :placeholder="t('device.search.color')"
            clearable
            style="width: 120px"
            @change="handleQuery"
          >
            <el-option :label="t('common.all')" value="" />
            <el-option
              v-for="item in colorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.syncStatus"
            :placeholder="t('device.search.syncStatus')"
            clearable
            style="width: 120px"
            @change="handleQuery"
          >
            <el-option :label="t('common.all')" value="" />
            <el-option :label="t('device.search.synced')" value="true" />
            <el-option :label="t('device.search.pendingSync')" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('device.createTimeRange')">
          <el-date-picker
            v-model="queryParams.createTimeRange"
            type="datetimerange"
            range-separator="-"
            :start-placeholder="t('common.startDate')"
            :end-placeholder="t('common.endDate')"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">{{ t('common.search') }}</el-button>
          <el-button @click="resetQuery">{{ t('device.buttons.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 内容区域 -->
    <el-card class="content-card">
      <!-- 操作按钮 -->
      <div class="toolbar">
        <el-button type="primary" @click="handleAdd">
          <el-icon><plus /></el-icon>{{ t('device.buttons.create') }}
        </el-button>
        <el-button type="primary" @click="handleImport">
          <el-icon><upload /></el-icon>{{ t('device.buttons.import') }}
        </el-button>
        <el-dropdown @command="handleExport" style="margin-left: 10px;">
          <el-button type="primary">
            <el-icon><download /></el-icon>{{ t('device.buttons.export') }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="csv">{{ t('device.export.csv') }}</el-dropdown-item>
              <el-dropdown-item command="excel">{{ t('device.export.excel') }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- 批量操作下拉菜单 -->
        <el-dropdown @command="handleBatchAction" style="margin-left: 10px;">
          <el-button type="danger" :disabled="!selectedDevices.length">
            {{ t('common.batchOperations') }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="batchDelete">{{ t('device.buttons.batchDelete') }}</el-dropdown-item>
              <el-dropdown-item command="batchChangeStatus">{{ t('device.batchChangeStatus.title') }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 设备列表 -->
      <div class="table-container">
        <div class="table-wrapper">
          <el-table
            v-loading="loading"
            :data="deviceList"
            style="width: 100%"
            max-height="58vh"
            border
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" fixed/>
            <el-table-column :label="t('device.list.deviceNumber')" min-width="130" show-overflow-tooltip fixed>
              <template #default="{ row }">
                <el-button link type="primary" @click="handleDetail(row)">{{ row.deviceNo || '--' }}</el-button>
              </template>
            </el-table-column>
            <el-table-column :label="t('device.deviceInfo')" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                 {{ [getBrandName(row.brand), row.model, row.deviceName ? `(${row.deviceName})` : ''].filter(Boolean).join(' ') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="t('device.list.status')" width="100">
              <template #default="{ row }">
                <el-tag :style="getStatusTagStyle(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sn" :label="t('device.serialNumber')" min-width="120" show-overflow-tooltip />
            <el-table-column prop="imei1" label="IMEI1" min-width="120" show-overflow-tooltip />
            <el-table-column prop="imei2" label="IMEI2" min-width="120" show-overflow-tooltip />
            <el-table-column prop="syncStatus" :label="t('device.syncStatus')" width="120">
              <template #default="{ row }">
                <el-tag :type="row.syncStatus ? 'success' : 'warning'">
                  {{ row.syncStatus ? t('device.search.synced') : t('device.search.pendingSync') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="syncTime" :label="t('device.syncTime')" min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatDateTime(row.syncTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" :label="t('common.createTime')" min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="orderId" :label="t('device.list.orderNo')" min-width="120" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.orderId ? row.orderId : '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="costAmount" :label="t('device.costAmount')" min-width="120" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.costAmount !== null && row.costAmount !== undefined ? `¥${row.costAmount.toFixed(2)}` : '--' }}
              </template>
            </el-table-column>
            <el-table-column :label="t('device.list.operations')" width="180" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleDetail(row)">{{ t('common.detail') }}</el-button>
                <el-button link type="primary" @click="handleEdit(row)">{{ t('common.edit') }}</el-button>
                <el-button link type="danger" @click="handleDelete(row)">{{ t('common.delete') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          {{ t('common.total', { count: total }) }}
        </div>
        <div class="pagination-content">
          <el-pagination
            v-model:current-page="queryParams.current"
            v-model:page-size="queryParams.size"
            :total="total"
            :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 设备详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="t('device.details.title')"
      width="1000px"
      :close-on-click-modal="false"
      destroy-on-close
      top="5vh"
    >
      <div class="device-detail-content" v-if="currentDevice"> 
        <div class="detail-section-title">{{ t('device.basicInfo') }}</div>
        <el-descriptions :column="2" border class="detail-block">
          <el-descriptions-item :label="t('device.list.deviceNumber')">{{ currentDevice.deviceNo || '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('device.brand.label')">{{ getBrandName(currentDevice.brand) }}</el-descriptions-item>
          <el-descriptions-item :label="t('device.model')">{{ currentDevice.model || '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('device.deviceName')">{{ currentDevice.deviceName || '--' }}</el-descriptions-item> 
          <el-descriptions-item :label="t('device.colorInfo')">
             <span v-if="currentDevice.color">{{ getColorText(currentDevice.color) }}</span>
             <span v-else>--</span>
          </el-descriptions-item>
          <el-descriptions-item :label="t('device.status.label')">
            <el-tag :type="getStatusType(currentDevice.status)" size="small">{{ getStatusText(currentDevice.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('device.costAmount')" :span="2">{{ currentDevice.costAmount !== null ? currentDevice.costAmount : '--' }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section-title">{{ t('device.identificationInfo') }}</div>
        <el-descriptions :column="1" border class="detail-block">
          <el-descriptions-item :label="t('device.serialNumber')">{{ currentDevice.sn || '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('device.imei1')">{{ currentDevice.imei1 || '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('device.imei2')">{{ currentDevice.imei2 || '--' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section-title">{{ t('order.information') }}</div>
        <el-descriptions v-if="currentDevice.relatedOrder" :column="2" border class="detail-block">
          <el-descriptions-item :label="t('order.orderNo')">{{ currentDevice.relatedOrder.orderId || '--' }}</el-descriptions-item> 
          <el-descriptions-item :label="t('order.orderType')">{{ currentDevice.relatedOrder.orderType ? t('order.types.' + currentDevice.relatedOrder.orderType.toLowerCase()) : '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('order.orderStatus')">{{ currentDevice.relatedOrder.orderStatus ? t('order.status.' + currentDevice.relatedOrder.orderStatus.toUpperCase()) : '--' }}</el-descriptions-item>
          <el-descriptions-item :label="t('order.firstPaymentDate')">{{ currentDevice.relatedOrder.firstPaymentDate || '--' }}</el-descriptions-item>
        </el-descriptions>
        <div v-else class="no-data-placeholder detail-block">{{ t('common.noData') }}</div>

        <div class="detail-section-title">{{ t('device.additionalInfo') }}</div>
         <el-descriptions :column="1" border class="detail-block">
            <el-descriptions-item :label="t('device.remarks')" label-class-name="remark-label-custom-width">
              <div class="remark-content" v-if="currentDevice.remark">
                {{ currentDevice.remark }}
              </div>
              <span v-else class="no-content">--</span>
            </el-descriptions-item>
         </el-descriptions>

        <div class="detail-section-title">{{ t('device.attachments') }}</div>
        <div class="attachments-container" v-if="processedAttachments.length > 0">
          <div v-for="attachment in processedAttachments" :key="attachment.id" class="attachment-item">
            <div class="attachment-thumbnail">
              <template v-if="isImageFile(attachment.fileName)">
                <el-image
                  style="width: 80px; height: 80px; border-radius: 4px; cursor: pointer"
                  :src="getProxiedUrl(attachment.fileUrl)"
                  :preview-src-list="processedAttachments.filter(att => isImageFile(att.fileName)).map(a => getProxiedUrl(a.fileUrl))"
                  :initial-index="processedAttachments.filter(att => isImageFile(att.fileName)).findIndex(a => a.id === attachment.id)"
                  fit="cover"
                  lazy
                />
              </template>
              <template v-else-if="isPdfFile(attachment.fileName)">
                <div class="pdf-thumbnail-wrapper" @click="handlePreview(attachment)">
                  <el-image
                    v-if="attachment.thumbnail"
                    style="width: 80px; height: 80px; border-radius: 4px; cursor: pointer;"
                    :src="getProxiedUrl(attachment.thumbnail)"
                    fit="cover"
                    :preview-src-list="[getProxiedUrl(attachment.thumbnail)]"
                  />
                  <div v-else class="thumbnail-placeholder">
                    <el-icon size="32"><Document /></el-icon>
                  </div>
                </div>
              </template>
              <template v-else>
                <!-- 对于其他文件类型，如果有缩略图就显示，否则显示默认图标 -->
                <div v-if="attachment.thumbnail" @click="handlePreview(attachment)">
                  <el-image
                    style="width: 80px; height: 80px; border-radius: 4px; cursor: pointer;"
                    :src="getProxiedUrl(attachment.thumbnail)"
                    fit="cover"
                    :preview-src-list="[getProxiedUrl(attachment.thumbnail)]"
                  />
                </div>
                <div v-else class="thumbnail-placeholder" @click="handlePreview(attachment)">
                  <el-icon size="32"><Document /></el-icon>
                </div>
              </template>
            </div>
            <div class="attachment-info">
              <div class="attachment-name" :title="attachment.fileName">{{ attachment.fileName }}</div>
            </div>
            <div class="attachment-actions-overlay">
              <el-button link type="primary" size="small" @click="handlePreview(attachment)">{{ t('common.view') }}</el-button>
              <el-button link type="primary" size="small" @click="handleDownload(attachment)">{{ t('common.download') }}</el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-data-placeholder detail-block">{{ t('common.noAttachments') }}</div>
        
        <div class="detail-section-title">{{ t('device.timeInfo') }}</div>
        <el-descriptions :column="2" border class="detail-block">
          <el-descriptions-item :label="t('device.syncStatus')">
            <el-tag :type="currentDevice.syncStatus ? 'success' : 'warning'">
              {{ currentDevice.syncStatus ? t('device.search.synced') : t('device.search.pendingSync') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('device.syncTime')">{{ formatDateTime(currentDevice.syncTime) }}</el-descriptions-item>
          <el-descriptions-item :label="t('common.createTime')">{{ formatDateTime(currentDevice.createTime) }}</el-descriptions-item>
          <el-descriptions-item :label="t('common.updateTime')">{{ formatDateTime(currentDevice.updateTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-skeleton :rows="10" animated v-else />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">{{ t('common.close') }}</el-button>
          <el-button type="primary" @click="handleEditFromDetail">{{ t('common.edit') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建/编辑设备弹窗 -->
    <create-device-dialog
      v-if="dialogVisible"
      :key="createDialogKey"
      v-model:visible="dialogVisible"
      :show-status-field-on-create="true"
      :device-data="currentDevice"
      @success="handleSuccess"
      @close="handleDialogClose"
    />

    <!-- 批量删除确认弹窗 -->
    <el-dialog v-model="batchDeleteVisible" :title="t('device.batchDelete.dialogTitle')" width="480px" :close-on-click-modal="false">
      <div class="batch-operation-content">
        <div class="warning-message">
          <el-icon class="warning-icon"><warning-filled /></el-icon>
          <div>
            {{ t('device.batchDelete.confirmMessage', { count: selectedDevices.length }) }}
            <span class="danger-text">{{ t('device.batchDelete.irreversibleWarning') }}</span>
          </div>
        </div>
        
        <div class="selected-devices-title">{{ t('device.batchDelete.selectedDevicesTitle') }}</div>
        <div class="selected-devices-list">
          <div v-for="device in selectedDevices" :key="device.id" class="device-item">
            <div class="device-info">
              <div class="device-name">{{ device.deviceName || device.model }}</div>
              <div class="device-desc">{{ getBrandName(device.brand) }} {{ device.model }} ({{ device.sn }})</div>
            </div>
            <el-tag :type="getStatusType(device.status)" size="small">{{ getStatusText(device.status) }}</el-tag>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDeleteVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmBatchDelete" :loading="batchDeleteLoading">
            {{ t('device.batchDelete.confirmButton') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量变更状态弹窗 -->
    <el-dialog 
      v-model="batchChangeStatusVisible" 
      :title="t('device.batchChangeStatus.dialogTitle')" 
      width="480px" 
      :close-on-click-modal="false"
    >
      <div class="batch-operation-content">
        <div class="change-status-form">
          <el-form :model="batchStatusForm" label-width="80px">
            <el-form-item :label="t('device.batchChangeStatus.changeToLabel')">
              <el-select v-model="batchStatusForm.targetStatus" :placeholder="t('device.batchChangeStatus.targetStatusPlaceholder')" style="width: 100%">
                <el-option :label="t('device.status.IN_STOCK')" value="IN_STOCK" />
                <el-option :label="t('device.status.IN_USE')" value="IN_USE" />
                <el-option :label="t('device.status.UNDER_REPAIR')" value="UNDER_REPAIR" />
                <el-option :label="t('device.status.IN_TRANSIT')" value="IN_TRANSIT" />
                <el-option :label="t('device.status.SCRAPPED')" value="SCRAPPED" />
                <el-option :label="t('device.status.LOST')" value="LOST" />
                <el-option :label="t('device.status.PREPARING')" value="PREPARING" />
                <el-option :label="t('device.status.SOLD')" value="SOLD" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="info-message">
          <el-icon class="info-icon"><info-filled /></el-icon>
          {{ t('device.batchChangeStatus.infoMessage', { count: selectedDevices.length }) }}
        </div>
        
        <div class="selected-devices-title">{{ t('device.batchChangeStatus.selectedDevicesTitle') }}</div>
        <div class="selected-devices-list">
          <div v-for="device in selectedDevices" :key="device.id" class="device-item">
            <div class="device-info">
              <div class="device-name">{{ device.deviceName || device.model }}</div>
              <div class="device-desc">{{ getBrandName(device.brand) }} {{ device.model }} ({{ device.sn }})</div>
            </div>
            <el-tag :type="getStatusType(device.status)" size="small">{{ getStatusText(device.status) }}</el-tag>
          </div>
        </div>
        
        <div class="warning-notice" v-if="hasRentedDevices">
          <el-icon class="warning-icon"><warning-filled /></el-icon>
          <span>{{ t('device.batchChangeStatus.rentedWarning') }}</span>
        </div>
        
        <div class="confirmation-checkbox">
          <el-checkbox v-model="statusChangeConfirmed">{{ t('device.batchChangeStatus.confirmationCheckbox') }}</el-checkbox>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchChangeStatusVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" :disabled="!statusChangeConfirmed || !batchStatusForm.targetStatus" @click="confirmBatchChangeStatus" :loading="batchChangeStatusLoading">
            {{ t('device.batchChangeStatus.confirmButton') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, onActivated } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document, Upload, Download, ArrowDown, WarningFilled, InfoFilled } from '@element-plus/icons-vue'
import { getDeviceList, deleteDevice, batchDeleteDevices, exportDeviceList, getDeviceDetail, batchUpdateDeviceStatus } from '@/api/device'
import { downloadOssFile } from '@/api/upload';
import CreateDeviceDialog from '@/components/Device/CreateDeviceDialog.vue'
import { formatDateTime } from '@/utils/format'
import { useRouter } from 'vue-router';
import { cloneDeep } from 'lodash';
import * as pdfjsLib from 'pdfjs-dist/build/pdf.mjs';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.mjs?url';
import axios from 'axios';
import { getDictFields } from '@/api/dictionary';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const { t } = useI18n()
const router = useRouter();

// 查询参数
const queryParams = ref({
  current: 1,
  size: 20,
  keyword: '',
  status: '',
  brand: '',
  color: '',
  syncStatus: '',
  createTimeRange: []
})

const loading = ref(false)
const deviceList = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const currentDevice = ref({})
const detailVisible = ref(false)
const selectedDevices = ref([])
const createDialogKey = ref(0)
const batchDeleteVisible = ref(false)
const batchChangeStatusVisible = ref(false)
const batchStatusForm = reactive({
  targetStatus: ''
})
const processedAttachments = ref([]);
const batchDeleteLoading = ref(false)
const statusChangeConfirmed = ref(false)
const batchChangeStatusLoading = ref(false)
const colorOptions = ref([]);
const brandOptions = ref([]);

// 计算属性：检查选中设备中是否有租用中的设备
const hasRentedDevices = computed(() => {
  return selectedDevices.value.some(device => device.status === 'IN_USE')
})

// 获取设备列表
const getList = async () => {
  try {
    loading.value = true
    const params = {
      ...queryParams.value,
      createTimeStart: queryParams.value.createTimeRange?.[0],
      createTimeEnd: queryParams.value.createTimeRange?.[1]
    }
    delete params.createTimeRange

    const res = await getDeviceList(params)
    console.log('设备列表数据：', res)
    if (res.code === 1000) {
      deviceList.value = res.data.records
      total.value = res.data.total
    } else {
      throw new Error(res.message)
    }
  } catch (error) {
    console.error('获取设备列表失败：', error)
    ElMessage.error(error.message || t('device.fetchListFailed'))
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.value.current = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.value.keyword = ''
  queryParams.value.status = ''
  queryParams.value.brand = ''
  queryParams.value.color = ''
  queryParams.value.syncStatus = ''
  queryParams.value.createTimeRange = []
  getList()
}

// 新增设备
const handleAdd = () => {
  currentDevice.value = null
  createDialogKey.value++
  dialogVisible.value = true
}

// 编辑设备
const handleEdit = async (row) => {
  try {
    loading.value = true
    const res = await getDeviceDetail(row.id)
    if (res.code === 1000 && res.data) {
      currentDevice.value = res.data
      createDialogKey.value++
      dialogVisible.value = true
    } else {
      ElMessage.error(res.message || t('device.fetchDetailFailed'))
    }
  } catch (error) {
    console.error('获取设备详情失败：', error)
    ElMessage.error(t('device.fetchDetailFailedRetry'))
  } finally {
    loading.value = false
  }
}

// 删除设备
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(t('device.delete.confirmSingle'), t('device.delete.title'), {
      type: 'warning'
    })
    
    const res = await deleteDevice(row.id)
    if (res.code === 1000) {
      ElMessage.success(t('common.deleteSuccess'))
      getList()
    } else {
      throw new Error(res.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除设备失败：', error)
    }
  }
}

// 处理分页大小改变
const handleSizeChange = (val) => {
  queryParams.value.size = val
  getList()
}

// 处理页码改变
const handleCurrentChange = (val) => {
  queryParams.value.current = val
  getList()
}

// 获取状态标签样式
const getStatusTagStyle = (status) => {
  const styleMap = {
    'IN_STOCK': { backgroundColor: '#67c23a', color: 'white', borderColor: 'transparent' }, // 绿色
    'IN_USE': { backgroundColor: '#409eff', color: 'white', borderColor: 'transparent' }, // 蓝色
    'UNDER_REPAIR': { backgroundColor: '#e6a23c', color: 'white', borderColor: 'transparent' }, // 黄色
    'SCRAPPED': { backgroundColor: '#909399', color: 'white', borderColor: 'transparent' }, // 灰色
    'LOST': { backgroundColor: '#f56c6c', color: 'white', borderColor: 'transparent' }, // 红色
    'IN_TRANSIT': { backgroundColor: '#fd7e14', color: 'white', borderColor: 'transparent' }, // 橙色
    'SOLD': { backgroundColor: '#4a5568', color: 'white', borderColor: 'transparent' }, // 深灰蓝色
    'PREPARING': { backgroundColor: '#6f42c1', color: 'white', borderColor: 'transparent' }, // 紫色
  };
  return styleMap[status] || { backgroundColor: '#E9E9EB', color: '#909399', borderColor: 'transparent' }; // 默认样式
};


// 获取状态类型 (此函数现在仅用于详情页，或者可以被新的样式逻辑替代)
const getStatusType = (status) => {
  const statusMap = {
    'IN_STOCK': 'success',
    'IN_USE': 'primary',
    'UNDER_REPAIR': 'warning',
    'IN_TRANSIT': 'warning',
    'SCRAPPED': 'info',
    'LOST': 'danger',
    'PREPARING': 'warning',
    'SOLD': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  if (!status) return '--';
  return t(`device.status.${status}`) || status;
}

// 颜色值映射
const getColorValue = (colorKey) => {
  const colorMap = {
    'black': '#000000',
    'white': '#FFFFFF',
    'silver': '#C0C0C0',
    'gold': '#FFD700',
    'blue': '#1E90FF',
    'red': '#FF4500',
    'green': '#32CD32',
    'purple': '#9932CC',
    'gray': '#808080',
    'brown': '#8B4513'
  }
  return colorMap[colorKey] || '#E5E7EB'
}

// 颜色文本映射（多语言）
const getColorText = (colorValue) => {
  if (!colorValue) return '--';
  const option = colorOptions.value.find(opt => opt.value === colorValue);
  return option ? option.label : colorValue;
}

const getBrandName = (brandValue) => {
  if (!brandValue) return '--';
  const option = brandOptions.value.find(opt => opt.value === brandValue);
  return option ? option.label : brandValue;
};

// 处理操作成功
const handleSuccess = () => {
  getList()
}

// 处理设备选择变化
const handleSelectionChange = (val) => {
  selectedDevices.value = val
}

// 处理导入设备
const handleImport = () => {
  // 实现导入设备的功能
}

// 处理导出设备
const handleExport = async (command) => {
  if (deviceList.value.length === 0) {
    ElMessage.warning(t('device.export.emptyListError'))
    return
  }

  try {
    loading.value = true
    
    const exportParams = {
      ...queryParams.value, // queryParams should be defined in your setup, containing filters like keyword, status etc.
      // format: command, // The exportDeviceList function in API already takes format as a separate argument or merges it.
    };
    
    if (selectedDevices.value && selectedDevices.value.length > 0) {
      // OpenAPI spec for device export does not show an 'ids' parameter.
      // If your backend /api/devices/export supports filtering by a list of IDs, 
      // ensure 'ids' is a supported query parameter, possibly comma-separated string or repeated.
      // Example: exportParams.ids = selectedDevices.value.map(item => item.id).join(',');
      // For now, assuming export of all filtered if no explicit selection logic on backend for 'ids
      console.warn('[handleExport] Exporting selected devices by ID is not explicitly defined in the provided OpenAPI for /api/devices/export. Sending all filtered results.');
    }
    
    console.log('[handleExport] Export params being sent to API function:', exportParams, 'format:', command);
    
    // exportDeviceList from api/device.js is expected to return the full axios response
    const response = await exportDeviceList(exportParams, command); 
    
    console.log('[handleExport] Export API response received');

    let blobData;
    let mimeType;
    let fileExt = command; // csv or excel (becomes xlsx)

    if (command === 'excel') {
      mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileExt = 'xlsx';
    } else if (command === 'csv') {
      mimeType = 'text/csv;charset=utf-8';
    } else {
      mimeType = 'application/octet-stream'; // Fallback
      console.warn('[handleExport] Unknown export format requested:', command);
    }

    // With responseType: 'blob', axios should put the blob in response.data
    if (response && response.data && response.data instanceof Blob) {
      blobData = response.data;
      // Check if the blob's type from server is more specific and valid
      if (response.data.type && response.data.type !== 'application/octet-stream') {
         mimeType = response.data.type;
         console.log('[handleExport] Using MIME type from response Blob:', mimeType);
      }
    } else {
      console.error('[handleExport] Export response is not a Blob or is missing data. Response:', response);
      let errorJson = {};
      try {
        // If response.data is where the error message might be (e.g. as JSON string or object)
        errorJson = (response && typeof response.data === 'string') ? JSON.parse(response.data) : (response && response.data);
      } catch (e) { /* ignore parsing error, errorJson remains {} */ }
      const apiMessage = errorJson?.message || '服务器未返回有效文件数据';
      ElMessage.error(`${t('device.export.error', '导出失败')}: ${apiMessage}`);
      loading.value = false;
      return;
    }

    const link = document.createElement('a');
    link.href = URL.createObjectURL(new Blob([blobData], { type: mimeType }));
    
    const contentDisposition = response.headers ? response.headers['content-disposition'] : '';
    // Use i18n for filename prefix, with a fallback
    let fileName = `${t('device.list.filenamePrefix', '设备列表')}_${new Date().toISOString().slice(0,10)}_${new Date().getTime()}.${fileExt}`;
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;"\n]*?)['";\n]?$/i);
      if (fileNameMatch && fileNameMatch[1]) {
        try {
            fileName = decodeURIComponent(fileNameMatch[1]);
            console.log('[handleExport] Filename from Content-Disposition (decoded):', fileName);
        } catch (e) {
            fileName = fileNameMatch[1]; // Fallback to raw match
            console.warn('[handleExport] Could not decode filename from Content-Disposition, using raw value:', fileName, 'Error:', e);
        }
      } else {
         const simpleMatch = contentDisposition.match(/filename="?(.+)"?/);
         if (simpleMatch && simpleMatch[1]) {
            fileName = simpleMatch[1];
            console.log('[handleExport] Filename from Content-Disposition (simple match):', fileName);
         }
      }
    }
    
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    
    ElMessage.success(t('device.export.exportSuccess'))
  } catch (error) {
    console.error('[handleExport] Export failed catastrophically:', error);
    let errorMessage = error.message || t('device.export.exportFailedError');
    if (error.response && error.response.data && error.response.data instanceof Blob) {
        try {
            // Attempt to read error message from blob if the server sent error as blob
            const errorText = await error.response.data.text();
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || errorMessage;
        } catch (e) {
            // Blob is not JSON or text, stick with original error from error.message
        }
    } else if (error.response && error.response.data && error.response.data.message) {
        // If error response has a JSON body with a message property
        errorMessage = error.response.data.message;
    }

    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
}

// 处理批量操作
const handleBatchAction = async (command) => {
  if (!selectedDevices.value.length) {
    return ElMessage.warning(t('device.selectDeviceForBatchOperation'))
  }
  
  if (command === 'batchDelete') {
    batchDeleteVisible.value = true
  } else if (command === 'batchChangeStatus') {
    statusChangeConfirmed.value = false // 重置确认状态
    batchStatusForm.targetStatus = '' // 重置选中状态
    batchChangeStatusVisible.value = true
  }
}

// 处理批量删除确认
const confirmBatchDelete = async () => {
  try {
    batchDeleteLoading.value = true
    const res = await batchDeleteDevices(selectedDevices.value.map(item => item.id))
    if (res.code === 1000) {
      ElMessage.success(t('device.batchDelete.success'))
      batchDeleteVisible.value = false
      getList()
    } 
  } catch (error) {
    console.error('批量删除失败：', error)
  } finally {
    batchDeleteLoading.value = false
  }
}

// 处理批量变更状态
const confirmBatchChangeStatus = async () => {
  try {
    batchChangeStatusLoading.value = true
    const res = await batchUpdateDeviceStatus(
      selectedDevices.value.map(item => item.id), 
      batchStatusForm.targetStatus
    )
    if (res.code === 1000) {
      ElMessage.success(t('device.batchChangeStatus.success'))
      batchChangeStatusVisible.value = false
      statusChangeConfirmed.value = false
      batchStatusForm.targetStatus = ''
      getList()
    } else {
      throw new Error(res.message)
    }
  } catch (error) {
    console.error('批量变更状态失败：', error)
    // ElMessage.error(error.message || t('device.batchChangeStatus.error'))
  } finally {
    batchChangeStatusLoading.value = false
  }
}

// 处理设备详情
const handleDetail = async (row) => {
  try {
    loading.value = true;
    const res = await getDeviceDetail(row.id);
    console.log('[handleDetail] API Response:', JSON.parse(JSON.stringify(res)));

    if (res.code === 1000 && res.data) {
      currentDevice.value = res.data;
      console.log('[handleDetail] currentDevice BEFORE attachments processing:', JSON.parse(JSON.stringify(currentDevice.value)));
      
      processAllAttachments(currentDevice.value.attachments);

      console.log('[handleDetail] currentDevice AFTER attachments processing:', JSON.parse(JSON.stringify(currentDevice.value)));

      detailVisible.value = true;
    } else {
      ElMessage.error(res.message || t('device.fetchDetailFailed'));
    }
  } catch (error) {
    console.error('获取设备详情失败：', error);
    ElMessage.error(t('device.fetchDetailFailedRetry'));
  } finally {
    loading.value = false;
  }
}

// #region --- New Attachment Logic ---

const getProxiedUrl = (url) => {
  if (!url) return '';
  const targetDomain = 'https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com';
  if (url.startsWith(targetDomain)) {
    if (import.meta.env.DEV) {
      return url.replace(targetDomain, '/cos-proxy');
    }
  }
  return url;
};

const isImageFile = (fileName) => {
  if (typeof fileName !== 'string') return false;
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = fileName.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
};

const isPdfFile = (fileName) => {
  if (typeof fileName !== 'string') return false;
  return fileName.toLowerCase().endsWith('.pdf');
};

// 不再需要文件类型和大小格式化函数，因为详情页不显示这些信息

// PDF缩略图生成函数已移除，现在使用后端返回的缩略图

const processAllAttachments = async (attachments) => {
  if (!attachments || attachments.length === 0) {
    processedAttachments.value = [];
    return;
  }

  const normalizedAttachments = attachments.map((att, index) => {
    if (typeof att === 'string') {
      const fileName = att.split('/').pop().split('?')[0];
      return { fileName: fileName, fileUrl: att, id: att };
    }
    if (typeof att === 'object' && att !== null && (att.fileUrl || att.url)) {
      return {
        fileName: att.fileName || att.name || `Attachment ${index + 1}`,
        fileUrl: att.fileUrl || att.url,
        id: att.id || att.uid || att.fileUrl || att.url,
        // 使用后端返回的缩略图信息
        thumbnail: att.thumbnail || null,
        ...att
      };
    }
    return null;
  }).filter(Boolean);

  // 直接使用后端返回的数据，不需要异步处理
  processedAttachments.value = normalizedAttachments;
};

const handlePreview = (attachment) => {
  window.open(getProxiedUrl(attachment.fileUrl), '_blank');
};

const handleDownload = async (attachment) => {
  if (attachment.fileUrl) {
    try {
      const { blob, filename } = await downloadOssFile(attachment.fileUrl);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || attachment.fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载文件失败:', error);
      ElMessage.error('下载文件失败，请检查链接或网络连接。');
    }
  } else {
    ElMessage.warning('附件链接无效。');
  }
};

// #endregion

// 处理编辑详情弹窗
const handleEditFromDetail = () => {
  console.log('[handleEditFromDetail] Attempting to edit from detail. currentDevice BEFORE closing detail:', JSON.parse(JSON.stringify(currentDevice.value || {})));
  detailVisible.value = false // 先关闭详情弹窗
  nextTick(() => {
    console.log('[handleEditFromDetail] currentDevice AFTER closing detail, BEFORE opening edit dialog:', JSON.parse(JSON.stringify(currentDevice.value || {})));
    createDialogKey.value++ // 强制重新创建组件
    dialogVisible.value = true // 再打开编辑弹窗 (CreateDeviceDialog)
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
  currentDevice.value = null
}

// 加载字段选项的函数
const loadFieldOptions = async () => {
  console.log('开始加载设备页面字段选项...')
  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'color', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      colorOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch color options:", error);
  }

  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'brand', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      brandOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch brand options:", error);
  }
}

// 页面加载时获取列表
onMounted(async () => {
  getList()
  await loadFieldOptions()
})

/**
 * 页面激活时重新加载字段选项
 */
onActivated(async () => {
  console.log('设备列表页面激活，重新加载字段选项...')
  await loadFieldOptions()
  resetQuery()
})

const isImageAttachment = (attachment) => {
  if (!attachment || !attachment.url) return false;
  const url = attachment.url.toLowerCase();
  // A more robust check might involve checking the actual fileName if available and URL doesn't have extension
  // Or checking a mimeType property if the attachment object has one.
  return url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.bmp') || url.endsWith('.webp');
};
</script>

<style lang="scss" scoped>
.device-container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 20px;
  background-color: #f5f7fa;

  .breadcrumb {
    margin-bottom: 16px;
  }

  .search-bar {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    overflow-x: auto;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      align-items: center;

      .el-form-item {
        margin-bottom: 0; // Override default margin
      }
    }
  }

  .content-card {
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    
    :deep(.el-card__body) {
      padding: 20px;
      display: flex;
      flex-direction: column;
    }
  }

  .toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
  }

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &::-webkit-scrollbar {
      height: 8px;
      width: 8px;
    }
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .pagination-info {
      font-size: 14px;
      color: #606266;
    }
  }
}

.table-wrapper {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 40px; /* Adjust based on your table header's actual height */
    left: 0;
    right: 0;
    height: 15px;
    background: linear-gradient(to bottom, white, rgba(255, 255, 255, 0));
    z-index: 2;
    pointer-events: none;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 15px;
    background: linear-gradient(to top, white, rgba(255, 255, 255, 0));
    z-index: 2;
    pointer-events: none;
  }
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

.el-message-box.delete-confirm-dialog {
  .el-message-box__header {
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-message-box__body {
    padding: 20px;
  }

  .el-message-box__footer {
    padding-top: 10px;
    border-top: 1px solid #ebeef5;
  }
}

.device-detail-content {
  max-height: 75vh;
  overflow-y: auto;
  padding: 0 10px; 

  .detail-section-title {
    font-size: 16px;
    font-weight: 600;
    margin-top: 20px; 
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
    &:first-child {
      margin-top: 0;
    }
  }

  .detail-block {
    margin-bottom: 20px;
  }

  .remark-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
    max-height: 120px;
    overflow-y: auto;
    padding: 8px 0;
    font-size: 14px;
    color: #606266;
  }

  .no-content {
    color: #c0c4cc;
    font-style: italic;
  }
}

.attachments-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 16px;
  overflow-x: auto;
  padding: 10px 2px;
}

.attachment-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
  flex-shrink: 0;
  margin-right: 16px;

  &:hover .attachment-actions-overlay {
    opacity: 1;
  }
}

.attachment-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.pdf-thumbnail-wrapper {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #f5f7fa;
  color: #c0c4cc;
  position: relative;
}

.attachment-info {
  width: 100%;
  text-align: center;
  margin-top: 8px;
}

.attachment-name {
  font-size: 12px;
  color: #606266;
  word-break: break-all;
  line-height: 1.2;
}

.attachment-name {
  margin-top: 8px;
  font-size: 13px;
  color: #606266;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-actions-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 4px;

  .el-button {
    color: white;
    font-size: 12px;
    margin: 0;
  }
}

.no-data-placeholder {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

:deep(.el-dialog__body) {
  padding-top: 15px; 
  padding-bottom: 15px;
}

:deep(.el-descriptions-item__label) {
  width: 120px;
}

.remark-label-custom-width {
  width: 80px !important; // 设置备注标签的宽度，可根据需要调整
}

// 批量操作弹窗样式
.batch-operation-content {
  .warning-message {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    background-color: #fdf6ec;
    border: 1px solid #fabe2d;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .warning-icon {
      color: #e6a23c;
      font-size: 16px;
      margin-right: 8px;
    }
    
    .danger-text {
      color: #f56c6c;
      font-weight: bold;
    }
  }
  
  .info-message {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .info-icon {
      color: #409eff;
      font-size: 16px;
      margin-right: 8px;
    }
  }
  
  .warning-notice {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #fdf6ec;
    border: 1px solid #fabe2d;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .warning-icon {
      color: #e6a23c;
      font-size: 16px;
      margin-right: 8px;
    }
  }
  
  .change-status-form {
    margin-bottom: 20px;
  }
  
  .selected-devices-title {
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
    font-size: 14px;
  }
  
  .selected-devices-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .device-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f5f7fa;
      
      &:last-child {
        border-bottom: none;
      }
      
      .device-info {
        flex: 1;
        
        .device-name {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .device-desc {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
  
  .confirmation-checkbox {
    margin-bottom: 20px;
    
    :deep(.el-checkbox) {
      align-items: flex-start;
    }

    :deep(.el-checkbox__label) {
      color: #606266;
      font-size: 14px;
      white-space: normal;
      line-height: 1.5;
    }
  }
}

// 对话框footer样式优化
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 颜色显示样式
.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    flex-shrink: 0;
    position: relative;
    
    // 白色特殊处理，增加边框可见性
    &[style*="#FFFFFF"],
    &[style*="#ffffff"] {
      border: 1px solid #d0d0d0;
    }
  }
  
  .color-text {
    font-size: 14px;
    color: #606266;
  }
}

.attachment-item-container {
  // width: 100px; // Or let content define width
}
.attachment-file-item {
  display: flex;
  align-items: center;
  border-radius: 4px;
  margin-bottom: 10px;
  box-sizing: border-box;
}
.file-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--el-color-primary);
}
.file-icon {
  font-size: 24px; /* Adjust as needed */
  margin-right: 8px;
}
.file-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px; /* Adjust as needed */
}
</style>

<style>
.device-status-dropdown .el-select-dropdown__wrap {
  max-height: 320px;
}
</style> 
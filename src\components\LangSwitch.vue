<template>
  <el-dropdown trigger="click" @command="handleCommand">
    <div class="lang-switch">
      <span class="lang-text">{{ currentLang === 'zh-CN' ? '中文' : 'English' }}</span>
      <span class="arrow-down">▾</span>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
        <el-dropdown-item command="en">English</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { setLanguage } from '@/locales'

const { locale } = useI18n()
const currentLang = computed(() => locale.value)

const handleCommand = (command) => {
  setLanguage(command)
}
</script>

<style lang="scss" scoped>
.lang-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 12px;
  height: 100%;
  color: #fff;
  gap: 4px;
  outline: none !important;
  
  &:focus,
  &:focus-visible {
    outline: none !important;
    box-shadow: none !important;
  }
  
  .lang-text {
    font-size: 14px;
    font-weight: 500;
  }
  
  .arrow-down {
    font-size: 12px;
    margin-left: 4px;
    margin-top: 1px;
  }
}

// 移除下拉菜单的选中边框
:deep(.el-dropdown) {
  outline: none !important;
  
  &:focus,
  &:focus-visible {
    outline: none !important;
    box-shadow: none !important;
  }
}

:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    &:focus,
    &:focus-visible {
      outline: none !important;
      box-shadow: none !important;
    }
  }
}
</style> 
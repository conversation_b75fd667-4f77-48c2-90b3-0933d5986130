<script setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { isUrl } from "@pureadmin/utils";
import { isAllEmpty } from '@pureadmin/utils';

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
});

const router = useRouter();
const route = useRoute();

// 外部链接处理
const isExternalLink = computed(() => {
  return isUrl(props.item.path);
});

// 该菜单是否显示
const showItem = computed(() => {
  return !props.item.meta?.hideMenu;
});

// 是否有子菜单
const hasChildren = computed(() => {
  const { children } = props.item;
  return children && children.length > 0;
});

// 菜单标题
const menuTitle = computed(() => {
  return props.item.meta?.title || '';
});

// 菜单图标
const menuIcon = computed(() => {
  return props.item.meta?.icon || '';
});

// 解析路径
const resolvePath = computed(() => {
  if (isExternalLink.value) {
    return props.item.path;
  }
  return props.basePath + '/' + props.item.path.replace(/^\//, '');
});

// 是否为当前激活菜单
const isActive = computed(() => {
  // 跳过没有路径的菜单项
  if (!props.item.path) return false;
  
  const currentPath = route.path;
  // 如果设置了activePath，则使用它进行比较
  const activePath = props.item.meta?.activePath;
  
  if (activePath && currentPath.includes(activePath)) {
    return true;
  }
  
  return currentPath.includes(resolvePath.value);
});

// 处理菜单点击
function handleMenuClick() {
  if (isExternalLink.value) {
    window.open(props.item.path, '_blank');
  } else {
    router.push(resolvePath.value);
  }
}
</script>

<template>
  <div v-if="showItem">
    <!-- 无子菜单的情况 -->
    <template v-if="!hasChildren">
      <el-menu-item 
        :index="resolvePath" 
        @click="handleMenuClick"
        :class="{ 'is-active': isActive }"
      >
        <template #title>
          <IconifyIconOffline v-if="menuIcon" :icon="menuIcon" />
          <span>{{ menuTitle }}</span>
        </template>
      </el-menu-item>
    </template>
    
    <!-- 有子菜单的情况 -->
    <el-sub-menu v-else :index="resolvePath" popper-class="pure-submenu">
      <template #title>
        <IconifyIconOffline v-if="menuIcon" :icon="menuIcon" />
        <span>{{ menuTitle }}</span>
      </template>
      
      <!-- 递归调用自身渲染子菜单 -->
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :base-path="resolvePath"
      />
    </el-sub-menu>
  </div>
</template>

<style lang="scss" scoped>
.el-menu-item, .el-sub-menu {
  &.is-active {
    color: var(--el-menu-active-color);
    background-color: var(--el-menu-hover-bg-color);
  }
}

// 图标样式
:deep(.el-icon) {
  margin-right: 6px;
  font-size: 18px;
  vertical-align: middle;
}
</style>
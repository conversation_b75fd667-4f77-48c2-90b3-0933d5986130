import { defineStore } from 'pinia';
import { storageLocal } from '@pureadmin/utils';
import { responsiveStorageNameSpace } from '@/config';

export const useSettingStore = defineStore({
  id: 'pure-settings',
  state: () => {
    return {
      // 默认配置
      title: 'Vue Pure Admin',
      fixedHeader: storageLocal().getItem(`${responsiveStorageNameSpace()}layout`)?.fixedHeader ?? true,
      hideTabs: storageLocal().getItem(`${responsiveStorageNameSpace()}configure`)?.hideTabs ?? false,
      hideFooter: storageLocal().getItem(`${responsiveStorageNameSpace()}configure`)?.hideFooter ?? false,
      showLogo: storageLocal().getItem(`${responsiveStorageNameSpace()}configure`)?.showLogo ?? true,
      theme: {
        primaryColor: storageLocal().getItem(`${responsiveStorageNameSpace()}layout`)?.epThemeColor ?? '#409EFF',
        darkMode: storageLocal().getItem(`${responsiveStorageNameSpace()}layout`)?.darkMode ?? false
      },
      footer: {
        author: 'Vue Pure Admin',
        copyright: new Date().getFullYear()
      }
    };
  },
  actions: {
    // 设置标题
    setTitle(title) {
      this.title = title;
    },
    
    // 设置固定头部
    setFixedHeader(fixed) {
      this.fixedHeader = fixed;
      this.saveLayoutConfig();
    },
    
    // 设置显示/隐藏标签页
    setHideTabs(hide) {
      this.hideTabs = hide;
      this.saveConfigure();
    },
    
    // 设置显示/隐藏页脚
    setHideFooter(hide) {
      this.hideFooter = hide;
      this.saveConfigure();
    },
    
    // 设置显示/隐藏Logo
    setShowLogo(show) {
      this.showLogo = show;
      this.saveConfigure();
    },
    
    // 设置主题颜色
    setThemeColor(color) {
      this.theme.primaryColor = color;
      this.saveLayoutConfig();
      
      // 动态设置CSS变量
      document.documentElement.style.setProperty('--el-color-primary', color);
    },
    
    // 设置暗黑模式
    setDarkMode(dark) {
      this.theme.darkMode = dark;
      this.saveLayoutConfig();
    },
    
    // 保存布局配置
    saveLayoutConfig() {
      const layout = storageLocal().getItem(`${responsiveStorageNameSpace()}layout`) || {};
      storageLocal().setItem(`${responsiveStorageNameSpace()}layout`, {
        ...layout,
        fixedHeader: this.fixedHeader,
        epThemeColor: this.theme.primaryColor,
        darkMode: this.theme.darkMode
      });
    },
    
    // 保存界面配置
    saveConfigure() {
      const configure = storageLocal().getItem(`${responsiveStorageNameSpace()}configure`) || {};
      storageLocal().setItem(`${responsiveStorageNameSpace()}configure`, {
        ...configure,
        hideTabs: this.hideTabs,
        hideFooter: this.hideFooter,
        showLogo: this.showLogo
      });
    }
  }
});
/**
 * 获取父级路径
 * @param {string} path 当前路径
 * @param {Array} routes 路由数组
 * @returns {Array} 父级路径数组
 */
export function getParentPaths(path, routes) {
    // 深度优先遍历查找
    function dfs(routes, path, parents) {
      for (let i = 0; i < routes.length; i++) {
        const route = routes[i];
        // 如果当前路由的path与目标path匹配
        if (route.path === path) {
          return parents;
        }
        
        // 如果有子路由
        if (route.children) {
          // 将当前路由加入父级路径
          parents.push(route.path);
          // 递归查找子路由
          const result = dfs(route.children, path, parents);
          if (result.length) {
            return result;
          }
          // 回溯，移除不匹配的父级路径
          parents.pop();
        }
      }
      return [];
    }
    
    return dfs(routes, path, []);
  }
  
  /**
   * 根据路径查找路由
   * @param {string} path 路径
   * @param {Array} routes 路由数组
   * @returns {Object} 匹配的路由
   */
  export function findRouteByPath(path, routes) {
    let result = null;
    
    function dfs(routes, path) {
      for (const route of routes) {
        if (route.path === path) {
          result = route;
          return;
        }
        
        if (route.children) {
          dfs(route.children, path);
        }
      }
    }
    
    dfs(routes, path);
    return result;
  }
// 订单详情Tab页面统一样式
.order-tab-container {
  padding: 16px;
  background-color: #f8fafc;
  min-height: 100%;

  // 统一的加载和错误状态
  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &.small {
      height: 120px;
      margin: 16px 0;
    }

    .retry-button {
      margin-left: 12px;
    }
  }

  .empty-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  // 统一的信息卡片样式
  .info-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      border-bottom: none;

      .action-buttons {
        display: flex;
        gap: 8px;

        .el-button {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: #fff;
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }

    .section-content {
      padding: 24px;
    }
  }

  // 统一的信息网格布局
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
      padding: 16px;
    }

    .info-item {
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        transform: translateY(-2px);
      }

      &.highlighted {
        background: linear-gradient(135deg, #667eea20, #764ba220);
        border-color: #667eea;
      }

      .info-label {
        font-size: 14px;
        color: #64748b;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .info-value {
        font-size: 16px;
        color: #1e293b;
        font-weight: 600;
        line-height: 1.5;

        &.important-amount {
          color: #059669;
          font-size: 18px;
        }

        &.highlight-date {
          color: #3b82f6;
        }
      }
    }
  }

  // 统一的卡片样式
  .summary-cards, .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 24px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      padding: 16px;
    }

    .summary-card, .overview-card {
      padding: 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 12px;
      border: 1px solid #e2e8f0;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      .card-label {
        font-size: 14px;
        color: #64748b;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .card-value {
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 4px;

        &.total-amount { color: #3b82f6; }
        &.paid-amount { color: #059669; }
        &.overdue-amount { color: #dc2626; }
        &.next-payment-amount { color: #7c3aed; }
        &.remaining-amount { color: #ea580c; }
        &.next-amount { color: #0891b2; }
      }

      .card-subtitle {
        font-size: 12px;
        color: #94a3b8;
      }
    }
  }

  // 统一的表格样式
  .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .el-table__header {
      th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        color: #374151;
        font-weight: 600;
        border-bottom: 2px solid #e5e7eb;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f8fafc;
      }

      td {
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }

  // 统一的按钮样式
  .el-button {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &.el-button--success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
      }
    }
  }

  // 统一的标签样式
  .el-tag {
    border-radius: 6px;
    font-weight: 500;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
  }

  // 统一的进度条样式
  .el-progress {
    .el-progress-bar__outer {
      border-radius: 10px;
      background-color: #f1f5f9;
    }

    .el-progress-bar__inner {
      border-radius: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  // 统一的时间轴样式
  .el-timeline {
    .el-timeline-item__wrapper {
      .el-timeline-item__timestamp {
        color: #64748b;
        font-size: 13px;
      }

      .timeline-content {
        .timeline-title {
          color: #1e293b;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .timeline-description {
          color: #64748b;
          line-height: 1.6;
        }
      }
    }
  }

  // 统一的分页样式
  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 24px 0;
    background: #fff;
    border-top: 1px solid #f3f4f6;
  }

  // 统一的空状态样式
  .empty-history, .empty-payments, .empty-attachments {
    text-align: center;
    padding: 40px 20px;
    color: #94a3b8;
    font-size: 14px;
    background: #f8fafc;
    border-radius: 8px;
    margin: 16px;
  }

  // 响应式设计
  @media (max-width: 1024px) {
    padding: 12px;

    .info-section {
      margin-bottom: 16px;

      .section-header {
        padding: 16px 20px;
        font-size: 15px;
      }

      .section-content {
        padding: 20px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 8px;

    .info-section {
      margin-bottom: 12px;
      border-radius: 8px;

      .section-header {
        padding: 12px 16px;
        font-size: 14px;
        flex-direction: column;
        gap: 8px;

        .action-buttons {
          width: 100%;
          justify-content: center;
        }
      }

      .section-content {
        padding: 16px;
      }
    }
  }
} 
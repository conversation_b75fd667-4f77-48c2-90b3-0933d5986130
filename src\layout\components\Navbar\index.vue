<template>
  <div class="navbar">
    <div class="navbar-left">
      <div class="logo">
        <img src="@/assets/logo.png" alt="logo" />
        <span class="title">EasyControl</span>
      </div>
    </div>
    <div class="navbar-right">
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="avatar-container">
          <img src="@/assets/avatar.png" class="avatar" />
          <span class="name">{{ userStore.name }}</span>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><user /></el-icon>
              {{ t('header.profile') }}
            </el-dropdown-item>
            <el-dropdown-item command="logout">
              <el-icon><switch-button /></el-icon>
              {{ t('header.logout') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { ArrowDown, User, SwitchButton } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm(t('header.logoutConfirm'), t('common.warning'), {
        type: 'warning'
      })
      await userStore.logout()
      router.push('/login')
    } catch (error) {
      // 用户取消操作
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: var(--el-color-primary);
  color: #fff;

  .navbar-left {
    display: flex;
    align-items: center;

    .logo {
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        height: 32px;
        margin-right: 8px;
      }

      .title {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        white-space: nowrap;
      }
    }
  }

  .navbar-right {
    display: flex;
    align-items: center;

    .avatar-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .name {
        font-size: 14px;
        margin-right: 4px;
      }

      .el-icon {
        font-size: 12px;
        margin-left: 4px;
      }
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}
</style> 
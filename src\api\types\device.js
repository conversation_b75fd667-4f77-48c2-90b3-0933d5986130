/**
 * @typedef {Object} DeviceAttachment
 * @property {string} id - 附件ID
 * @property {string} fileName - 文件名
 * @property {string} fileUrl - 文件URL
 * @property {string} [thumbnailUrl] - 缩略图URL（后端返回）
 * @property {number} [fileSize] - 文件大小（字节，后端返回）
 * @property {string} [fileType] - 文件类型（后端返回）
 * @property {string} [uploadTime] - 上传时间
 * @property {string} [createTime] - 创建时间
 * @property {string} [updateTime] - 更新时间
 */

/**
 * @typedef {Object} DeviceDTO
 * @property {string} id - 设备ID
 * @property {number} enterpriseId - 企业ID
 * @property {string} brand - 品牌
 * @property {string} model - 型号
 * @property {string} deviceName - 设备名称
 * @property {string} deviceNo - 设备编号
 * @property {string} color - 颜色
 * @property {string} sn - 序列号
 * @property {string} imei1 - IMEI1
 * @property {string} imei2 - IMEI2
 * @property {string} status - 设备状态
 * @property {boolean} syncStatus - 同步状态
 * @property {string} remark - 备注
 * @property {number} costAmount - 成本金额
 * @property {Array<DeviceAttachment>} attachments - 附件列表
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 * @property {Object} [relatedOrder] - 关联订单信息
 */

/**
 * @typedef {Object} DeviceQuery
 * @property {number} current - 当前页码
 * @property {number} size - 每页数量
 * @property {string} [brand] - 品牌
 * @property {string} [model] - 型号
 * @property {string} [deviceName] - 设备名称
 * @property {string} [status] - 设备状态
 * @property {string} [sn] - 序列号
 * @property {string} [imei1] - IMEI1
 * @property {string} [createTimeStart] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 */

/**
 * @typedef {Object} DeviceCreateDTO
 * @property {string} brand - 品牌
 * @property {string} model - 型号
 * @property {string} deviceName - 设备名称
 * @property {string} color - 颜色
 * @property {string} sn - 序列号
 * @property {string} imei1 - IMEI1
 * @property {string} [imei2] - IMEI2
 * @property {string} status - 设备状态
 * @property {boolean} syncStatus - 同步状态
 * @property {string} [remark] - 备注
 * @property {number} costAmount - 成本金额
 * @property {Array<{fileName: string, fileUrl: string}>} [attachments] - 附件列表
 */

/**
 * @typedef {Object} DeviceUpdateDTO
 * @property {string} id - 设备ID
 * @property {string} brand - 品牌
 * @property {string} model - 型号
 * @property {string} deviceName - 设备名称
 * @property {string} color - 颜色
 * @property {string} sn - 序列号
 * @property {string} imei1 - IMEI1
 * @property {string} [imei2] - IMEI2
 * @property {string} status - 设备状态
 * @property {boolean} syncStatus - 同步状态
 * @property {string} [remark] - 备注
 * @property {number} costAmount - 成本金额
 * @property {Array<{fileName: string, fileUrl: string}>} [attachments] - 附件列表
 */

export const DEVICE_STATUS = {
  IN_STOCK: '在库',
  IN_USE: '使用中',
  UNDER_REPAIR: '维修中',
  IN_TRANSIT: '物流中',
  SCRAPPED: '报废',
  LOST: '丢失',
  PREPARING: '准备中',
  SOLD: '已售'
}

export const DEVICE_STATUS_OPTIONS = [
  { label: '在库', value: 'IN_STOCK' },
  { label: '使用中', value: 'IN_USE' },
  { label: '维修中', value: 'UNDER_REPAIR' },
  { label: '物流中', value: 'IN_TRANSIT' },
  { label: '报废', value: 'SCRAPPED' },
  { label: '丢失', value: 'LOST' },
  { label: '准备中', value: 'PREPARING' },
  { label: '已售', value: 'SOLD' }
]

export const DEVICE_BRANDS = {
  Apple: '苹果',
  Samsung: '三星',
  Huawei: '华为',
  Xiaomi: '小米',
  OPPO: 'OPPO',
  vivo: 'vivo',
  other: '其他'
}

export const DEVICE_BRAND_OPTIONS = [
  { label: '苹果', value: 'Apple' },
  { label: '三星', value: 'Samsung' },
  { label: '华为', value: 'Huawei' },
  { label: '小米', value: 'Xiaomi' },
  { label: 'OPPO', value: 'OPPO' },
  { label: 'vivo', value: 'vivo' },
  { label: '其他', value: 'other' }
]

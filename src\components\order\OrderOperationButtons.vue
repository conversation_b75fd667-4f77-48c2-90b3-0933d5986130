<template>
  <div class="order-operation-buttons">
    <!-- 详情按钮 -->
    <el-button 
      type="primary" 
      link 
      size="small"
      @click="handleViewDetail"
    >
      {{ $t('common.details') }}
    </el-button>

    <!-- 更多操作下拉菜单 -->
    <el-dropdown trigger="click" @command="handleCommand">
      <el-button 
        type="primary" 
        link 
        size="small"
        class="more-btn"
      >
        {{ $t('common.more') }}
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="updateStatus">
            <el-icon><refresh /></el-icon>
            {{ $t('order.actions.updateStatus') }}
          </el-dropdown-item>
          <el-dropdown-item command="attachments">
            <el-icon><document /></el-icon>
            {{ $t('order.actions.attachments') }}
          </el-dropdown-item>
          <el-dropdown-item command="payment">
            <el-icon><money /></el-icon>
            {{ $t('order.actions.payment') }}
          </el-dropdown-item>
          <el-dropdown-item command="delete" divided>
            <el-icon><delete /></el-icon>
            {{ $t('common.delete') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ArrowDown, Refresh, Document, Money, Delete } from '@element-plus/icons-vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  order: {
    type: Object,
    required: true
  }
});

const emit = defineEmits([
  'view-detail', 
  'update-status', 
  'view-attachments', 
  'make-payment',
  'delete'
]);

// 查看详情
const handleViewDetail = () => {
  emit('view-detail', props.order);
};

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'updateStatus':
      emit('update-status', props.order);
      break;
    case 'attachments':
      emit('view-attachments', props.order);
      break;
    case 'payment':
      emit('make-payment', props.order);
      break;
    case 'delete':
      emit('delete', props.order);
      break;
  }
};
</script>

<style lang="scss" scoped>
.order-operation-buttons {
  display: flex;
  align-items: center;
  
  .more-btn {
    margin-left: 12px;
  }
}
</style> 
﻿# .\.commit.ps1 "Your commit message"

param (
    [Parameter(Mandatory=$false, Position=0)]
    [string]$Message
)

# 如果没有提供提交信息，则提示用户输入
if ([string]::IsNullOrEmpty($Message)) {
    $Message = Read-Host "请输入您的 Commit 信息"
    if ([string]::IsNullOrEmpty($Message)) {
        Write-Host "Commit 信息不能为空，操作已中止。" -ForegroundColor Red
        exit 1
    }
}

Write-Host "正在暂存所有更改..." -ForegroundColor Cyan
git add .

# 检查是否有文件被暂存以进行提交
$StagedChanges = git diff --staged --quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "没有需要提交的更改。工作目录是干净的，或者您需要添加新文件。" -ForegroundColor Yellow
    exit 0
}

Write-Host "正在提交，信息为: '$Message'..." -ForegroundColor Cyan
git commit -m "$Message"

if ($LASTEXITCODE -ne 0) {
    Write-Host "Git commit 失败。请解决问题后重试。" -ForegroundColor Red
    exit 1
}

Write-Host "正在推送到远程仓库..." -ForegroundColor Cyan
git push

if ($LASTEXITCODE -eq 0) {
    Write-Host "成功提交并推送到远程仓库。" -ForegroundColor Green
} else {
    Write-Host "Git push 失败。请检查您的网络连接和远程仓库状态。" -ForegroundColor Red
} 
import { defineStore } from 'pinia';
// import { asyncRoutes, constantRoutes } from '@/router';

/**
 * 使用meta.role来确定当前用户是否有权限
 * @param {Array} roles 用户角色
 * @param {Object} route 路由
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role));
  }
  // 如果没有设置权限，则视为所有人可访问
  return true;
}

/**
 * 递归过滤异步路由表
 * @param {Array} routes 异步路由
 * @param {Array} roles 用户角色
 */
function filterAsyncRoutes(routes, roles) {
  const res = [];
  
  routes.forEach(route => {
    const tmp = { ...route };
    
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      res.push(tmp);
    }
  });
  
  return res;
}

export const usePermissionStore = defineStore({
  id: 'pure-permission',
  state: () => {
    return {
      // 所有路由
      routes: [],
      // 动态路由
      addRoutes: [],
      // 完整菜单
      wholeMenus: [],
      // 需要缓存的页面
      cachePageList: []
    };
  },
  actions: {
    /**
     * 生成路由
     * @param {Array} roles 用户角色
     */
    generateRoutes(roles) {
      return new Promise(resolve => {
        let accessedRoutes;
        
        if (roles.includes('admin')) {
          // 管理员可以访问所有路由
          accessedRoutes = asyncRoutes || [];
        } else {
          // 根据角色过滤路由
          accessedRoutes = filterAsyncRoutes(asyncRoutes, roles);
        }
        
        // 更新路由
        this.addRoutes = accessedRoutes;
        this.routes = constantRoutes.concat(accessedRoutes);
        
        // 生成菜单
        this.wholeMenus = this.handleWholeMenus(this.routes);
        
        resolve(accessedRoutes);
      });
    },
    
    /**
     * 处理完整菜单
     * @param {Array} routes 路由列表
     */
    handleWholeMenus(routes) {
      const menus = [];
      
      routes.forEach(route => {
        // 跳过不需要显示在菜单中的路由
        if (route.meta && route.meta.hideInMenu) {
          return;
        }
        
        const menu = {
          path: route.path,
          name: route.name,
          meta: route.meta,
          children: route.children ? this.handleWholeMenus(route.children) : []
        };
        
        // 只有当菜单项有标题或有子项时才添加
        if ((menu.meta && menu.meta.title) || menu.children.length > 0) {
          menus.push(menu);
        }
      });
      
      return menus;
    },
    
    /**
     * 设置需要缓存的页面
     * @param {Array} list 组件名称列表
     */
    setCachePageList(list) {
      this.cachePageList = list;
    }
  }
});
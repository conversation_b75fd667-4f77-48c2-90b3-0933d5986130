<template>
  <div class="customer-info-tab">
    <div v-if="props.isLoading" class="loading-container">
      <el-progress 
        type="circle" 
        :width="24" 
        :stroke-width="3" 
        :percentage="100" 
        status="success" 
        :indeterminate="true"
        :duration="1"
        class="custom-loader"
      />
      <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
    </div>

    <div v-else-if="props.loadingError" class="error-container">
      <span>{{ $t('common.fetchFailed') }}</span>
      <el-button type="danger" :icon="Refresh" @click="handleRetryFetch" plain size="small" class="retry-button">
        {{ $t('common.retry') }}
      </el-button>
    </div>

    <div v-else-if="customerInfo" class="customer-info-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-header">{{ $t('customer.basicInfo') }}</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="$t('customer.name')">
            {{ computedFullName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.idType')">
            {{ idTypeLabel }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.idNumber')">
            {{ customerInfo.idNumber || '--' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.phone')">
            {{ customerInfo.phone }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.email')" :span="2">
            {{ customerInfo.email }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.residenceAddress')" :span="2">
            {{ customerInfo.licenseAddress || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('customer.mailingAddress')" :span="2">
            {{ customerInfo.contactAddress || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 附件信息 -->
      <div class="info-section attachment-section">
        <div class="section-header">{{ $t('customer.details.attachments') }}</div>
        <AttachmentList 
          :attachments="customerInfo.attachments"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </div>
    </div>

    <div v-else class="empty-data">
      <el-empty :description="$t('common.noData')" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Refresh } from '@element-plus/icons-vue';
import AttachmentList from '@/components/common/AttachmentList.vue';
import { getDictFields } from '@/api/dictionary';

const { t } = useI18n();

const props = defineProps({
  customerInfo: {
    type: Object,
    default: () => null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);
const localIsLoading = ref(props.isLoading);
const localLoadingError = ref(props.loadingError);
const idTypeOptions = ref([]);

const handleRetry = () => {
  emit('retry-fetch');
};

const computedFullName = computed(() => {
  if (!props.customerInfo) return '-';
  const firstName = props.customerInfo.firstName?.replace('_', '') || '';
  const lastName = props.customerInfo.lastName?.replace('_', '') || '';
  if (firstName && lastName) {
    return `${lastName}${firstName}`;
  }
  return firstName || lastName || '-';
});

const idTypeLabel = computed(() => {
  if (!props.customerInfo || !props.customerInfo.idType) {
    return props.customerInfo?.idType || '--';
  }
  const option = idTypeOptions.value.find(opt => opt.value === props.customerInfo.idType);
  return option ? option.label : props.customerInfo.idType;
});

onMounted(async () => {
  try {
    const response = await getDictFields({ module: 'CUSTOMER', fieldCode: 'id_type' });
    if (response.data && response.data.records && response.data.records.length > 0) {
      idTypeOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch id_type options:", error);
  }
});

// Watch for prop changes
watch(() => props.isLoading, (newVal) => {
  localIsLoading.value = newVal;
});

watch(() => props.loadingError, (newVal) => {
  localLoadingError.value = newVal;
});

// 格式化证件类型
const formatIdType = (type) => {
  if (!type) return '-'
  
  const typeMap = {
    'ID_CARD': t('customer.idTypes.idCard'),
    'PASSPORT': t('customer.idTypes.passport'),
    'DRIVING_LICENSE': t('customer.idTypes.driverLicense')
  }
  
  return typeMap[type] || type
}

// 重试获取数据
const handleRetryFetch = () => {
  emit('retry-fetch')
}

</script>

<style lang="scss" scoped>
.customer-info-tab {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
.loading-container, .error-container, .empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #909399;
  font-size: 14px;
}
.customer-info-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.info-section {
  margin-bottom: 30px;
}
.section-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}
/* El-Descriptions 样式调整 */
:deep(.el-descriptions) {
  border: 1px solid #ebeef5;
  border-right: none;
  border-bottom: none;
}
:deep(.el-descriptions__body) {
  background-color: #fff;
}
:deep(.el-descriptions-item) {
  border-left: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  padding: 12px 16px;
}
:deep(.el-descriptions-item__label) {
  background-color: #fcfcfc;
  color: #606266;
  font-weight: 500;
}
:deep(.el-descriptions-item__content) {
  color: #303133;
  word-break: break-word;
}
.attachment-section {
  margin-top: 30px;
}
</style> 
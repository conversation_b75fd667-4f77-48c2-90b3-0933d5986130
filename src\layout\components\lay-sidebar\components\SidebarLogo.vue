<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useSettingStore } from "@/stores/modules/settings";

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
});

const router = useRouter();
const settingStore = useSettingStore();

const title = computed(() => {
  return settingStore.title;
});

function goHome() {
  router.push("/");
}
</script>

<template>
  <div class="sidebar-logo-container" @click="goHome">
    <transition name="sidebarLogoFade">
      <div
        v-if="collapse"
        :class="['sidebar-logo-fade', 'sidebar-logo-wrapper']"
        key="collapse"
      >
        <img src="@/assets/logo.png" class="sidebar-logo" />
      </div>
      <div v-else :class="['sidebar-logo-fade', 'sidebar-title-wrapper']" key="expand">
        <img src="@/assets/logo.png" class="sidebar-logo" />
        <h1 class="sidebar-title">{{ title }}</h1>
      </div>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 48px;
  overflow: hidden;
  background: var(--el-menu-bg-color);
  text-align: center;
  transition: all 0.2s;

  & > div {
    height: 100%;
    transition: margin-left var(--pure-transition-duration);
  }

  .sidebar-logo-wrapper {
    padding-left: 16px;
    text-align: left;

    .sidebar-logo {
      width: 32px;
      height: 32px;
      margin-top: 8px;
    }
  }

  .sidebar-title-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 16px;

    .sidebar-logo {
      width: 32px;
      height: 32px;
    }

    .sidebar-title {
      display: inline-block;
      margin: 0 0 0 12px;
      color: var(--el-menu-text-color);
      font-weight: 600;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }
}

.sidebarLogoFade-enter-active,
.sidebarLogoFade-leave-active {
  transition: opacity 0.2s;
}
.sidebarLogoFade-enter-from,
.sidebarLogoFade-leave-to {
  opacity: 0;
}
</style>

import request from '@/utils/request';
import { 
  OrderTypes,
  OrderStatus, 
  PaymentPlanStatus, 
  PaymentPlanType, 
  PenaltyType 
} from './types/order';

// 修正API前缀，加上crm-service前缀用于后端URL分配
const API_PREFIX = '/crm-service/api/orders';

/**
 * 获取订单列表 - 使用标准化参数
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.size - 每页数量
 * @param {string} [params.orderId] - 订单号
 * @param {string} [params.customerName] - 客户名称
 * @param {string} [params.customerPhone] - 客户电话
 * @param {string} [params.serialNumber] - 序列号
 * @param {string} [params.imei1] - IMEI1
 * @param {string} [params.imei2] - IMEI2
 * @param {string} [params.orderType] - 订单类型
 * @param {string} [params.orderStatus] - 订单状态
 * @param {string} [params.financialStatus] - 信贷状态
 * @param {string} [params.dueDateType] - 到期时间类型
 * @param {string} [params.dueDateCompareType] - 到期时间比较类型
 * @param {string} [params.dueDate] - 到期时间
 * @param {string} [params.firstPaymentDateStart] - 首付日期开始
 * @param {string} [params.firstPaymentDateEnd] - 首付日期结束
 * @returns {Promise} - 返回订单分页数据
 */
export function getOrderList(params) {
  console.log('[OrderAPI] getOrderList called with params:', JSON.parse(JSON.stringify(params)));
  
  return request({
    url: `${API_PREFIX}`,
    method: 'get',
    params
  });
}

/**
 * 获取订单详情
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回订单详情数据
 */
export function getOrderDetail(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'get'
  });
}

/**
 * 根据订单号获取订单详情
 * @param {string} orderNo - 订单号
 * @returns {Promise} - 返回订单详情数据
 */
export function getOrderDetailByOrderNo(orderNo) {
  // orderid是path上传的，不是query参数
  return request({
    url: `${API_PREFIX}/${orderNo}`,
    method: 'get'
  });
}

/**
 * 创建订单
 * @param {Object} data - 订单数据
 * @returns {Promise} - 返回创建结果
 */
export function createOrder(data) {
  return request({
    url: `${API_PREFIX}`,
    method: 'post',
    data
  });
}

/**
 * 更新订单
 * @param {string} id - 订单ID
 * @param {Object} data - 订单更新数据
 * @returns {Promise} - 返回更新结果
 */
export function updateOrder(id, data) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除订单
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteOrder(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'delete'
  });
}

/**
 * 取消订单
 * @param {string} id - 订单ID
 * @param {Object} data - 取消原因等数据
 * @returns {Promise} - 返回取消结果
 */
export function cancelOrder(id, data) {
  return request({
    url: `${API_PREFIX}/${id}/cancel`,
    method: 'post',
    data
  });
}

/**
 * 更新订单状态
 * @param {string} id - 订单ID
 * @param {string} status - 新状态
 * @param {Object} data - 附加数据
 * @returns {Promise} - 返回状态更新结果
 */
export function updateOrderStatus(id, status, data = {}) {
  return request({
    url: `${API_PREFIX}/${id}/status`,
    method: 'post',
    data: { status, ...data }
  });
}

/**
 * 获取订单统计数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回订单统计数据
 */
export function getOrderStatistics(params) {
  return request({
    url: `${API_PREFIX}/statistics`,
    method: 'get',
    params
  });
}

/**
 * 获取订单付款记录
 * @param {string|number} id - 订单ID或订单号
 * @param {Object} params - 查询参数
 * @param {string} [params.type] - 交易类型
 * @param {string} [params.status] - 交易状态
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise} - 返回订单付款记录
 */
export function getOrderPayments(id, params = {}) {
  // 确保id参数是字符串类型
  const safeId = id ? id.toString() : '';
  
  if (!safeId) {
    console.error('getOrderPayments: 无效的订单ID');
    return Promise.reject(new Error('无效的订单ID'));
  }
  
  // 检查是否为有效路径参数
  if (safeId.includes('/') || safeId.includes('?') || safeId.includes('&')) {
    console.warn('getOrderPayments: 订单ID包含特殊字符，可能影响URL构建:', safeId);
  }
  
  console.log(`获取付款记录，使用URL: ${API_PREFIX}/${safeId}/payments, 参数:`, params);
  
  return request({
    url: `${API_PREFIX}/${safeId}/payments`,
    method: 'get',
    params: params
  }).then(response => {
    // 记录响应用于调试
    if (!response || response.code !== 1000) {
      console.warn('getOrderPayments 响应错误:', response);
    }
    return response;
  }).catch(error => {
    console.error('getOrderPayments 请求失败:', error);
    throw error;
  });
}

/**
 * 获取订单历史记录
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回订单历史记录
 */
export function getOrderHistory(id) {
  return request({
    url: `/crm-service/api/order-history/${id}`,
    method: 'get'
  });
}

/**
 * 导出订单历史记录为Excel
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回导出结果
 */
export function exportOrderHistoryToExcel(id) {
  return request({
    url: `/crm-service/api/order-history/export/excel/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 导出订单历史记录为CSV
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回导出结果
 */
export function exportOrderHistoryToCSV(id) {
  return request({
    url: `/crm-service/api/order-history/export/csv/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 添加订单付款记录
 * @param {string|number} id - 订单ID
 * @param {Object} data - 付款数据
 * @returns {Promise} - 返回添加结果
 */
export function addOrderPayment(id, data) {
  // 确保id参数是字符串类型
  const safeId = id ? id.toString() : '';
  
  if (!safeId) {
    console.error('addOrderPayment: 无效的订单ID');
    return Promise.reject(new Error('无效的订单ID'));
  }
  
  // 检查是否为有效路径参数
  if (safeId.includes('/') || safeId.includes('?') || safeId.includes('&')) {
    console.warn('addOrderPayment: 订单ID包含特殊字符，可能影响URL构建:', safeId);
  }
  
  console.log(`添加付款记录，使用URL: ${API_PREFIX}/${safeId}/payments, 数据:`, data);
  
  return request({
    url: `${API_PREFIX}/${safeId}/payments`,
    method: 'post',
    data
  }).then(response => {
    // 记录响应用于调试
    if (!response || response.code !== 1000) {
      console.warn('addOrderPayment 响应错误:', response);
    }
    return response;
  }).catch(error => {
    console.error('addOrderPayment 请求失败:', error);
    throw error;
  });
}

/**
 * 完成订单
 * @param {Object} data - 订单数据
 * @returns {Promise} - 返回创建结果
 */
export function completeOrder(data) {
  return request({
    url: `${API_PREFIX}/complete`,
    method: 'post',
    data
  });
}

/**
 * 获取订单附件列表
 * @param {string} id - 订单ID
 * @returns {Promise} - 返回订单附件列表
 */
export function getOrderAttachments(id) {
  return request({
    url: `${API_PREFIX}/${id}/attachments`,
    method: 'get'
  });
}

/**
 * 上传订单附件
 * @param {string} id - 订单ID
 * @param {FormData} data - 附件数据
 * @returns {Promise} - 返回上传结果
 */
export function uploadOrderAttachment(id, data) {
  return request({
    url: `${API_PREFIX}/${id}/attachments`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除订单附件
 * @param {string} orderId - 订单ID
 * @param {string} attachmentId - 附件ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteOrderAttachment(orderId, attachmentId) {
  return request({
    url: `${API_PREFIX}/${orderId}/attachments/${attachmentId}`,
    method: 'delete'
  });
}

/**
 * 导出订单
 * @param {Object} params - 导出参数
 * @param {string} [params.orderId] - 订单号
 * @param {string} [params.customerName] - 客户名称
 * @param {string} [params.customerPhone] - 客户电话
 * @param {string} [params.serialNumber] - 序列号
 * @param {string} [params.imei1] - IMEI1
 * @param {string} [params.imei2] - IMEI2
 * @param {string} [params.orderType] - 订单类型
 * @param {string} [params.orderStatus] - 订单状态
 * @param {string} [params.financialStatus] - 信贷状态
 * @param {string} [params.format] - 导出格式 (excel/csv)
 * @returns {Promise} - 返回文件下载流
 */
export function exportOrders(params) {
  return request({
    url: `${API_PREFIX}/export`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 导出订单的支付明细
export function exportOrderPayments(orderId, params) {
  return request({
    url: `${API_PREFIX}/${orderId}/export-payments`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * Batch update order status
 * @param {Object} data - The request body
 * @param {Array<Number>} data.orderIds - List of internal database IDs of orders to update
 * @param {Boolean} [data.markAsBadDebt] - Mark as bad debt (true/false/null)
 * @param {Boolean} [data.recalculatePenaltyOnCancelBadDebt] - Recalculate penalty when un-marking bad debt
 * @param {Boolean} [data.markAsCompletedEarly] - Mark as completed early
 * @param {String} [data.newFinancialStatus] - New financial status
 * @param {String} [data.reason] - Reason for the update
 * @returns Promise
 */
export function batchUpdateOrderStatus(data) {
  return request({
    url: '/crm-service/api/orders/batch-status-update',
    method: 'post',
    data
  });
}

/**
 * 更新订单的附件列表 (替换操作)
 * @param {string|number} orderPrimaryId - 订单的主键ID
 * @param {Array<Object>} attachments - 附件对象数组, 每个对象包含 { fileName, fileUrl }
 * @returns {Promise} - 返回操作结果
 */
export function updateOrderAttachments(orderPrimaryId, attachments) {
  if (!orderPrimaryId) {
    console.error('updateOrderAttachments: 订单ID (orderPrimaryId) 不能为空');
    return Promise.reject(new Error('订单ID (orderPrimaryId) 不能为空'));
  }
  return request({
    url: `${API_PREFIX}/${orderPrimaryId}/attachments/update`,
    method: 'post',
    data: attachments // 直接发送附件数组作为请求体
  });
}

// 导出常量
export { OrderTypes, OrderStatus, PaymentPlanStatus, PaymentPlanType, PenaltyType };

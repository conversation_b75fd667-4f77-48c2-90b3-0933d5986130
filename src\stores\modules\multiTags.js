import { defineStore } from 'pinia';
import { storageLocal } from '@pureadmin/utils';
import { responsiveStorageNameSpace } from '@/config';

export const useMultiTagsStore = defineStore({
  id: 'pure-multiTags',
  state: () => {
    return {
      // 标签页列表
      tagList: storageLocal().getItem(`${responsiveStorageNameSpace()}tags`) || [],
      // 是否缓存标签页
      cacheTagList: true
    };
  },
  getters: {
    // 获取缓存名称列表
    keepAliveList() {
      return this.tagList.filter(item => item.meta.keepAlive).map(item => item.name);
    }
  },
  actions: {
    // 添加标签
    addTag(tag) {
      // 检查是否存在相同路径的标签
      const isExist = this.tagList.some(item => item.path === tag.path);
      if (!isExist) {
        this.tagList.push(tag);
        this.saveTagList();
      }
    },
    
    // 删除标签
    delTag(tag) {
      const index = this.tagList.findIndex(item => item.path === tag.path);
      if (index > -1) {
        this.tagList.splice(index, 1);
        this.saveTagList();
      }
    },
    
    // 关闭所有标签，只保留固定的
    delAllTag() {
      this.tagList = this.tagList.filter(item => item.meta.affix);
      this.saveTagList();
    },
    
    // 关闭其他标签，只保留当前和固定的
    delOtherTag(currentPath) {
      this.tagList = this.tagList.filter(item => item.path === currentPath || item.meta.affix);
      this.saveTagList();
    },
    
    // 保存标签列表到本地存储
    saveTagList() {
      if (this.cacheTagList) {
        storageLocal().setItem(`${responsiveStorageNameSpace()}tags`, this.tagList);
      }
    }
  }
});
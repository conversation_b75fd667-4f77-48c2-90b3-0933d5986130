<template>
  <el-dialog
    v-model="visible"
    :title="$t('system.assignPermissions')"
    width="700px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="permission-dialog"
  >
    <div class="permission-dialog-content" v-loading="loading">
      <div v-if="!loading && permissionData.length === 0" class="empty-data">
        {{ $t('system.noPermissionData') }}
      </div>
      
      <template v-else>
        <div class="permission-header">
          <div class="role-name">
            {{ $t('system.roleName') + ': ' + (role?.roleName || '') }}
          </div>
          <div class="actions">
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
            >
              {{ $t('system.allPermissions') }}
            </el-checkbox>
          </div>
        </div>
        
        <el-divider />
        
        <div class="permission-list">
          <el-collapse v-model="activeCategories" accordion>
            <el-collapse-item
              v-for="category in permissionCategories"
              :key="category.code"
              :title="category.name"
              :name="category.code"
            >
              <div class="category-permissions">
                <div class="category-header">
                  <el-checkbox
                    :indeterminate="isCategoryIndeterminate(category.code)"
                    v-model="categoryChecked[category.code]"
                    @change="(val) => handleCategoryCheckChange(val, category.code)"
                  >
                    {{ $t('common.selectAll') }}
                  </el-checkbox>
                </div>
                
                <el-divider />
                
                <div class="permission-items">
                  <el-checkbox-group v-model="selectedPermissions" @change="handlePermissionChange">
                    <div
                      v-for="item in getPermissionsByCategory(category.code)"
                      :key="item.id"
                      class="permission-item"
                    >
                      <el-checkbox :label="item.id">
                        <div class="permission-item-content">
                          <div class="permission-name">{{ item.name }}</div>
                          <div class="permission-desc" v-if="item.description">
                            {{ item.description }}
                          </div>
                        </div>
                      </el-checkbox>
                    </div>
                  </el-checkbox-group>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </template>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ $t('common.confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { getPermissionList, getRolePermissions, updateRolePermissions } from '@/api/role'
import { getUserInfo } from '@/utils/auth'

const { t } = useI18n()

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  role: {
    type: Object,
    default: () => ({})
  }
})

// 组件事件
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框可见性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 数据状态
const loading = ref(false)
const submitting = ref(false)
const activeCategories = ref([])
const permissionData = ref([])
const selectedPermissions = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(false)
const categoryChecked = reactive({})

// 权限分类
const permissionCategories = computed(() => {
  const categories = []
  const categoryMap = {}
  
  permissionData.value.forEach(item => {
    if (!item.category) return
    
    if (!categoryMap[item.category.code]) {
      categoryMap[item.category.code] = true
      categories.push({
        code: item.category.code,
        name: item.category.name
      })
    }
  })
  
  return categories
})

// 按分类获取权限
const getPermissionsByCategory = (categoryCode) => {
  return permissionData.value.filter(item => 
    item.category && item.category.code === categoryCode
  )
}

// 判断分类是否为部分选中状态
const isCategoryIndeterminate = (categoryCode) => {
  const categoryPermissions = getPermissionsByCategory(categoryCode)
  const categoryPermissionIds = categoryPermissions.map(item => item.id)
  const selectedCount = selectedPermissions.value.filter(id => 
    categoryPermissionIds.includes(id)
  ).length
  
  return selectedCount > 0 && selectedCount < categoryPermissions.length
}

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    const enterpriseId = getUserInfo().enterpriseId
    const [permissionRes, rolePermissionRes] = await Promise.all([
      getPermissionList(enterpriseId),
      getRolePermissions(props.role.id)
    ])
    
    // 处理权限列表数据
    if (permissionRes.code === 1000) {
      permissionData.value = permissionRes.data || []
      
      // 激活第一个分类
      if (permissionCategories.value.length > 0) {
        activeCategories.value = [permissionCategories.value[0].code]
      }
    } else {
      ElMessage.error(permissionRes.message || t('system.fetchFailed'))
    }
    
    // 处理角色权限数据
    if (rolePermissionRes.code === 1000) {
      selectedPermissions.value = rolePermissionRes.data.map(item => item.id) || []
      updateCheckAllStatus()
    } else {
      ElMessage.error(rolePermissionRes.message || t('system.fetchFailed'))
    }
  } catch (error) {
    console.error('获取权限数据失败:', error)
    ElMessage.error(t('system.fetchFailed'))
  } finally {
    loading.value = false
  }
}

// 更新全选状态
const updateCheckAllStatus = () => {
  const totalCount = permissionData.value.length
  const selectedCount = selectedPermissions.value.length
  
  checkAll.value = totalCount > 0 && selectedCount === totalCount
  isIndeterminate.value = selectedCount > 0 && selectedCount < totalCount
  
  // 更新分类选中状态
  permissionCategories.value.forEach(category => {
    const categoryPermissions = getPermissionsByCategory(category.code)
    const categoryPermissionIds = categoryPermissions.map(item => item.id)
    const selectedCategoryIds = selectedPermissions.value.filter(id => 
      categoryPermissionIds.includes(id)
    )
    
    categoryChecked[category.code] = selectedCategoryIds.length === categoryPermissions.length
  })
}

// 全选/取消全选
const handleCheckAllChange = (val) => {
  const allPermissionIds = permissionData.value.map(item => item.id)
  selectedPermissions.value = val ? allPermissionIds : []
  isIndeterminate.value = false
  
  // 更新分类选中状态
  permissionCategories.value.forEach(category => {
    categoryChecked[category.code] = val
  })
}

// 分类全选/取消全选
const handleCategoryCheckChange = (val, categoryCode) => {
  const categoryPermissions = getPermissionsByCategory(categoryCode)
  const categoryPermissionIds = categoryPermissions.map(item => item.id)
  
  // 先移除该分类所有权限
  selectedPermissions.value = selectedPermissions.value.filter(id => 
    !categoryPermissionIds.includes(id)
  )
  
  // 如果选中，则添加该分类所有权限
  if (val) {
    selectedPermissions.value = [...selectedPermissions.value, ...categoryPermissionIds]
  }
  
  updateCheckAllStatus()
}

// 权限选择变化
const handlePermissionChange = () => {
  updateCheckAllStatus()
}

// 提交权限
const handleSubmit = async () => {
  submitting.value = true
  try {
    const data = {
      roleId: props.role.id,
      permissionIds: selectedPermissions.value
    }
    
    const res = await updateRolePermissions(data)
    
    if (res.code === 1000) {
      ElMessage.success(t('system.savePermissionsSuccess'))
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.message || t('system.savePermissionsFailed'))
    }
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error(t('system.savePermissionsFailed'))
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听对话框打开
watch(visible, (val) => {
  if (val) {
    initData()
  }
})
</script>

<style lang="scss" scoped>
.permission-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.permission-dialog-content {
  max-height: 600px;
  overflow-y: auto;
  padding: 20px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  font-size: 14px;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.role-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.permission-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

.permission-item {
  padding: 5px 0;
}

.permission-item-content {
  display: flex;
  flex-direction: column;
}

.permission-name {
  font-size: 14px;
  color: #303133;
}

.permission-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

:deep(.el-collapse-item__header) {
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-collapse-item__content) {
  padding: 15px;
}

:deep(.el-checkbox) {
  margin-right: 25px;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

:deep(.el-checkbox__label) {
  padding-left: 10px;
}

:deep(.el-divider--horizontal) {
  margin: 15px 0;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px 20px;
}
</style> 
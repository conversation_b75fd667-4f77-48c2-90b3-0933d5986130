<template>
  <div class="account-management-container">
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ $t('menus.pureHome') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('menus.system') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('system.accountManagement') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" ref="searchFormRef" :inline="true" class="search-form">
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            :placeholder="$t('system.searchPlaceholder')"
            clearable
            style="width: 280px"
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.status" clearable :placeholder="$t('system.allStatus')" style="width: 180px">
            <el-option :label="$t('system.active')" value="1" />
            <el-option :label="$t('system.disabled')" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.accountType" clearable :placeholder="$t('system.allTypes')" style="width: 180px">
            <el-option :label="$t('system.trial')" value="0" />
            <el-option :label="$t('system.formal')" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            {{ $t('common.search') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
      <div class="search-tips" v-if="searchForm.keyword && accountList.length === 0" style="margin-top: 10px;">
        <el-alert
          type="info"
          :closable="false"
          :title="$t('system.searchResult')"
          :description="`'${searchForm.keyword}' ${$t('common.noData')}`"
          show-icon
        />
      </div>
    </div>

    <div class="toolbar">
      <el-button type="primary" :icon="Plus" @click="handleAdd" :loading="addLoading">
        {{ $t('system.addAccount') }}
      </el-button>
    </div>

    <div class="table-container" v-loading="loading">
      <el-table
        :data="accountList"
        border
        style="width: 100%"
        :empty-text="$t('common.noData')"
        highlight-current-row
        stripe
        show-overflow-tooltip
      >
        <el-table-column type="index" width="60" align="center" :label="$t('common.index')" />
        <el-table-column prop="username" :label="$t('system.username')" min-width="120" show-overflow-tooltip />
        <el-table-column prop="roleName" :label="$t('system.roleName')" min-width="120" show-overflow-tooltip />
        <el-table-column prop="email" :label="$t('system.email')" min-width="180" show-overflow-tooltip />
        <el-table-column prop="mobile" :label="$t('system.phoneNumber')" min-width="120" align="center" />
        <el-table-column :label="$t('system.status')" width="100" align="center">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.status === 1 ? $t('system.enabledStatus') : $t('system.disabledStatus') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.accountType')" width="130" align="center">
          <template #default="scope">
            <el-tag
              :type="scope.row.accountType === 1 ? 'primary' : 'warning'"
              size="small"
            >
              {{ scope.row.accountType === 1 ? $t('system.formal') : $t('system.trial') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('common.createTime')" min-width="170" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operations')" fixed="right" width="240" align="center" :show-overflow-tooltip="false">
          <template #default="scope">
            <el-button link type="primary" :icon="Edit" size="small" @click.stop="handleEdit(scope.row)" :loading="editLoading">{{ $t('common.edit') }}</el-button>
            <el-button link :type="scope.row.status === 1 ? 'danger' : 'success'" size="small" @click.stop="handleToggleStatus(scope.row)" :loading="scope.row.statusLoading">
              {{ scope.row.status === 1 ? $t('system.disableAccount') : $t('system.enableAccount') }}
            </el-button>
            <el-button link type="warning" :icon="Key" size="small" @click.stop="handleResetPassword(scope.row)" :loading="scope.row.resetLoading">{{ $t('system.resetPassword') }}</el-button>
            <el-button link type="danger" :icon="Delete" size="small" @click.stop="handleDelete(scope.row)" :loading="scope.row.deleteLoading">{{ $t('common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <div class="pagination-info">
        {{ $t('system.totalItems', { total: pagination.total }) }}
      </div>
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        :layout="`sizes, prev, pager, next, jumper, ${$t('customer.pagination.pageClassifier')}`"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #goto>
          {{ $t('customer.pagination.goto') }}
        </template>
        <template #jumper>
          {{ $t('customer.pagination.pageClassifier') }}
        </template>
        <template #sizes="{ item }">
          {{ item.value + $t('customer.pagination.pagesize') }}
        </template>
      </el-pagination>
    </div>

    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? $t('system.editAccount') : $t('system.addAccount')"
      width="700px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      class="account-dialog"
      destroy-on-close
      v-loading="formLoading"
    >
      <el-form
        :model="accountForm"
        :rules="accountRules"
        ref="accountFormRef"
        label-width="0"
        class="account-form"
        label-position="top"
      >
        <div class="form-title">{{ $t('system.basicInfo') }}</div>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="$t('system.username')" prop="username" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.username') }}</span>
              </div>
              <el-input
                v-model="accountForm.username"
                :placeholder="$t('system.usernameRequired')"
                maxlength="50"
                show-word-limit
                :disabled="isEdit"
              />
              <div class="form-tip" v-if="isEdit">{{ $t('system.usernameUnmodifiable') }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.email')" prop="email" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.email') }}</span>
              </div>
              <el-input
                v-model="accountForm.email"
                :placeholder="$t('system.emailRequired')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item :label="$t('system.phoneNumber')" prop="mobile" class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            <span>{{ $t('system.phoneNumber') }}</span>
          </div>
          <el-input
            v-model="accountForm.mobile"
            :placeholder="$t('system.phoneRequired')"
            maxlength="11"
          />
        </el-form-item>

        <template v-if="!isEdit">
          <div class="form-title">{{ $t('system.password') }}</div>
          
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item :label="$t('system.password')" prop="password" class="form-item">
                <div class="form-label">
                  <span class="required">*</span>
                  <span>{{ $t('system.password') }}</span>
                </div>
                <el-input
                  v-model="accountForm.password"
                  type="password"
                  :placeholder="$t('system.passwordRequired')"
                  show-password
                />
                <div class="form-tip">{{ $t('system.passwordPolicy') }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('system.confirmPassword')" prop="confirmPassword" class="form-item">
                <div class="form-label">
                  <span class="required">*</span>
                  <span>{{ $t('system.confirmPassword') }}</span>
                </div>
                <el-input
                  v-model="accountForm.confirmPassword"
                  type="password"
                  :placeholder="$t('system.confirmPasswordRequired')"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        
        <div class="form-title">{{ $t('system.accountSettings') }}</div>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="$t('system.passwordValidity')" prop="passwordValidity" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.passwordValidity') }}</span>
              </div>
              <el-select v-model="accountForm.passwordValidity" :placeholder="$t('system.validityRequired')" class="full-width">
                <el-option :label="`30${$t('system.days')}`" :value="30" />
                <el-option :label="`60${$t('system.days')}`" :value="60" />
                <el-option :label="`90${$t('system.days')}`" :value="90" />
                <el-option :label="`180${$t('system.days')}`" :value="180" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.roleName')" prop="roleId" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.roleName') }}</span>
              </div>
              <el-select v-model="accountForm.roleId" :placeholder="$t('system.roleRequired')" class="full-width">
                <el-option
                  v-for="role in roleOptions"
                  :key="role.roleId"
                  :label="role.roleName"
                  :value="role.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="$t('system.accountType')" prop="accountType" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.accountType') }}</span>
              </div>
            </el-form-item>
            <el-radio-group v-model="accountForm.accountType" class="account-type-group">
                <div class="radio-group-box">
                  <el-radio label="trial">{{ $t('system.trial') }}</el-radio>
                  <el-radio label="formal">{{ $t('system.formal') }}</el-radio>
                </div>
              </el-radio-group>
          </el-col>
          <el-col :span="12" v-if="isEdit">
            <el-form-item :label="$t('system.status')" prop="status" class="form-item">
              <div class="form-label">
                <span class="required">*</span>
                <span>{{ $t('system.status') }}</span>
              </div>
            </el-form-item>
            <el-radio-group v-model.number="accountForm.status" class="account-type-group">
                <div class="radio-group-box">
                  <el-radio :label="1">{{ $t('system.enabledStatus') }}</el-radio>
                  <el-radio :label="0">{{ $t('system.disabledStatus') }}</el-radio>
                </div>
              </el-radio-group>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ $t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
<!-- 
    <el-dialog
      v-model="resetPwdDialogVisible"
      :title="$t('system.resetPassword')"
      width="450px"
      :close-on-click-modal="false"
      class="reset-pwd-dialog"
      destroy-on-close
    >
      <el-form
        :model="resetPwdForm"
        :rules="resetPwdRules"
        ref="resetPwdFormRef"
        label-width="0"
        label-position="top"
      >
        <el-form-item :label="$t('system.newPassword')" prop="password" class="form-item">
          <el-input
            v-model="resetPwdForm.password"
            type="password"
            :placeholder="$t('system.inputNewPassword')"
            show-password
          />
          <div class="form-tip">{{ $t('system.passwordPolicy') }}</div>
        </el-form-item>
        
        <el-form-item :label="$t('system.confirmPassword')" prop="confirmPassword" class="form-item">
          <el-input
            v-model="resetPwdForm.confirmPassword"
            type="password"
            :placeholder="$t('system.confirmPasswordRequired')"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPwdDialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="submitResetPassword" :loading="submitting">
            {{ $t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Edit, Delete, Plus, Check, CircleClose, Key } from '@element-plus/icons-vue'
import { 
  getAccountList,
  createAccount,
  updateAccount,
  deleteAccount,
  updateAccountStatus,
  resetPassword
} from '@/api/account'
import { getRoleList } from '@/api/role'
import { formatDateTime } from '@/utils/format'
import { getUserInfo } from '@/utils/auth'

const { t } = useI18n()

let searchTimer = null

const searchForm = reactive({
  keyword: '',
  status: '',
  accountType: ''
})
const searchFormRef = ref()

const accountList = ref([])
const loading = ref(false)
let isRequesting = false

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)

const roleOptions = ref([])

const accountForm = reactive({
  id: '',
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  passwordValidity: 30,
  mobile: '',
  roleId: '',
  accountType: 'formal',
  status: 1,
  areaCode: ''
})
const accountFormRef = ref()

const resetPwdDialogVisible = ref(false)
const selectedAccount = ref(null)
const resetPwdForm = reactive({
  id: '',
  username: '',
})
const resetPwdFormRef = ref()

const addLoading = ref(false)
const editLoading = ref(false)
const formLoading = ref(false)

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(t('system.passwordRequired')));
  } else {
    // 密码应包含字母、数字，且长度至少为8位
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/;
    if (!passwordRegex.test(value)) {
      callback(new Error(t('system.passwordPolicy')));
    }
    callback();
  }
};

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('system.confirmPasswordRequired')))
  } else if (value !== accountForm.password) {
    callback(new Error(t('system.passwordNotMatch')))
  } else {
    callback()
  }
}

const validateResetConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('system.confirmPasswordRequired')))
  } else if (value !== resetPwdForm.password) {
    callback(new Error(t('system.passwordNotMatch')))
  } else {
    callback()
  }
}

const accountRules = reactive({
  username: [
    { required: true, message: t('system.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 50, message: t('system.usernameLength') || '用户名长度应在3-50个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('system.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('system.emailFormat'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('system.passwordRequired'), trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: t('system.confirmPasswordRequired'), trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  passwordValidity: [
    { required: true, message: t('system.validityRequired'), trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: t('system.phoneRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('system.phoneFormat'), trigger: 'blur' }
  ],
  roleId: [
    { required: true, message: t('system.roleRequired'), trigger: 'blur' }
  ],
  accountType: [
    { required: true, message: t('system.accountTypeRequired'), trigger: 'blur' }
  ],
  status: [
    { required: true, message: t('system.statusRequired'), trigger: 'blur' }
  ]
})

const resetPwdRules = reactive({
  password: [
    { required: true, message: t('system.passwordRequired'), trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: t('system.confirmPasswordRequired'), trigger: 'blur' },
    { validator: validateResetConfirmPassword, trigger: 'blur' }
  ]
})

const fetchAccountList = async () => {
  if (isRequesting) return
  
  isRequesting = true
  loading.value = true
  try {
    const params = {
      page: pagination.current > 0 ? pagination.current - 1 : 0,
      size: pagination.size,
      userName: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      accountType: searchForm.accountType || undefined
    }
    
    const res = await getAccountList(params)
    
    if (res.code === 1000) {
      accountList.value = (res.data.records || []).map(item => ({
        ...item,
        statusLoading: false,
        resetLoading: false,
        deleteLoading: false
      }))
      pagination.total = res.data.total || 0
      pagination.current = (res.data.current || 0)
      pagination.size = res.data.size || 10
    } else {
      ElMessage.error(res.message || t('system.fetchFailed'))
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    ElMessage.error(t('system.fetchFailed'))
  } finally {
    loading.value = false
    isRequesting = false
  }
}

const fetchRoleOptions = async () => {
  try {
    const currentEnterpriseId = getEnterpriseId()
    const params = { page: 0, size: 10000, enterpriseId: currentEnterpriseId }
    console.log('Fetching role options with params:', params)
    const res = await getRoleList(params)
    console.log('Role options response:', res)
    if (res.code === 1000 && res.data && res.data.records) {
      roleOptions.value = res.data.records || []
    } else {
      ElMessage.error(res.message || t('system.fetchFailed'))
      roleOptions.value = []
    }
  } catch (error) {
    console.error('获取角色选项失败:', error)
    ElMessage.error(t('system.fetchFailed'))
    roleOptions.value = []
  }
}

const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(() => {
    pagination.current = 1
    fetchAccountList()
  }, 300)
}

const handleReset = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchFormRef.value?.resetFields()
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.accountType = ''
  pagination.current = 1
  fetchAccountList()
}

const handleSizeChange = (val) => {
  pagination.size = val
  console.log('handleSizeChange', val)
  const maxPage = Math.ceil(pagination.total / val) || 1
  pagination.current = Math.min(pagination.current, maxPage)
  fetchAccountList()
}

const handleCurrentChange = (val) => {
  fetchAccountList()
}

const handleAdd = async () => {
  addLoading.value = true
  try {
    isEdit.value = false
    Object.assign(accountForm, {
      id: '',
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      passwordValidity: 30,
      mobile: '',
      roleId: '',
      accountType: 'formal',
      status: 1,
      areaCode: ''
    })
    // 每次打开弹窗时都强制获取最新的角色列表
    await fetchRoleOptions();
    dialogVisible.value = true
    setTimeout(() => {
      accountFormRef.value?.resetFields()
    }, 0)
  } finally {
    addLoading.value = false
  }
}

const handleEdit = async (row) => {
  editLoading.value = true
  try {
    isEdit.value = true
    await fetchRoleOptions()
    
    Object.assign(accountForm, {
      id: row.id,
      username: row.username,
      email: row.email,
      passwordValidity: row.period || 30,
      mobile: row.mobile,
      roleId: row.roleId,
      accountType: row.accountType === 0 ? 'trial' : 'formal',
      status: row.status,
      areaCode: row.areaCode || ''
    })
    dialogVisible.value = true
  } catch (error) {
    console.error('编辑账号时获取角色列表失败:', error)
    ElMessage.error(t('system.fetchRolesFailed'))
  } finally {
    editLoading.value = false
  }
}

const handleDialogClose = () => {
  accountFormRef.value?.resetFields()
  dialogVisible.value = false
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    t('system.deleteAccountConfirm'),
    t('system.deleteAccount'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      row.deleteLoading = true
      try {
        const res = await deleteAccount(row.id)
        
        if (res.code === 1000) {
          ElMessage.success(t('system.accountDeletedSuccess'))
          pagination.current = 1
          fetchAccountList()
        } else {
          ElMessage.error(res.message || t('system.accountDeletedFailed'))
        }
      } catch (error) {
        console.error('删除账号失败:', error)
        ElMessage.error(t('system.accountDeletedFailed'))
      } finally {
        row.deleteLoading = false
      }
    })
    .catch(() => {})
}

const handleToggleStatus = async (row) => {
  row.statusLoading = true
  try {
    const newStatus = row.status === 1 ? 0 : 1
    const res = await updateAccountStatus({
      id: row.id,
      status: newStatus,
      username: row.username,
    })
    
    if (res.code === 1000) {
      ElMessage.success(newStatus === 1 ? t('system.enableAccountSuccess') : t('system.disableAccountSuccess'))
      row.status = newStatus
    } else {
      ElMessage.error(res.message || t('system.statusUpdatedFailed'))
    }
  } catch (error) {
    console.error('更新账号状态失败:', error)
    ElMessage.error(t('system.statusUpdatedFailed'))
  } finally {
    row.statusLoading = false
  }
}

const handleResetPassword = async(row) => {
   console.log('handleResetPassword', row)
   submitting.value = true
    try {
      const res = await resetPassword({
        id: row.id,
        username: row.username
      })
      
      if (res.code === 1000) {
        ElMessage.success(t('system.passwordResetSuccess'))
        resetPwdDialogVisible.value = false
      } else {
        ElMessage.error(res.message || t('system.passwordResetFailed'))
      }
    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error(t('system.passwordResetFailed'))
    } finally {
      submitting.value = false
    }
}

// const submitResetPassword = async () => {
//   resetPwdFormRef.value?.validate(async (valid) => {
//     if (!valid) return
    
 
//   })
// }

const submitForm = async () => {
  accountFormRef.value?.validate(async (valid) => {
    if (!valid) return;

    formLoading.value = true;
    submitting.value = true;
    try {
      const currentEnterpriseId = getEnterpriseId();

      let payload = {
        id: accountForm.id,
        username: accountForm.username,
        enterpriseId: currentEnterpriseId,
        roleId: accountForm.roleId,
        period: accountForm.passwordValidity,
        email: accountForm.email,
        status: accountForm.status,
        mobile: accountForm.mobile,
        accountType: accountForm.accountType === 'trial' ? 0 : 1,
        areaCode: accountForm.areaCode,
      };

      let res;
      if (isEdit.value) {
        res = await updateAccount(payload);
      } else {
        const createPayload = {
          ...payload,
          password: accountForm.password,
          userType: 2,
        };
        delete createPayload.id;
        res = await createAccount(createPayload);
      }

      if (res.code === 1000) {
        ElMessage.success(isEdit.value ? t('system.accountUpdatedSuccess') : t('system.accountCreatedSuccess'));
        dialogVisible.value = false;
        pagination.current = 1;
        fetchAccountList();
      } else {
        ElMessage.error(res.message);
      }
    } catch (error) {
      console.error('操作账号失败:', error);
      // ElMessage.error(isEdit.value ? t('system.accountUpdatedFailed') : t('system.accountCreatedFailed'));
    } finally {
      formLoading.value = false;
      submitting.value = false;
    }
  });
};

const getEnterpriseId = () => {
  try {
    const userInfo = getUserInfo()
    if (userInfo && userInfo.enterpriseId) {
      let id = userInfo.enterpriseId
      if (typeof id === 'string') {
        id = parseInt(id, 10)
        if (isNaN(id)) {
          console.warn('Enterprise ID is not a valid number from userInfo:', userInfo.enterpriseId)
          return 1 // Default or error case
        }
      }
      return id
    } else {
      console.warn('Enterprise ID not found in userInfo, using default 1.')
      return 1 // Default if not found
    }
  } catch (error) {
    console.error('Failed to get enterprise ID:', error)
    return 1 // Default on error
  }
}

onBeforeUnmount(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})

onMounted(() => {
  fetchAccountList()
  fetchRoleOptions()
})
</script>

<style lang="scss" scoped>
.account-management-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;

  .breadcrumb {
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .search-bar {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 10px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    flex-grow: 1;
    overflow-x: auto;

    .el-table {
      border-radius: 4px;
      overflow: hidden;

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }

      td {
        padding: 8px 0;
      }
      .el-button--small {
        padding-top: 0;
        padding-bottom: 0;
      }
      .el-tag--small {
        padding: 0 8px;
        height: 24px;
        line-height: 22px;
      }
    }
  }
  
  .pagination-container {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .pagination-info {
      color: #606266;
      font-size: 14px;
    }
  }
}

.account-dialog, .reset-pwd-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    text-align: right;
  }
}

.account-form {
  .form-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    margin-top: 20px;
    &:first-child {
      margin-top: 0;
    }
  }
  
  .form-item {
    margin-bottom: 20px;
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 12px;
      color: #606266;
      font-weight: 500;
      .required {
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    :deep(.el-form-item__label) {
      display: none;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    padding-top: 4px;
  }

  .full-width {
    width: 100%;
  }

  .account-type-group {
    display: block;
    clear: both;
    margin-top: -20px;
    .radio-group-box {
      display: flex;
      align-items: center;
      gap: 20px;
    }
    :deep(.el-radio) {
      margin-right: 0;
    }
  }
}

.reset-pwd-header {
  text-align: center;
  margin-bottom: 20px;
  p {
    margin: 4px 0;
    font-size: 14px;
    color: #606266;
  }
  .reset-pwd-username {
    font-size: 16px;
    font-weight: 600;
    color: #409EFF;
    margin-top: 8px;
  }
}

.dialog-footer {
  .el-button {
    min-width: 80px;
  }
}
</style> 
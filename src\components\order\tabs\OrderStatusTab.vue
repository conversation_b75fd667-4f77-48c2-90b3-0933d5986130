<template>
  <div class="order-status-tab">
    <div v-if="props.isLoading" class="loading-container">
      <el-progress 
        type="circle" 
        :width="24" 
        :stroke-width="3" 
        :percentage="100" 
        status="success" 
        :indeterminate="true" 
        :duration="1"
        class="custom-loader"
      />
      <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
    </div>

    <div v-else-if="props.loadingError" class="error-container">
      <span>{{ $t('common.fetchFailed') }}</span>
      <el-button type="danger" :icon="Refresh" @click="handleRetryFetch" plain size="small" class="retry-button">
        {{ $t('common.retry') }}
      </el-button>
    </div>

    <div v-else-if="orderInfo" class="order-status-content">
      <!-- 订单状态与进度 -->
      <div class="info-section">
        <div class="section-header">{{ $t('order.statusAndProgress') }}</div>
        
        <div class="overview-grid">
          <div class="grid-item">
            <div class="item-label">{{ $t('order.orderDate') }}</div>
            <div class="item-value">{{ formatDate(orderInfo.orderDate || orderInfo.createTime) }}</div>
          </div>
          <div class="grid-item">
            <div class="item-label">{{ $t('order.orderStatus') }}</div>
            <div class="item-value">
              <el-tag v-if="orderInfo.status || orderInfo.orderStatus" 
                      :type="getOrderStatusType(orderInfo.status || orderInfo.orderStatus)" 
                      size="medium">
                {{ formatOrderStatus(orderInfo.status || orderInfo.orderStatus) }}
                <span v-if="getStatusLower(orderInfo.status || orderInfo.orderStatus) === 'normal'" class="days-info">
                  {{ orderInfo.daysCount || 0 }} {{ $t('order.list.days') }}
                </span>
                <span v-else-if="getStatusLower(orderInfo.status || orderInfo.orderStatus) === 'overdue'" class="days-info">
                  {{ orderInfo.daysCount || 0 }} {{ $t('order.list.days') }}
                </span>
              </el-tag>
              <span v-else>-</span>
            </div>
          </div>
          <div class="grid-item">
            <div class="item-label">{{ $t('order.financialStatusInfo') }}</div>
            <div class="item-value">
              <el-tag v-if="orderInfo.financialStatus" 
                      :type="getFinancialStatusType(orderInfo.financialStatus)" 
                      size="medium">
                {{ financialStatusLabel }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </div>
          <div class="grid-item">
            <div class="item-label">{{ $t('order.periodInfo') }}</div>
            <div class="item-value">
              <span v-if="orderInfo.totalInstallments > 0" class="period-value">
                <span>{{ $t('order.periodLease') }}&nbsp;</span>
                <span class="period-numbers">{{ orderInfo.currentInstallment || 0 }}/{{ orderInfo.totalInstallments }}</span>
              </span>
              <span v-else>-</span>
            </div>
          </div>
          <div class="grid-item progress-item">
            <div class="item-label">{{ $t('order.periodProgress') }}</div>
            <div class="item-value">
              <el-progress 
                :percentage="calculateProgressPercentage" 
                :stroke-width="10" 
                :show-text="false" 
                class="order-progress"
                :status="getProgressStatus(orderInfo.status)"
              />
              <span class="progress-text">{{ $t('order.completed') }}: {{ calculateProgressPercentage }}%</span>
            </div>
          </div>
          <div class="grid-item">
            <div class="item-label">{{ $t('order.nextPaymentDate') }}</div>
            <div class="item-value highlight-date">{{ formatDate(orderInfo.nextPaymentDate) }}</div>
          </div>
        </div>
      </div>

      <!-- 财务概览 -->
      <div class="info-section">
        <div class="section-header">{{ $t('order.financialOverview') }}</div>
        <div class="summary-boxes">
          <div class="summary-box">
            <div class="box-title">{{ $t('order.totalAmount') }}</div>
            <div class="box-value">¥{{ formatAmount(orderInfo.contractAmount || orderInfo.totalAmount) }}</div>
            <!-- <div class="box-subtitle">{{ $t('order.contractAmount') }}</div> -->
          </div>
          <div class="summary-box">
            <div class="box-title">{{ $t('order.paidAmount') }}</div>
            <div class="box-value paid-amount">¥{{ formatAmount(orderInfo.paidAmount) }}</div>
            <!-- <div class="box-subtitle">{{ calculatePaidPercentage }}%</div> -->
          </div>
          <div class="summary-box">
            <div class="box-title">{{ $t('order.remainingAmount') }}</div>
            <div class="box-value remaining-amount">¥{{ formatAmount(calculateRemainingAmount) }}</div>
            <!-- <div class="box-subtitle">{{ $t('order.includeOverdue') }}: ¥{{ formatAmount(orderInfo.overdueAmount || 0) }}</div> -->
          </div>
          <!-- <div class="summary-box">
            <div class="box-title">{{ $t('order.nextPaymentAmount') }}</div>
            <div class="box-value next-amount">¥{{ formatAmount(orderInfo.nextPaymentAmount || 0) }}</div>
            <div class="box-subtitle">{{ formatDate(orderInfo.nextPaymentDate) }}</div>
          </div> -->
        </div>
      </div>

      <!-- 订单历史记录 -->
      <div class="info-section">
        <div class="section-header">
          {{ $t('order.orderHistory') }}
          <div class="action-buttons">
            <el-dropdown trigger="click" @command="handleExportCommand">
              <el-button type="success" size="small" :icon="Download">
                {{ $t('common.export') }}
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="excel">{{ $t('order.actions.exportExcel') }}</el-dropdown-item>
                  <el-dropdown-item command="csv">{{ $t('order.actions.exportCSV') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div class="history-container">
          <div v-if="historyLoading" class="loading-container small">
            <el-progress 
              type="circle" 
              :width="20" 
              :stroke-width="2" 
              :percentage="100" 
              status="success" 
              :indeterminate="true"
              :duration="1"
              class="custom-loader"
            />
            <span style="margin-left: 8px; font-size: 14px;">{{ $t('common.loading') }}</span>
          </div>
          
          <div v-else-if="historyError" class="error-container small">
            <span>{{ $t('common.fetchFailed') }}</span>
            <el-button type="danger" :icon="Refresh" @click="fetchOrderHistory" plain size="small" class="retry-button">
              {{ $t('common.retry') }}
            </el-button>
          </div>
          
          <div v-else-if="activities.length > 0" class="history-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :type="getActivityType(activity.type)"
                :color="getActivityColor(activity.type)"
                :timestamp="formatDateTime(activity.timestamp)"
                :hollow="activity.hollow"
              >
                <div class="timeline-content">
                  <h4 class="timeline-title">{{ formatActivityType(activity.type, activity.eventType, activity.eventTypeDesc) }}</h4>
                  <p class="timeline-description">{{ activity.description }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-history">
            {{ $t('order.noHistoryRecords') }}
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-data">
      <el-empty :description="$t('common.noData')" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Refresh, Download, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getOrderHistory, exportOrderHistoryToExcel, exportOrderHistoryToCSV } from '@/api/order';
import { getDictFields } from '@/api/dictionary';

const { t } = useI18n();

const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  },
  orderInfo: {
    type: Object,
    default: () => null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);

// 活动历史数据
const activities = ref([]);
const historyLoading = ref(false);
const historyError = ref(false);
const financialStatusOptions = ref([]);

const financialStatusLabel = computed(() => {
  if (props.orderInfo && props.orderInfo.financialStatus) {
    const option = financialStatusOptions.value.find(opt => opt.value === props.orderInfo.financialStatus);
    return option ? option.label : props.orderInfo.financialStatus;
  }
  return '-';
});

// 通用文件下载函数，从response headers中获取文件名
const downloadFile = (response) => {
  if (!response || !response.data) {
    ElMessage.error(t('common.fileDownloadFailed'));
    return;
  }

  const blob = response.data;
  let filename = `order_history_${props.orderId}`;

  // 尝试从Content-Disposition获取文件名，采用更健壮的匹配模式
  const contentDisposition = response.headers['content-disposition'];
  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;"\n]*?)['";\n]?$/i);
    if (fileNameMatch && fileNameMatch[1]) {
      try {
        filename = decodeURIComponent(fileNameMatch[1]);
      } catch (e) {
        filename = fileNameMatch[1]; // 解码失败时使用原始值
        console.warn('Could not decode filename from Content-Disposition, using raw value:', filename);
      }
    } else {
       // 兼容简单的 filename="..." 格式
       const simpleMatch = contentDisposition.match(/filename="?(.+)"?/);
       if (simpleMatch && simpleMatch[1]) filename = simpleMatch[1];
    }
  }

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// 获取订单历史记录
const fetchOrderHistory = async () => {
  if (!props.orderId) return;
  
  historyLoading.value = true;
  historyError.value = false;
  
  try {
    const response = await getOrderHistory(props.orderId);
    
    if (response.code === 1000) {
      // 将API返回的历史数据转换为前端需要的格式
      activities.value = (response.data || []).map(item => ({
        type: mapEventTypeToActivityType(item.eventType),
        timestamp: item.createTime,
        description: item.description,
        operator: item.operatorName,
        eventLevel: item.eventLevel,
        eventType: item.eventType,
        eventTypeDesc: item.eventTypeDesc,
        hollow: false
      }));
      console.log('订单历史记录获取成功:', activities.value);
    } else {
      historyError.value = true;
      console.error('获取订单历史失败:', response.message);
    }
  } catch (error) {
    historyError.value = true;
    console.error('获取订单历史异常:', error);
  } finally {
    historyLoading.value = false;
  }
};

// 将API的eventType映射为前端的活动类型
const mapEventTypeToActivityType = (eventType) => {
  if (!eventType) return 'other';
  
  const typeMap = {
    'ORDER_CREATED': 'order',
    'PAYMENT_COMPLETED': 'payment',
    'PAYMENT_RECEIVED': 'payment',
    'STATUS_CHANGED': 'status',
    'ORDER_STATUS_CHANGED': 'status',
    'INSTALLMENT_COMPLETED': 'payment',
    'ORDER_COMPLETED': 'order',
    'ORDER_CANCELLED': 'order',
    'PAYMENT_OVERDUE': 'status',
    'PAYMENT_REMINDER': 'status'
  };
  
  return typeMap[eventType] || 'other';
};

// 处理导出命令
const handleExportCommand = async (command) => {
  console.log(props.orderInfo.orderId)
  if (!props.orderInfo.orderId) {
    ElMessage.warning(t('common.noData'));
    return;
  }
  
  try {
    let response;

    if (command === 'excel') {
      response = await exportOrderHistoryToExcel(props.orderInfo.orderId);
    } else if (command === 'csv') {
      response = await exportOrderHistoryToCSV(props.orderInfo.orderId);
    }
    
    // 检查响应是否为Blob类型，并处理文件下载
    if (response && response.data instanceof Blob) {
      downloadFile(response);
      ElMessage.success(t('order.actions.exportSuccess'));
    } else if (response && response.code && response.code !== 1000) {
      // 如果API返回了JSON格式的错误信息
      ElMessage.error(response.message || t('order.actions.exportFailed'));
    } else {
      // 其他未知错误，或者响应不是预期的Blob类型
      ElMessage.error(t('order.actions.exportFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('order.actions.exportFailed'));
  }
};

// 组件挂载时获取历史记录
onMounted(async () => {
  if (props.orderId) {
    fetchOrderHistory();
  }

  try {
    const response = await getDictFields({ module: 'ORDER', fieldCode: 'financial_status', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      financialStatusOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch financial status options:", error);
  }
});

// 重试获取数据
const handleRetryFetch = () => {
  emit('retry-fetch');
  if (props.orderId) {
    fetchOrderHistory();
  }
};

// 安全获取状态的小写形式
const getStatusLower = (status) => {
  return typeof status === 'string' ? status.toLowerCase() : '';
};

// 计算进度百分比
const calculateProgressPercentage = computed(() => {
  if (!props.orderInfo) return 0;

  // 如果订单已完成或已结清，显示100%进度
  const orderStatus = getStatusLower(props.orderInfo.status);
  const financialStatus = getStatusLower(props.orderInfo.financialStatus);

  if (orderStatus === 'completed' ||
      financialStatus === 'settled' ||
      financialStatus === '已结清') {
    return 100;
  }

  const currentInstallment = props.orderInfo.currentInstallment || 0;
  const totalInstallments = props.orderInfo.totalInstallments || 1;

  if (totalInstallments === 0) return 0;

  // 修正计算公式：(当前期数 - 1) / 总期数
  // 这样第1期时进度为0%，第2期时进度为1/总期数，最后一期时进度为100%
  const progressRatio = Math.max(0, currentInstallment - 1) / totalInstallments;
  return Math.floor(progressRatio * 100);
});

// 计算已付款百分比
const calculatePaidPercentage = computed(() => {
  if (!props.orderInfo) return 0;
  
  const paidAmount = props.orderInfo.paidAmount || 0;
  const totalAmount = props.orderInfo.contractAmount || props.orderInfo.totalAmount || 1;
  
  if (totalAmount === 0) return 0;
  
  return Math.floor((paidAmount / totalAmount) * 100);
});

// 计算剩余金额
const calculateRemainingAmount = computed(() => {
  if (!props.orderInfo) return 0;
  
  const totalAmount = props.orderInfo.contractAmount || props.orderInfo.totalAmount || 0;
  const paidAmount = props.orderInfo.paidAmount || 0;
  
  return Math.max(0, totalAmount - paidAmount);
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit' 
  });
};

// 格式化日期和时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化金额
const formatAmount = (amount) => {
  if (amount === undefined || amount === null) return '0.00';
  return Number(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 格式化订单状态
const formatOrderStatus = (status) => {
  if (!status) return t('common.unknown');
  const upperStatus = typeof status === 'string' ? status.toUpperCase() : '';
  // 直接使用 t() 和大写键名构造路径
  const translationKey = `order.status.${upperStatus}`;
  const translatedText = t(translationKey);
  // 如果翻译结果就是键名本身，说明可能没有对应翻译，可以返回原始状态或通用未知
  return translatedText === translationKey ? (upperStatus || t('common.unknown')) : translatedText;
};

// 获取订单状态类型
const getOrderStatusType = (status) => {
  if (!status) return 'info';
  const upperStatus = typeof status === 'string' ? status.toUpperCase() : '';
  const typeMap = {
    'ACTIVE': 'success',
    'NORMAL': 'success',
    'COMPLETED': 'info',
    'OVERDUE': 'danger',
    'CANCELLED': 'warning',
    'NOT_STARTED': 'info', // 假设
    'DEFAULTED': 'danger', // 假设
    'BAD_DEBT': 'danger' // 假设
  };
  return typeMap[upperStatus] || 'info';
};

// 格式化财务状态
const formatFinancialStatus = (status) => {
  if (!status) return t('common.unknown');
  const upperStatus = typeof status === 'string' ? status.toUpperCase() : '';
  // 直接使用 t() 和大写键名构造路径
  const translationKey = `order.financialStatus.${upperStatus}`;
  const translatedText = t(translationKey);
  // 如果翻译结果就是键名本身，说明可能没有对应翻译，可以返回原始状态或通用未知
  return translatedText === translationKey ? (upperStatus || t('common.unknown')) : translatedText;
};

// 获取财务状态类型
const getFinancialStatusType = (status) => {
  if (!status) return 'info';
  const upperStatus = typeof status === 'string' ? status.toUpperCase() : '';
  const typeMap = {
    'NORMAL': 'success',
    'OVERDUE': 'danger',
    'SETTLED': 'info',
    'COMPLETED': 'info',      // 对应 financialStatusOptions
    'NOT_STARTED': 'info',  // 对应 financialStatusOptions
    'IN_PROGRESS': 'primary', // 对应 financialStatusOptions
    'DEFAULTED': 'danger'   // 对应 financialStatusOptions
  };
  return typeMap[upperStatus] || 'info';
};

// 获取进度条状态
const getProgressStatus = (status) => {
  if (!status) return '';
  
  // 将状态转换为小写以便匹配
  const lowerStatus = typeof status === 'string' ? status.toLowerCase() : '';
  
  if (lowerStatus === 'overdue') return 'exception';
  if (lowerStatus === 'completed') return 'success';
  return '';
};

// 获取活动类型
const getActivityType = (type) => {
  if (!type) return 'primary';
  
  // 将类型转换为小写以便匹配
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  const typeMap = {
    'order': 'primary',
    'payment': 'success',
    'status': 'warning',
    'other': 'info'
  };
  
  return typeMap[lowerType] || 'primary';
};

// 获取活动颜色
const getActivityColor = (type) => {
  if (!type) return '#409EFF';
  
  // 将类型转换为小写以便匹配
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  const colorMap = {
    'order': '#409EFF', // 蓝色，系统相关
    'payment': '#67C23A', // 绿色，支付相关
    'status': '#E6A23C', // 黄色，状态变更
    'other': '#909399' // 灰色，其他操作
  };
  
  return colorMap[lowerType] || '#409EFF';
};

// 格式化活动类型
const formatActivityType = (type, eventType, eventTypeDesc) => {
  // 如果有eventTypeDesc，优先使用API返回的描述
  if (eventTypeDesc) {
    return eventTypeDesc;
  }
  
  // 如果有eventType，使用eventType进行翻译
  if (eventType) {
    const eventTypeMap = {
      'ORDER_CREATED': t('order.activity.created'),
      'PAYMENT_COMPLETED': t('order.activity.paymentReceived'),
      'PAYMENT_RECEIVED': t('order.activity.paymentReceived'),
      'STATUS_CHANGED': t('order.activity.statusChanged'),
      'ORDER_STATUS_CHANGED': t('order.activity.statusChanged'),
      'INSTALLMENT_COMPLETED': t('order.activity.paymentReceived'),
      'ORDER_COMPLETED': t('order.activity.completed'),
      'ORDER_CANCELLED': t('order.activity.cancelled'),
      'PAYMENT_OVERDUE': t('order.activity.paymentOverdue'),
      'PAYMENT_REMINDER': t('order.activity.paymentReminder')
    };
    
    if (eventTypeMap[eventType]) {
      return eventTypeMap[eventType];
    }
  }
  
  // 兜底使用原有的type映射
  if (!type) return t('order.activity.other');
  
  // 将类型转换为小写以便匹配
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  const typeMap = {
    'order': t('order.activity.created'),
    'payment': t('order.activity.paymentReceived'),
    'status': t('order.activity.statusChanged'),
    'other': t('order.activity.other')
  };
  
  return typeMap[lowerType] || t('order.activity.other');
};
</script>

<style lang="scss" scoped>
.order-status-tab {
  padding: 20px;
  
  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    
    &.small {
      height: auto;
      padding: 20px 0;
    }
    
    .retry-button {
      margin-left: 10px;
    }
  }
  
  .info-section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 16px;
      font-weight: 500;
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }
    
    .overview-grid {
      padding: 24px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: auto auto;
      gap: 32px 24px; /* row-gap column-gap */

      .grid-item {
        text-align: left;

        .item-label {
          color: #606266;
          font-size: 14px;
          margin-bottom: 10px;
        }

        .item-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;

          .period-value {
            font-weight: normal;
            font-size: 16px;
            .period-numbers {
              font-weight: 600;
              color: #409EFF;
            }
          }

          .el-tag {
            font-size: 13px;
            padding: 0 10px;
            height: 28px;
            line-height: 26px;
          }

          &.highlight-date {
            color: #E6A23C;
            font-weight: bold;
          }

          .days-info {
            font-size: 13px;
            margin-left: 6px;
            font-weight: 600;
          }
        }
        
        &.progress-item {
          .item-value {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: normal;
          }

          .order-progress {
            flex-grow: 1;
            width: 100%;
          }

          .progress-text {
            font-size: 14px;
            color: #606266;
            white-space: nowrap;
          }
        }
      }
    }
    
    .summary-boxes {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      padding: 24px;
      
      .summary-box {
        flex: 1;
        min-width: 180px;
        padding: 16px;
        background-color: #f9fafb;
        border-radius: 6px;
        text-align: center;
        
        .box-title {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .box-value {
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 4px;
          
          &.paid-amount {
            color: #67C23A;
          }
          
          &.remaining-amount {
            color: #F56C6C;
          }
          
          &.next-amount {
            color: #409EFF;
          }
        }
        
        .box-subtitle {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .history-container {
      padding: 16px;
      max-height: 300px;
      overflow-y: auto;
      padding-right: 10px;
      
      .history-timeline {
        padding: 8px 0;
        
        :deep(.el-timeline) {
          padding-left: 8px;
          
          .el-timeline-item__wrapper {
            padding-left: 16px;
            
            .timeline-content {
              .timeline-title {
                font-size: 15px;
                font-weight: 500;
                margin: 0 0 8px;
              }
              
              .timeline-description {
                margin: 0;
                color: #666;
                line-height: 1.5;
              }
            }
          }
        }
      }
      
      .empty-history {
        text-align: center;
        padding: 30px;
        color: #909399;
      }
    }
  }
  
  .empty-data {
    display: flex;
    justify-content: center;
    padding: 60px 0;
  }
}
</style> 
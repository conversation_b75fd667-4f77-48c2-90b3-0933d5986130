/**
 * 订单相关类型定义
 */

/**
 * 订单类型
 * @typedef {Object} Order
 * @property {string} id - 订单ID
 * @property {string} orderNo - 订单编号
 * @property {string} customerId - 客户ID
 * @property {string} customerName - 客户姓名
 * @property {string} status - 订单状态
 * @property {string} type - 订单类型
 * @property {string} startDate - 开始日期
 * @property {string} createdAt - 创建时间
 * @property {string} updatedAt - 更新时间
 * @property {number} totalAmount - 总金额
 * @property {OrderBasicInfo} basicInfo - 订单基本信息
 * @property {OrderCustomer} customer - 客户信息
 * @property {Array<OrderDevice>} devices - 设备信息
 * @property {OrderPayment} payment - 付款信息
 * @property {Array<OrderPaymentPlan>} paymentPlan - 付款计划
 */

/**
 * 订单基本信息
 * @typedef {Object} OrderBasicInfo
 * @property {string} orderType - 订单类型：rental(租赁)，installment(分期)
 * @property {string} startDate - 开始日期
 * @property {string} remarks - 订单备注
 */

/**
 * 订单客户信息
 * @typedef {Object} OrderCustomer
 * @property {string} id - 客户ID
 * @property {string} name - 客户姓名
 * @property {string} phone - 联系电话
 * @property {string} email - 电子邮箱
 * @property {string} idType - 证件类型
 * @property {string} idNumber - 证件号码
 * @property {string} licenseAddress - 证件地址
 * @property {string} contactAddress - 联系地址
 */

/**
 * 订单设备信息
 * @typedef {Object} OrderDevice
 * @property {string} id - 设备ID
 * @property {string} serialNumber - 设备序列号
 * @property {string} brand - 品牌
 * @property {string} model - 型号
 * @property {string} color - 颜色
 * @property {string} imei1 - IMEI号码1
 * @property {string} imei2 - IMEI号码2（可选）
 * @property {string} accessories - 配件清单
 * @property {string} status - 设备状态
 */

/**
 * 订单付款信息
 * @typedef {Object} OrderPayment
 * @property {number} initialPayment - 首付金额
 * @property {string} firstPaymentDate - 首次付款日期
 * @property {number} periodicPayment - 每期付款金额
 * @property {number} installments - 付款期数
 * @property {number} periodicLength - 每期长度
 * @property {string} periodicLengthUnit - 每期长度单位：day, month, year
 * @property {number} totalAmount - 总金额
 * @property {boolean} needDeposit - 是否需要押金
 * @property {number} depositAmount - 押金金额
 * @property {string} depositCurrency - 押金币种
 * @property {string} penaltyCalculationType - 逾期计算方式：DAILY, WEEKLY, MONTHLY
 * @property {string} penaltyAmountType - 违约金类型：FIXED_AMOUNT, PERCENTAGE_AMOUNT
 * @property {number} penaltyValue - 违约金数值
 * @property {number} serviceFee - 服务费
 */

/**
 * 订单付款计划项
 * @typedef {Object} OrderPaymentPlan
 * @property {string} type - 类型：initial（首付）, deposit（押金）, installment（分期）
 * @property {number} installmentNo - 期数（仅分期有）
 * @property {number} amount - 金额
 * @property {number} serviceFee - 服务费
 * @property {string} dueDate - 应付日期
 * @property {string} status - 状态：pending（待付）, paid（已付）, overdue（逾期）
 */

/**
 * 创建订单请求参数
 * @typedef {Object} CreateOrderRequest
 * @property {OrderBasicInfo} basicInfo - 订单基本信息
 * @property {OrderCustomer} customer - 客户信息
 * @property {Array<OrderDevice>} devices - 设备信息
 * @property {OrderPayment} payment - 付款信息
 * @property {Array<OrderPaymentPlan>} paymentPlan - 付款计划
 */

/**
 * 订单统计数据
 * @typedef {Object} OrderStatistics
 * @property {number} total - 订单总数
 * @property {number} pending - 待处理订单数
 * @property {number} confirmed - 已确认订单数
 * @property {number} completed - 已完成订单数
 * @property {number} cancelled - 已取消订单数
 * @property {number} totalAmount - 订单总金额
 * @property {number} pendingAmount - 待收款金额
 * @property {number} collectedAmount - 已收款金额
 */

/**
 * 订单付款记录
 * @typedef {Object} OrderPaymentRecord
 * @property {string} id - 付款记录ID
 * @property {string} orderId - 订单ID
 * @property {string} paymentType - 付款类型
 * @property {number} amount - 付款金额
 * @property {string} paymentMethod - 付款方式
 * @property {string} paymentDate - 付款日期
 * @property {string} status - 状态
 * @property {string} remarks - 备注
 */

// 导出类型
export const OrderTypes = {
  RENTAL: 'rental',
  INSTALLMENT: 'installment'
};

export const OrderStatus = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

export const PaymentPlanStatus = {
  PENDING: 'pending',
  PAID: 'paid',
  OVERDUE: 'overdue'
};

export const PaymentPlanType = {
  INITIAL: 'initial',
  DEPOSIT: 'deposit',
  INSTALLMENT: 'installment'
};

export const PenaltyType = {
  NONE: 'none',
  FIXED: 'fixed',
  PERCENTAGE: 'percentage'
};

export const PenaltyCalculationType = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  MONTHLY: 'MONTHLY'
};

export const PenaltyAmountType = {
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  PERCENTAGE_AMOUNT: 'PERCENTAGE_AMOUNT'
}; 
<template>
  <div class="attachment-list-component">
    <div v-if="isLoading" class="loading-container">
      <el-progress 
        type="circle" 
        :width="24" 
        :stroke-width="3" 
        :percentage="100" 
        status="success" 
        :indeterminate="true"
        :duration="1"
        class="custom-loader"
      />
      <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
    </div>

    <div v-else-if="loadingError" class="error-container">
      <span>{{ $t('common.fetchFailed') }}</span>
      <el-button type="danger" :icon="Refresh" @click="handleRetryFetch" plain size="small" class="retry-button">
        {{ $t('common.retry') }}
      </el-button>
    </div>

    <div v-else-if="processedAttachments && processedAttachments.length > 0" class="attachment-list">
      <div v-for="attachment in processedAttachments" :key="attachment.id" class="attachment-item">
        <div class="attachment-main-content">
          <div class="attachment-thumbnail">
            <el-skeleton v-if="attachment.isProcessing" style="width: 50px; height: 50px" animated>
              <template #template>
                <el-skeleton-item
                  variant="image"
                  style="width: 50px; height: 50px; border-radius: 4px"
                />
              </template>
            </el-skeleton>
            <template v-else>
              <el-image
                v-if="isImageFile(attachment.fileName)"
                style="width: 50px; height: 50px; border-radius: 4px"
                :src="getProxiedUrl(attachment.fileUrl)"
                :preview-src-list="[getProxiedUrl(attachment.fileUrl)]"
                fit="cover"
                lazy
              >
                <template #placeholder>
                  <div class="image-slot"><el-icon><Picture /></el-icon></div>
                </template>
                <template #error>
                  <div class="image-slot"><el-icon><Picture /></el-icon></div>
                </template>
              </el-image>
              <el-image
                v-else-if="isPdfFile(attachment.fileName) && attachment.thumbnail"
                style="width: 50px; height: 50px; border-radius: 4px"
                :src="attachment.thumbnail"
                :preview-src-list="[getProxiedUrl(attachment.fileUrl)]"
                fit="cover"
                lazy
              >
                <template #error>
                  <div class="thumbnail-placeholder">
                    <el-icon size="24"><Document /></el-icon>
                  </div>
                </template>
              </el-image>
              <div v-else class="thumbnail-placeholder">
                <el-icon size="24"><Document /></el-icon>
              </div>
            </template>
          </div>
          <div class="attachment-details">
            <a class="el-upload-list__item-name" @click="handlePreview(attachment)">
              <span>{{ attachment.fileName }}</span>
            </a>
            <div class="attachment-meta">
              <el-skeleton v-if="attachment.isProcessing" style="width: 120px" animated>
                <template #template>
                  <el-skeleton-item variant="text" style="width: 100%" />
                </template>
              </el-skeleton>
              <template v-else>
                <span>{{ attachment.formattedSize }}</span>
                <span
                  v-if="attachment.formattedSize && attachment.formattedUploadTime"
                  class="meta-divider"
                  >|</span
                >
                <span>{{ attachment.formattedUploadTime }}</span>
              </template>
            </div>
          </div>
        </div>
        <div class="attachment-actions">
          <el-button type="primary" link size="small" @click="handlePreview(attachment)">{{
            $t('order.attachment.preview')
          }}</el-button>
          <el-button
            type="primary"
            link
            size="small"
            @click="downloadFile(attachment)"
            :loading="attachment.isDownloading"
            :disabled="attachment.isDownloading"
            >{{
              attachment.isDownloading ? $t('common.downloading') : $t('common.download')
            }}</el-button
          >
        </div>
      </div>
    </div>
    <div v-else class="no-attachments">{{ $t('customer.details.noAttachments') }}</div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Refresh, Document, Picture } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { downloadOssFile } from '@/api/upload';
import * as pdfjsLib from 'pdfjs-dist/build/pdf.mjs';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.mjs?url';
import axios from 'axios';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const { t } = useI18n();

const props = defineProps({
  attachments: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);

const processedAttachments = ref([]);

watch(
  () => props.attachments,
  (newAttachments) => {
    if (newAttachments) {
      processAllAttachments(newAttachments);
    } else {
      processedAttachments.value = [];
    }
  },
  { immediate: true, deep: true }
);


function getProxiedUrl(url) {
  if (!url) return '';
  const targetDomain = 'https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com';
  if (url.startsWith(targetDomain)) {
    if (import.meta.env.DEV) {
      return url.replace(targetDomain, '/cos-proxy');
    }
  }
  return url;
}

async function processAllAttachments(attachments) {
  if (!attachments || attachments.length === 0) {
    processedAttachments.value = [];
    return;
  }

  const attachmentsData = attachments.map((att) => ({
    ...att,
    id: att.id || `temp_${Date.now()}_${Math.random()}`,
    // 使用后端返回的信息
    thumbnail: att.thumbnail || null,
    fileSize: att.fileSize || 0,
    fileType: att.fileType || getFileTypeFromName(att.fileName),
    formattedSize: att.fileSize ? formatFileSize(att.fileSize) : '',
    uploadTime: att.uploadTime,
    formattedUploadTime: formatUploadTime(att.uploadTime),
    isDownloading: false,
    isProcessing: false // 后端已经提供了所有信息，不需要处理
  }));

  // 直接使用后端返回的数据，不需要异步处理
  processedAttachments.value = attachmentsData;
}

// fetchFileSize函数已移除，现在使用后端返回的文件大小信息

// generatePdfThumbnail函数已移除，现在使用后端返回的缩略图

function formatUploadTime(dateString) {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return '';
  }
}

function isImageFile(fileName) {
  if (!fileName) return false;
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = fileName.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
}

function isPdfFile(fileName) {
  if (!fileName) return false;
  const pdfExtensions = ['pdf'];
  const extension = fileName.split('.').pop().toLowerCase();
  return pdfExtensions.includes(extension);
}

// 从文件名获取文件类型
function getFileTypeFromName(fileName) {
  if (!fileName || typeof fileName !== 'string') return '';
  const extension = fileName.split('.').pop()?.toLowerCase();
  if (!extension) return '';

  const typeMap = {
    'pdf': 'PDF',
    'jpg': 'JPG',
    'jpeg': 'JPEG',
    'png': 'PNG',
    'gif': 'GIF',
    'bmp': 'BMP',
    'webp': 'WEBP',
    'svg': 'SVG',
    'doc': 'DOC',
    'docx': 'DOCX',
    'xls': 'XLS',
    'xlsx': 'XLSX',
    'ppt': 'PPT',
    'pptx': 'PPTX',
    'txt': 'TXT',
    'zip': 'ZIP',
    'rar': 'RAR'
  };

  return typeMap[extension] || extension.toUpperCase();
}

function formatFileSize(size) {
  if (size === 0) return '0 B';
  if (!size || isNaN(size)) return '';
  const units = ['B', 'KB', 'MB', 'GB'];
  let i = 0;
  let formattedSize = size;
  while (formattedSize >= 1024 && i < units.length - 1) {
    formattedSize /= 1024;
    i++;
  }
  return `${formattedSize.toFixed(2)} ${units[i]}`;
}

function handleRetryFetch() {
  emit('retry-fetch');
}

function handlePreview(attachment) {
  const previewUrl = getProxiedUrl(attachment.fileUrl || attachment.url);
  if (previewUrl) {
    window.open(previewUrl, '_blank');
  } else {
    ElMessage.info(t('common.previewNotAvailable', '此文件无法预览或尚未上传成功。'));
  }
}

async function downloadFile(attachment) {
  const attachmentToDownload = processedAttachments.value.find((a) => a.id === attachment.id);
  if (!attachmentToDownload || !attachmentToDownload.fileUrl) {
    ElMessage.warning(t('order.attachment.downloadUrlMissing'));
    return;
  }
  attachmentToDownload.isDownloading = true;

  const getOriginalUrl = (url) => {
    if (!url) return '';
    const proxyPrefix = '/cos-proxy';
    if (url.startsWith(proxyPrefix)) {
      const targetDomain = 'https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com';
      return url.replace(proxyPrefix, targetDomain);
    }
    return url;
  };

  try {
    // Use the original URL for download
    const { blob, filename } = await downloadOssFile(getOriginalUrl(attachmentToDownload.fileUrl), attachmentToDownload.fileName);

    // Create a temporary link to trigger the download
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // Clean up the temporary link
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    ElMessage.success(t('common.download') + t('common.success'));
  } catch (error) {
    console.error('文件下载失败:', error);
    ElMessage.error(
      t('common.download') + ' ' + t('common.failed') + ': ' + (error.message || t('common.unknown'))
    );
  } finally {
    if (attachmentToDownload) {
      attachmentToDownload.isDownloading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.attachment-list-component {
  background-color: #fff;
  /* padding: 20px; */ /* remove padding */
  border-radius: 4px;
}

.loading-container, .error-container, .empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #909399;
  font-size: 14px;
}
.attachment-list {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}
.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  
  &:last-child {
    border-bottom: none;
  }
}
.attachment-item .attachment-main-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-grow: 1;
}
.attachment-item .attachment-thumbnail {
  flex-shrink: 0;
}
.attachment-item .attachment-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}
.attachment-item .el-upload-list__item-name {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    color: #409eff;
  }
}
.attachment-item .attachment-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 5px;
}
.attachment-item .attachment-meta .meta-divider {
  color: #e0e0e0;
}
.attachment-item .attachment-actions {
  display: flex;
  gap: 10px;
}
.thumbnail-placeholder {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #c0c4cc;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: var(--el-text-color-secondary);
  font-size: 24px;
}
.no-attachments {
  color: #909399;
  text-align: center;
  padding: 20px;
  border: 1px dashed #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
}
</style> 
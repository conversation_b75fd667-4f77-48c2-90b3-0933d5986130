<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="85%"
    :before-close="handleClose"
    class="order-detail-dialog"
    @open="handleDialogOpen"
    append-to-body
    draggable
    :destroy-on-close="true"
  >
    <div class="order-header" v-if="orderDetail">
      <div class="order-basic-info">
        <el-tag 
          :type="getOrderStatusType(orderDetail.orderInfo?.status)" 
          effect="light" 
          class="status-tag"
        >
          {{ formatOrderStatus(orderDetail.orderInfo?.status) }}
        </el-tag>
        <el-tag 
          v-if="orderDetail.orderInfo?.isBadDebt" 
          type="danger" 
          effect="light" 
          class="bad-debt-tag"
        >
          {{ $t('order.list.badDebt') }}: {{ $t('order.list.yes') }}
        </el-tag>
      </div>
    </div>
    
    <el-tabs 
      v-model="activeTabName"
      class="detail-tabs"
      type="card"
    >
      <el-tab-pane :label="$t('order.statusInfo')" name="status">
        <OrderStatusTab 
          v-if="activeTabName === 'status'" 
          :order-id="orderIdProp"
          :order-info="orderDetail?.orderInfo"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </el-tab-pane>
      <el-tab-pane :label="$t('order.basicInfo')" name="info">
        <OrderInfoTab 
          v-if="activeTabName === 'info'" 
          :order-id="orderIdProp"
          :order-info="orderDetail?.orderInfo"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </el-tab-pane>
      <el-tab-pane :label="$t('customer.information')" name="customer">
        <CustomerInfoTab 
          v-if="activeTabName === 'customer'" 
          :order-id="orderIdProp"
          :customer-info="orderDetail?.customerInfo"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </el-tab-pane>
      <el-tab-pane :label="$t('device.information')" name="device">
        <DeviceInfoTab 
          v-if="activeTabName === 'device'" 
          :order-id="orderIdProp"
          :device-info="orderDetail?.deviceInfo"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </el-tab-pane>
      <el-tab-pane :label="$t('order.paymentPlanTitle')" name="payment">
        <PaymentHistoryTab 
          v-if="activeTabName === 'payment'" 
          :order-id="orderDetail?.orderInfo?.orderNo || orderIdProp"
          :payment-info="orderDetail?.paymentInfo"
          :is-loading="isLoading"
          :loading-error="loadingError"
          @retry-fetch="handleRetryFetch"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
// Import tab components
import OrderStatusTab from './tabs/OrderStatusTab.vue';
import OrderInfoTab from './tabs/OrderInfoTab.vue';
import CustomerInfoTab from './tabs/CustomerInfoTab.vue';
import DeviceInfoTab from './tabs/DeviceInfoTab.vue';
import PaymentHistoryTab from './tabs/PaymentHistoryTab.vue';
import { getOrderDetail, getOrderDetailByOrderNo } from '@/api/order';
import { ElMessage } from 'element-plus';

const { t } = useI18n();

const props = defineProps({
  modelValue: Boolean, // For v-model binding
  orderId: {
    type: [String, Number], // 接受字符串或数字类型
    default: ''
  },
  // Optional: to display order number in title
  orderNumber: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const activeTabName = ref('status'); // Default active tab
const orderIdProp = ref(props.orderId);
const orderDetail = ref(null);
const isLoading = ref(false);
const loadingError = ref(false);

const dialogTitle = computed(() => {
  return props.orderNumber 
    ? `${t('order.details.title')} - ${props.orderNumber}` 
    : t('order.details.title');
});

watch(() => props.orderId, (newVal) => {
  orderIdProp.value = newVal;
  // Reset to the first tab when a new orderId is received,
  // and the dialog is already open or about to open.
  if (dialogVisible.value && newVal) {
    activeTabName.value = 'status'; 
    fetchOrderDetail();
  }
});

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!orderIdProp.value) return;
  
  isLoading.value = true;
  loadingError.value = false;
  
  try {
    // 记录订单ID以便调试
    console.log(`获取订单详情，订单ID: ${orderIdProp.value}, 类型: ${typeof orderIdProp.value}`);
    
    // 检查订单ID是否为类似R202505082002这样的格式
    const isOrderNoFormat = typeof orderIdProp.value === 'string' && /^[A-Za-z]/.test(orderIdProp.value);
    
    // 根据ID格式选择不同的API调用
    let res;
    if (isOrderNoFormat) {
      console.log(`使用getOrderDetailByOrderNo接口获取订单: ${orderIdProp.value}`);
      res = await getOrderDetailByOrderNo(orderIdProp.value);
    } else {
      console.log(`使用getOrderDetail接口获取订单: ${orderIdProp.value}`);
      res = await getOrderDetail(orderIdProp.value);
    }
    
    console.log('订单详情API响应:', res);
    
    if (res.code === 1000) {
      // 处理API返回的数据 - 支持小写状态值处理
      // 计算并添加金额相关字段
      if (res.data) {
        // 计算进度和状态相关信息
        processOrderData(res.data);
        // 确保状态显示正确
        ensureStatusDisplay(res.data);
        orderDetail.value = res.data;
        
        // 打印订单号和ID以便调试
        if (res.data.orderInfo) {
          console.log('处理后的订单数据:');
          console.log('- 订单号(orderNo):', res.data.orderInfo.orderNo);
          console.log('- 订单ID(id):', res.data.orderInfo.id);
          console.log('- 订单ID(orderId):', res.data.orderInfo.orderId);
        }
      } else {
        orderDetail.value = null;
        loadingError.value = true;
      }
      console.log('获取订单详情成功:', orderDetail.value);
    } else {
      loadingError.value = true;
      ElMessage.error(res.message || t('common.fetchFailed'));
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    loadingError.value = true;
    if (error.response && error.response.status === 502) {
      ElMessage.error(t('common.serverError'));
    } else {
      ElMessage.error(t('common.fetchFailed'));
    }
  } finally {
    isLoading.value = false;
  }
};

// 处理订单数据 - 计算金额和状态
const processOrderData = (data) => {
  if (!data || !data.orderInfo) return;
  
  const orderInfo = data.orderInfo;
  
  // 处理特定字段的映射关系
  // 确保orderNo字段存在
  if (!orderInfo.orderNo && orderInfo.orderId) {
    console.log('使用orderId作为orderNo:', orderInfo.orderId);
    orderInfo.orderNo = orderInfo.orderId;
  }
  
  // API返回的orderStatus应该映射到status
  if (orderInfo.orderStatus && !orderInfo.status) {
    orderInfo.status = orderInfo.orderStatus;
  }
  
  // 日期处理
  orderInfo.createTime = orderInfo.orderDate || orderInfo.createTime || orderInfo.createdAt;
  
  // 输出订单编号以便调试
  console.log('订单编号(orderNo):', orderInfo.orderNo);
  
  // 计算订单状态进度百分比
  if (orderInfo.numberOfInstallments > 0 || orderInfo.installments > 0) {
    const currentInstallment = orderInfo.currentInstallment || orderInfo.currentPeriod || 0;
    orderInfo.totalInstallments = orderInfo.numberOfInstallments || orderInfo.installments;
    // 修正计算公式：(当前期数 - 1) / 总期数
    const progressRatio = Math.max(0, currentInstallment - 1) / orderInfo.totalInstallments;
    orderInfo.progressPercentage = Math.floor(progressRatio * 100);
  } else {
    orderInfo.progressPercentage = 0;
  }
  
  // 处理逾期相关信息
  orderInfo.hasOverdue = orderInfo.hasOverdue || false;
  orderInfo.totalOverdueAmount = orderInfo.totalOverdueAmount || 0;
  orderInfo.overdueInstallments = orderInfo.overdueInstallments || 0;
  orderInfo.maxOverdueDays = orderInfo.maxOverdueDays || 0;
  orderInfo.totalPenaltyIncurred = orderInfo.totalPenaltyIncurred || 0;
  orderInfo.totalPenaltyPaid = orderInfo.totalPenaltyPaid || 0;
  orderInfo.remainingPenalty = orderInfo.remainingPenalty || 0;
  
  // 确保付款信息存在
  if (!data.paymentInfo) {
    data.paymentInfo = {
      totalPayableAmount: orderInfo.totalAmount || 0,
      totalPaidAmount: 0,
      totalOverdueAmount: orderInfo.overdueAmount || 0,
      nextPaymentAmount: 0,
      transactions: []
    };
  } else {
    // 确保paymentInfo中包含所有必要字段
    console.log('原始paymentInfo数据:', data.paymentInfo);
    
    // 处理汇总金额数据
    if (data.paymentInfo.totalPayableAmount === undefined) {
      data.paymentInfo.totalPayableAmount = extractValue([
        orderInfo.totalAmount,
        orderInfo.contractAmount,
        data.totalAmount
      ], 0);
    }
    
    if (data.paymentInfo.totalPaidAmount === undefined) {
      data.paymentInfo.totalPaidAmount = extractValue([
        orderInfo.paidAmount,
        data.paidAmount,
        0
      ]);
    }
    
    if (data.paymentInfo.totalOverdueAmount === undefined && 
        data.paymentInfo.currentOverduePrincipal === undefined) {
      data.paymentInfo.totalOverdueAmount = extractValue([
        orderInfo.overdueAmount,
        data.overdueAmount,
        data.overdueFee,
        0
      ]);
    }
    
    // 处理违约金信息
    if (data.paymentInfo.transactions) {
      let totalPenaltyIncurred = 0;
      let totalPenaltyPaid = 0;
      
      data.paymentInfo.transactions.forEach(transaction => {
        if (transaction.status === 'OVERDUE') {
          totalPenaltyIncurred += Number(transaction.penaltyIncurred || 0);
          totalPenaltyPaid += Number(transaction.penaltyPaid || 0);
        }
      });
      
      orderInfo.totalPenaltyIncurred = totalPenaltyIncurred;
      orderInfo.totalPenaltyPaid = totalPenaltyPaid;
      orderInfo.remainingPenalty = totalPenaltyIncurred - totalPenaltyPaid;
    }
    
    // 打印处理后的paymentInfo
    console.log('处理后的paymentInfo数据:', data.paymentInfo);
  }
  
  // 在订单详情API中查找付款计划数据
  const transactionData = extractPaymentPlanData(data);
  if (transactionData.length > 0) {
    if (!data.paymentInfo.transactions) {
      data.paymentInfo.transactions = transactionData;
    }
    console.log('找到付款计划数据，共', transactionData.length, '条');
  }
  
  // 更新订单详情中的支付金额信息
  if (data.paymentInfo) {
    // 使用API返回的totalPayableAmount作为总金额
    orderInfo.totalAmount = extractValue([
      data.paymentInfo.totalPayableAmount,
      orderInfo.totalAmount,
      data.totalAmount
    ], 0);
    
    orderInfo.contractAmount = orderInfo.totalAmount; 
    
    orderInfo.paidAmount = extractValue([
      data.paymentInfo.totalPaidAmount,
      data.paymentInfo.paidAmount,
      orderInfo.paidAmount,
      data.paidAmount,
      0
    ]);
    
    orderInfo.overdueAmount = extractValue([
      data.paymentInfo.totalUnpaidPenalty,
      data.paymentInfo.totalOverdueAmount,
      data.paymentInfo.currentOverduePrincipal,
      orderInfo.overdueAmount,
      data.overdueAmount,
      0
    ]);
    
    orderInfo.nextPaymentAmount = extractValue([
      data.paymentInfo.nextPaymentDueAmount,
      data.paymentInfo.nextPaymentAmount,
      data.paymentInfo.nextInstallmentAmount,
      orderInfo.nextPaymentAmount,
      data.nextPaymentAmount,
      0
    ]);
    
    // 计算剩余金额
    orderInfo.remainingAmount = Math.max(0, orderInfo.totalAmount - orderInfo.paidAmount);
  }
  
  // 调试输出
  console.log('处理后的订单数据:', data);
  console.log('合同金额:', orderInfo.contractAmount);
  console.log('总金额:', orderInfo.totalAmount);
  console.log('已付金额:', orderInfo.paidAmount);
  console.log('剩余金额:', orderInfo.remainingAmount);
  console.log('订单状态:', orderInfo.status);
  console.log('财务状态:', orderInfo.financialStatus);
  console.log('逾期信息:', {
    hasOverdue: orderInfo.hasOverdue,
    overdueInstallments: orderInfo.overdueInstallments,
    maxOverdueDays: orderInfo.maxOverdueDays,
    totalPenaltyIncurred: orderInfo.totalPenaltyIncurred,
    totalPenaltyPaid: orderInfo.totalPenaltyPaid,
    remainingPenalty: orderInfo.remainingPenalty
  });
  
  return data;
};

// 从多个可能的来源提取支付计划数据
const extractPaymentPlanData = (data) => {
  if (!data) return [];
  
  // 按优先级尝试不同来源的数据
  // 检查是否存在paymentPlans的直接字段
  if (data.paymentPlans && Array.isArray(data.paymentPlans) && data.paymentPlans.length > 0) {
    console.log('从顶层paymentPlans获取付款计划', data.paymentPlans.length);
    return data.paymentPlans;
  }
  
  // 检查是否存在直接的transactions字段
  if (data.transactions && Array.isArray(data.transactions) && data.transactions.length > 0) {
    console.log('从顶层transactions获取付款计划', data.transactions.length);
    return data.transactions;
  }
  
  // 检查是否存在paymentSchedules字段
  if (data.paymentSchedules && Array.isArray(data.paymentSchedules) && data.paymentSchedules.length > 0) {
    console.log('从paymentSchedules获取付款计划', data.paymentSchedules.length);
    return data.paymentSchedules;
  }
  
  // 尝试从paymentInfo中获取
  if (data.paymentInfo) {
    if (data.paymentInfo.paymentPlans && data.paymentInfo.paymentPlans.length > 0) {
      console.log('从paymentInfo.paymentPlans复制交易记录');
      return data.paymentInfo.paymentPlans;
    }
    
    if (data.paymentInfo.transactions && data.paymentInfo.transactions.length > 0) {
      console.log('从paymentInfo.transactions复制交易记录');
      return data.paymentInfo.transactions;
    }
    
    if (data.paymentInfo.paymentSchedules && data.paymentInfo.paymentSchedules.length > 0) {
      console.log('从paymentInfo.paymentSchedules复制交易记录');
      return data.paymentInfo.paymentSchedules;
    }
  }
  
  // 尝试从paymentList中获取
  if (data.paymentList && Array.isArray(data.paymentList) && data.paymentList.length > 0) {
    console.log('从paymentList获取付款计划', data.paymentList.length);
    return data.paymentList;
  }
  
  console.warn('未找到交易记录数据');
  return [];
};

// 从多个可能的字段中提取值
const extractValue = (possibleValues, defaultValue = null) => {
  for (const value of possibleValues) {
    if (value !== undefined && value !== null) {
      return value;
    }
  }
  return defaultValue;
};

const handleDialogOpen = () => {
  // When dialog opens, ensure the orderId is current and reset tab if needed
  orderIdProp.value = props.orderId;
  if (props.orderId) { // If there's an orderId, set the active tab
     activeTabName.value = 'status';
     fetchOrderDetail();
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};

// 重新获取订单详情
const handleRetryFetch = () => {
  fetchOrderDetail();
};

// 确保状态显示正确
const ensureStatusDisplay = (orderData) => {
  if (!orderData || !orderData.orderInfo) return;

  // 修正状态显示
  const orderInfo = orderData.orderInfo;
  
  // 显示原始数据用于诊断
  console.log('原始订单数据:', JSON.stringify(orderInfo));
  
  // 确保订单状态有值
  if (!orderInfo.status) {
    console.log('订单状态为空，设置为默认值unknown');
    orderInfo.status = 'unknown';
  }
  
  // 确保财务状态有值
  if (!orderInfo.financialStatus) {
    console.log('财务状态为空，设置为默认值unknown');
    orderInfo.financialStatus = 'unknown';
  }
  
  // 确保合同金额有值
  if (!orderInfo.contractAmount && orderInfo.totalAmount) {
    console.log('合同金额为空，使用totalAmount作为默认值');
    orderInfo.contractAmount = orderInfo.totalAmount;
  }
  
  // 记录到控制台
  console.log('处理后订单状态:', orderInfo.status);
  console.log('处理后财务状态:', orderInfo.financialStatus);
  console.log('处理后合同金额:', orderInfo.contractAmount);
  
  return orderData;
};

// 获取订单状态对应的类型
const getOrderStatusType = (status) => {
  if (!status) return 'info';
  
  // 将状态转换为大写以便匹配
  const upperStatus = getStatusLower(status).toUpperCase();
  
  const map = {
    'NORMAL': 'success',
    'ACTIVE': 'success',
    'OVERDUE': 'warning', // 在之前的截图中，overdue 是 warning 类型
    'COMPLETED': 'info',
    'CANCELLED': 'danger', // 在之前的截图中，cancelled 是 danger 类型
    'UNKNOWN': 'info' // 为未知状态添加默认类型
  };
  
  return map[upperStatus] || 'info'; // 如果没有匹配到，默认为 'info'
};

// 安全获取状态的小写形式 (这个函数保持不变，但在上面的函数中会转大写)
const getStatusLower = (status) => {
  if (!status) return '';
  return typeof status === 'string' ? status.toLowerCase() : '';
};

// 格式化订单状态
const formatOrderStatus = (status) => {
  if (!status) return t('common.unknown');
  
  // 将状态转换为大写
  const upperStatus = typeof status === 'string' ? status.toUpperCase() : '';
  
  // 使用 t() 和大写键名构造路径
  const translationKey = `order.status.${upperStatus}`;
  const translatedText = t(translationKey);
  
  // 如果翻译结果就是键名本身（意味着没有找到对应的翻译），
  // 则返回原始的大写状态值，或者一个通用的未知提示
  if (translatedText === translationKey) {
    return upperStatus || t('common.unknown');
  }
  
  return translatedText;
};
</script>

<style lang="scss" scoped>
.order-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
  
  :deep(.el-dialog__header) {
    margin-right: 0;
    padding: 16px 20px;
    background-color: #fff;
    border-bottom: 1px solid var(--el-border-color-lighter);
    border-radius: 8px 8px 0 0;
  }
  
  :deep(.el-dialog__title) {
    font-weight: 600;
    font-size: 18px;
    color: #303133;
  }
  
  :deep(.el-dialog__headerbtn) {
    top: 16px;
    right: 16px;
  }
  
  :deep(.el-dialog__wrapper) {
    backdrop-filter: blur(5px);
  }
  
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  }
}

.order-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  
  .order-basic-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .status-tag, .bad-debt-tag {
      font-size: 14px;
      padding: 0 12px;
      height: 28px;
      line-height: 26px;
      border-radius: 4px;
    }
  }
}

.detail-tabs {
  margin-bottom: 0;
  
  :deep(.el-tabs__header) {
    margin-bottom: 0;
    background-color: #fff;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
  }
  
  :deep(.el-tabs__nav) {
    border: none;
  }
  
  :deep(.el-tabs__item) {
    height: 48px;
    line-height: 48px;
    font-size: 15px;
    transition: all 0.3s;
    border: none;
    background-color: transparent;
    margin-right: 4px;
    
    &.is-active {
      font-weight: 600;
      color: var(--el-color-primary);
      background-color: #f0f8ff;
      border-radius: 4px 4px 0 0;
    }
    
    &:hover {
      color: var(--el-color-primary-light-3);
    }
  }
  
  :deep(.el-tabs__active-bar) {
    display: none;
  }
  
  :deep(.el-tabs__content) {
    padding: 0;
    background-color: #f9fafc;
    
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
}

:deep(.preview-dialog .el-dialog__body) {
  height: calc(80vh);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.preview-dialog .image-preview-container) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.preview-dialog .preview-image) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

@media (max-width: 768px) {
  .order-header {
    padding: 8px 16px;
  }
  
  .detail-tabs {
    :deep(.el-tabs__header) {
      padding: 0 12px;
    }
    
    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 10px;
      margin-right: 2px;
    }
  }
}
</style> 
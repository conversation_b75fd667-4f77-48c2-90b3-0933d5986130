<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('order.dialog.createTitle')"
    width="1200px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="create-order-dialog"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="custom-dialog-header">
        <h4 :id="titleId" :class="titleClass">{{ $t('order.dialog.createTitle') }}</h4>
        <el-button @click="handleCancel">{{ $t('order.backToList') }}</el-button>
      </div>
    </template>
    <div class="dialog-content">
      <!-- 左侧步骤条 -->
      <div class="steps-container">
        <el-steps direction="vertical" :active="activeStep" finish-status="success">
          <el-step :title="$t('order.steps.selectDevice')" />
          <el-step :title="$t('order.steps.customerInfo')" />
          <el-step :title="$t('order.steps.orderInfo')" />
          <el-step :title="$t('order.steps.paymentPlan')" />
          <el-step :title="$t('order.steps.preview')" />
        </el-steps>
      </div>

      <!-- 右侧表单内容 -->
      <div class="form-container">
        <!-- 步骤1：选择设备 -->
        <div v-if="activeStep === 0" class="step-device">
          <h3 class="step-title">{{ $t('order.steps.selectDevice') }}</h3>
          
          <!-- 设备信息 -->
          <div class="device-select-section">
            <div class="section-title">{{ $t('device.information') }}</div>
            
            <!-- 搜索下拉框 -->
            <el-select
              v-model="selectedDeviceId"
              filterable
              remote
              reserve-keyword
              :placeholder="$t('device.search.placeholder')"
              :remote-method="searchDevices"
              :loading="deviceLoading"
              style="width: 100%"
              @change="handleDeviceSelect"
              clearable
              popper-class="device-select-dropdown"
            >
              <el-option
                v-for="item in deviceList"
                :key="item.id"
                :label="`${item.deviceInfo} (${item.serialNumber})`"
                :value="item.id"
              />
              <template #empty>
                <div v-if="deviceLoading" class="searching-device py-10">
                  {{ $t('device.searching') }}
                </div>
                <div v-else class="not-found-device py-10">
                  <span>{{ $t('device.notFound') }}</span>
                </div>
                <div class="create-device-option py-10">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click.stop.prevent="handleCreateDevice"
                    class="create-device-btn"
                  >
                    <el-icon class="el-icon--left"><CirclePlus /></el-icon>
                    {{ $t('device.action.create') }}
                  </el-button>
                </div>
              </template>
            </el-select>
          </div>
          
          <!-- 设备详情信息区域 -->
          <div class="device-details-section">
            <div class="section-title">{{ $t('device.details.title') }}</div>
            <div class="grid-row">
              <div class="grid-col">
                <div class="field-label">{{ $t('device.serialNumber') }}</div>
                <el-input v-model="deviceSnInput" :placeholder="$t('device.sn.placeholder')" disabled />
              </div>
              <div class="grid-col">
                <div class="field-label">{{ $t('device.brand.label') }}</div>
                <el-input v-model="brandLabel" :placeholder="$t('device.brand.placeholder')" disabled />
              </div>
              <!-- <div class="grid-col">
                <div class="field-label">{{ $t('device.model') }}</div>
                <el-input v-model="deviceModelInput" :placeholder="$t('device.modelPlaceholder')" disabled />
              </div> -->
            </div>
            <div class="grid-row">
              <div class="grid-col">
                <div class="field-label">{{ $t('device.colorInfo') }}</div>
                <el-input v-model="deviceColorInput" :placeholder="$t('device.color.placeholder')" disabled />
              </div>
              <div class="grid-col">
                <div class="field-label">{{ $t('device.deviceDetails') }}</div>
                <el-input v-model="deviceModelInput" :placeholder="$t('device.details.placeholder')" disabled />
              </div>
            </div>
            
            <div class="grid-row">
              <div class="grid-col">
                <div class="field-label">IMEI1</div>
                <el-input v-model="deviceImei1Input" :placeholder="$t('device.imeiPlaceholder')" disabled />
              </div>
              <div class="grid-col">
                <div class="field-label">IMEI2</div>
                <el-input v-model="deviceImei2Input" :placeholder="$t('device.imei2Placeholder')" disabled />
              </div>
            </div>
            
            <div class="grid-row">
              <div class="grid-col full-width">
                <div class="field-label">{{ $t('device.attachments') }}</div>
                <div v-if="deviceAttachments && deviceAttachments.length > 0" class="attachment-list">
                  <div v-for="(attachment, index) in deviceAttachments" :key="index" class="attachment-item">
                    <el-link type="primary" :href="attachment.fileUrl" target="_blank">
                      <el-icon class="attachment-icon"><Document /></el-icon>
                      {{ attachment.fileName }}
                    </el-link>
                  </div>
                </div>
                <div v-else class="no-attachments">{{ $t('device.noAttachments') }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2：客户信息 -->
        <div v-if="activeStep === 1" class="step-customer">
          <h3 class="step-title">{{ $t('order.steps.customerInfo') }}</h3>
          
          <!-- 客户信息 -->
          <div class="customer-select-section">
            
            <!-- 搜索下拉框 -->
            <el-select
              v-model="selectedCustomerId"
              filterable
              remote
              reserve-keyword
              :placeholder="$t('customer.search.placeholder')"
              :remote-method="searchCustomers"
              :loading="customerLoading"
              style="width: 100%"
              @change="handleCustomerSelect"
              clearable
              popper-class="customer-select-dropdown"
            >
              <el-option
                v-for="item in customerList"
                :key="item.id"
                :label="`${item.name} (${item.phone})`"
                :value="item.id"
                class="customer-option"
              />
              <template #empty>
                <div v-if="customerLoading" class="searching-customer">
                  {{ $t('customer.searching') }}
                </div>
                <div v-else class="not-found-customer">
                  <span>{{ $t('customer.notFound') }}</span>
                </div>
                <div class="create-customer-option">
                  <span 
                    class="create-customer-text"
                    @click.stop.prevent="openCreateCustomerDialog"
                  >
                    <el-icon class="el-icon--left"><CirclePlus /></el-icon>
                    {{ $t('customer.action.create') }}
                  </span>
                </div>
              </template>
            </el-select>
          </div>
          
          <!-- 客户表单信息 - 统一使用表单样式 -->
          <div class="customer-form-container mt-4">
            <el-form :model="customerFormDisplay" label-width="100px" class="form-no-padding customer-form">
              <el-form-item :label="$t('customer.email') + ' *'" prop="email">
                <el-input v-model="customerFormDisplay.email" :placeholder="$t('customer.placeholder.email')" disabled />
              </el-form-item>

              <el-form-item :label="$t('customer.form.firstName') + ' *'" prop="firstName" style="margin-bottom: 18px">
                <el-col :span="10">
                  <el-input v-model="customerFormDisplay.firstName" :placeholder="$t('customer.placeholder.firstName')" disabled />
                </el-col>
                <el-col :span="4" style="text-align: center; padding: 0 8px; margin-left: -20px">{{ $t('customer.form.lastName') + ' *' }}</el-col>
                <el-col :span="10">
                  <el-input v-model="customerFormDisplay.lastName" :placeholder="$t('customer.placeholder.lastName')" disabled />
                </el-col>
              </el-form-item>

              <el-form-item :label="$t('customer.form.idType') + ' *'" prop="idType" style="margin-bottom: 18px">
                <el-col :span="10">
                  <el-select v-model="customerFormDisplay.idType" :placeholder="t('customer.placeholder.idType')" disabled>
                    <el-option
                      v-for="item in idTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-col>
                <el-col :span="4" style="text-align: center; padding: 0 8px; margin-left: -20px">{{ $t('customer.form.idNumber') + ' *' }}</el-col>
                <el-col :span="10">
                  <el-input v-model="customerFormDisplay.idNumber" :placeholder="$t('customer.placeholder.idNumber')" disabled />
                </el-col>
              </el-form-item>

              <el-form-item :label="$t('customer.form.licenseAddress') + ' *'" prop="licenseAddress">
                <el-input v-model="customerFormDisplay.residenceAddress" :placeholder="$t('customer.placeholder.licenseAddress')" disabled />
              </el-form-item>

              <el-form-item :label="$t('customer.form.contactAddress') + ' *'" prop="contactAddress">
                <el-input v-model="customerFormDisplay.mailingAddress" :placeholder="$t('customer.placeholder.contactAddress')" disabled />
              </el-form-item>
              
              <el-form-item :label="$t('customer.form.phone1') + ' *'" prop="phone1" style="margin-bottom: 18px">
                <el-col :span="10">
                  <el-input v-model="customerFormDisplay.phone1" :placeholder="$t('customer.placeholder.phone')" disabled />
                </el-col>
                <el-col :span="4" style="text-align: center; padding: 0 8px">{{ $t('customer.form.phone2') }}</el-col>
                <el-col :span="10">
                  <el-input v-model="customerFormDisplay.phone2" :placeholder="$t('customer.placeholder.phone2')" disabled />
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 步骤3：订单信息 -->
        <div v-if="activeStep === 2" class="step-order-info">
          <h3 class="step-title">{{ $t('order.orderBasicInfo') }}</h3>
          <el-form :model="orderForm" :rules="orderRules" label-width="120px" class="order-form" ref="orderFormRef">
            <!-- 订单基本信息 -->
            <div class="order-section">
              <div class="section-divider"></div>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('order.orderDate')" required prop="startDate">
                    <el-date-picker
                      v-model="orderForm.startDate"
                      type="date"
                      style="width: 100%"
                      :placeholder="$t('order.selectStartDate')"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('order.orderType')" required prop="type">
                    <el-radio-group v-model="orderForm.type">
                      <el-radio label="installment">{{ $t('order.types.installment') }}</el-radio>
                      <el-radio label="rental">{{ $t('order.types.rental') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <!-- 付款信息 -->
            <h3 class="step-title">{{ $t('order.paymentInfo') }}</h3>
            <div class="payment-section">
              <div class="section-divider"></div>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('order.initialPayment')" prop="payment.initialPayment">
                    <div class="amount-input">
                      <el-input 
                        v-model="orderForm.payment.initialPayment" 
                        placeholder="0.00"
                        @input="newValue => handleAmountInput(newValue, 'initialPayment')"
                        maxlength="10"
                      />
                      <span class="currency-symbol">¥</span>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('order.firstPaymentDate')" prop="payment.firstPaymentDate">
                    <el-date-picker
                      v-model="orderForm.payment.firstPaymentDate"
                      type="date"
                      style="width: 100%"
                      :placeholder="$t('order.selectFirstPaymentDate')"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('order.periodicPayment')" prop="payment.periodicPayment">
                    <div class="amount-input">
                      <el-input 
                        v-model="orderForm.payment.periodicPayment" 
                        placeholder="0.00"
                        @input="newValue => handleAmountInput(newValue, 'periodicPayment')"
                        maxlength="10"
                      />
                      <span class="currency-symbol">¥</span>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('order.numberOfInstallments')" prop="payment.installments">
                    <el-input 
                      v-model.number="orderForm.payment.installments" 
                      :placeholder="$t('order.enterNumberOfInstallments')"
                      @input="newValue => handleIntegerInput(newValue, 'installments')"
                      maxlength="3"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="16">
                  <el-form-item :label="$t('order.periodicLength')" prop="payment.periodicLength">
                    <el-row :gutter="10" class="length-unit-container">
                      <el-col :span="16">
                        <el-input 
                          v-model.number="orderForm.payment.periodicLength" 
                          :placeholder="$t('order.enterPeriodicLength')" 
                          maxlength="3"
                        />
                      </el-col>
                      <el-col :span="8">
                        <el-select v-model="orderForm.payment.periodicLengthUnit" class="unit-select">
                          <el-option :label="$t('order.day')" value="day" />
                          <el-option :label="$t('order.week')" value="week" />
                          <el-option :label="$t('order.month')" value="month" />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('order.totalAmount')">
                    <div class="amount-input">
                      <el-input v-model="totalAmount" disabled placeholder="0.00" />
                      <span class="currency-symbol">¥</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <!-- 其他费用信息 -->
            <h3 class="step-title">{{ $t('order.otherFees') }}</h3>
            <div class="fees-section">
              <div class="section-divider"></div>
              
              <el-row :gutter="20">
                <el-col :span="20">
                  <!-- 违约计算 -->
                  <el-form-item :label="$t('order.penaltyType')" required>
                    <el-row :gutter="10">
                      <el-col :span="6">
                        <el-select 
                          v-model="orderForm.payment.penaltyCalculationType" 
                          placeholder="每天"
                          @change="() => orderFormRef.validateField('penaltyCalculationType')"
                        >
                          <el-option label="每天" value="DAILY" />
                          <el-option label="每周" value="WEEKLY" />
                          <el-option label="每月" value="MONTHLY" />
                        </el-select>
                      </el-col>
                      <el-col :span="6">
                        <el-input 
                          v-model="orderForm.payment.penaltyValue" 
                          placeholder="0"
                          @input="handlePenaltyValueInput"
                          maxlength="3"
                        />
                      </el-col>
                      <el-col :span="4">
                        <el-select 
                          v-model="orderForm.payment.penaltyAmountType" 
                          placeholder="单位"
                          @change="() => orderFormRef.validateField('penaltyAmountType')"
                        >
                          <el-option label="元" value="FIXED_AMOUNT" />
                          <el-option label="%" value="PERCENTAGE_AMOUNT" />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="20">
                  <el-form-item :label="$t('order.deposit')" prop="payment.depositAmount" class="deposit-form-item">
                    <div class="deposit-container-flex">
                      <el-input
                        v-model="orderForm.payment.depositAmount"
                        placeholder="0.00"
                        class="deposit-amount-input"
                        @input="newValue => handleAmountInput(newValue, 'depositAmount')"
                        maxlength="8"
                      />
                      <span class="currency-symbol-deposit">¥</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 总服务费 -->
              <el-form-item :label="$t('order.totalServiceFee')" prop="payment.serviceFee" class="service-fee-form-item">
                <div class="service-fee-container">
                  <el-input 
                    v-model="orderForm.payment.serviceFee" 
                    placeholder="0.00" 
                    class="service-fee-input"
                    @input="newValue => handleAmountInput(newValue, 'serviceFee')"
                    maxlength="8"
                  />
                  <span class="currency-symbol-service-fee">¥</span>
                </div>
              </el-form-item>
              
              <!-- 备注 -->
              <el-form-item :label="$t('order.remarks')" class="remarks-form-item">
                <el-input 
                  v-model="orderForm.remarks" 
                  type="textarea"
                  :rows="3"
                  :placeholder="$t('order.remarksPlaceholder')"
                  maxlength="256"
                  show-word-limit
                  class="remarks-input"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 步骤4：付款计划 -->
        <div v-if="activeStep === 3" class="step-payment">
          <h3 class="step-title">{{ $t('order.steps.paymentPlan') }}</h3>
          <div class="payment-info-alert">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <span>{{ $t('order.paymentPlanTip') }}</span>
            </el-alert>
          </div>
          
          <!-- 付款计划表格 -->
          <div class="payment-plan-table">
            <el-table 
              :data="paymentPlan" 
              style="width: 100%" 
              border
              highlight-current-row
            >
              <el-table-column 
                prop="index" 
                :label="$t('order.serialNumber')" 
                width="70" 
                align="center"
                fixed
              >
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              
              <el-table-column 
                prop="installmentNo" 
                :label="$t('order.installmentNumber')" 
                width="90" 
                align="center"
              >
                <template #default="scope">
                  <span v-if="scope.row.type === 'installment'">{{ $t('order.installmentPeriod', { n: scope.row.installmentNo }) }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              
              <el-table-column 
                prop="type" 
                :label="$t('order.paymentTypeLabel')" 
                width="120"
                align="center"
              >
                <template #default="scope">
                  <span v-if="scope.row.type === 'initial'">{{ $t('order.initialPaymentType') }}</span>
                  <span v-else-if="scope.row.type === 'deposit'">{{ $t('order.depositType') }}</span>
                  <span v-else>{{ $t('order.installmentType') }}</span>
                </template>
              </el-table-column>
              
              <el-table-column 
                prop="amount" 
                :label="$t('order.amountLabel')" 
                width="120"
                align="right"
              >
                <template #default="scope">
                  <el-input 
                    v-if="scope.row.type === 'installment'"
                    v-model="scope.row.amount" 
                    placeholder="0.00"
                    class="amount-input-cell"
                    @input="handleAmountChange(scope.row, scope.$index)"
                    @change="handleAmountBlur(scope.row)"
                    maxlength="10"
                  />
                  <span v-else>¥{{ parseFloat(scope.row.amount).toFixed(2) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column 
                prop="serviceFee" 
                :label="$t('order.serviceFeeLabel')" 
                width="100"
                align="right"
              >
                <template #default="scope">
                  <el-input 
                    v-if="scope.row.type === 'installment'"
                    v-model="scope.row.serviceFee" 
                    placeholder="0.00"
                    class="fee-input-cell"
                    @input="handleServiceFeeChange(scope.row, scope.$index)"
                    @change="handleServiceFeeBlur(scope.row)"
                    maxlength="10"
                  />
                  <span v-else>¥{{ parseFloat(scope.row.serviceFee).toFixed(2) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column 
                prop="dueDate" 
                :label="$t('order.dueDateLabel')"
                min-width="110"
                align="center"
              />
            </el-table>
            
            <!-- 付款计划统计 -->
            <div class="payment-plan-summary">
              <div class="summary-total">
                <span class="summary-label">{{ $t('order.totalLabel') }}:</span>
                <div class="summary-values">
                  <div class="summary-item">
                    <span class="amount-label">{{ $t('order.totalPaymentAmount') }}:</span>
                    <span class="amount-value">¥{{ totalPaymentAmount }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="fee-label">{{ $t('order.totalServiceFee') }}:</span>
                    <span class="fee-value">¥{{ totalServiceFeeAmount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤5：预览确认 -->
        <div v-if="activeStep === 4" class="step-preview">
          <h3 class="step-title">{{ $t('order.steps.preview') }}</h3>
          <div class="preview-content">


                        <!-- 设备信息区域 -->
            <div class="preview-card">
              <div class="preview-card-header">
                <div class="card-icon">
                  <el-icon><Monitor /></el-icon>
                </div>
                <h4>{{ $t('device.information') }}</h4>
              </div>
              <div class="preview-card-body">
                <div class="preview-grid">
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('device.serialNumber') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? selectedDevices[0].serialNumber : 'RF8N82799KA' }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('device.status.label') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? t('device.status.' + selectedDevices[0].status, selectedDevices[0].status) : t('device.status.IN_STOCK') }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('device.brand.label') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? brandLabel : 'samsung' }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('device.color.label') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? deviceColorLabel : '黑色' }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('device.model') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? selectedDevices[0].model : 'SM-A217F' }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ t('orderDialog.preview.imei1Label') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? selectedDevices[0].imei1 : '351856221016264' }}</div>
                  </div>
                  <div class="preview-grid-item full-width">
                    <div class="preview-label">{{ $t('device.description') }}</div>
                    <div class="preview-value">{{ selectedDevices.length > 0 ? selectedDevices[0].details : '三星 Galaxy A21s 智能手机，4GB+64GB，黑色' }}</div>
                  </div>
                  <!-- <div class="preview-grid-item full-width">
                    <div class="preview-label">{{ $t('device.accessories') }}</div>
                    <div class="preview-value">
                      {{
                        selectedDevices.length > 0 && selectedDevices[0].attachments && selectedDevices[0].attachments.length > 0 
                        ? selectedDevices[0].attachments.map(a => a.fileName).join(', ') 
                        : '' 
                      }}
                    </div>
                  </div> -->
                </div>
              </div>
            </div>
            
            <!-- 客户信息区域 -->
            <div class="preview-card">
              <div class="preview-card-header">
                <div class="card-icon">
                  <el-icon><User /></el-icon>
                </div>
                <h4>{{ $t('customer.information') }}</h4>
              </div>
              <div class="preview-card-body">
                <div class="preview-grid">
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('customer.name') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.name : customerFormDisplay.lastName + customerFormDisplay.firstName }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('customer.form.idType') }}</div>
                    <div class="preview-value">{{ idTypeLabel }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('customer.form.idNumber') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.idNumber : customerFormDisplay.idNumber }}</div>
                  </div>
                  <div class="preview-grid-item"></div>
                  <div class="preview-grid-item full-width">
                    <div class="preview-label">{{ $t('customer.form.licenseAddress') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.residenceAddress : customerFormDisplay.licenseAddress }}</div>
                  </div>
                  <div class="preview-grid-item full-width">
                    <div class="preview-label">{{ $t('customer.form.contactAddress') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.mailingAddress : customerFormDisplay.contactAddress }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('customer.form.phone1') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.phone : customerFormDisplay.phone1 }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('common.email') }}</div>
                    <div class="preview-value">{{ selectedCustomer ? selectedCustomer.email : customerFormDisplay.email }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 订单信息区域 -->
            <div class="preview-card">
              <div class="preview-card-header">
                <div class="card-icon">
                  <Document />
                </div>
                <h4>{{ $t('order.information') }}</h4>
              </div>
              <div class="preview-card-body">
                <div class="preview-grid">
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.orderNo') }}</div>
                    <div class="preview-value">{{ orderNumber || 'ORD' + new Date().getTime().toString().slice(-10) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('common.creator') }}</div>
                    <div class="preview-value">{{ creator }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('common.createTime') }}</div>
                    <div class="preview-value">{{ new Date().toLocaleString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit'}).replace(/\//g, '/') }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.orderStatus') }}</div>
                    <div class="preview-value">{{ t('orderDialog.preview.statusPendingConfirmation') }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.orderDate') }}</div>
                    <div class="preview-value">{{ new Date().toISOString().slice(0, 10) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.orderType') }}</div>
                    <div class="preview-value">{{ $t(`order.types.${orderForm.type}`) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.startDate') }}</div>
                    <div class="preview-value">{{ orderForm.startDate }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.initialPayment') }}</div>
                    <div class="preview-value">¥{{ parseFloat(orderForm.payment.initialPayment || 0).toFixed(2) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.numberOfInstallments') }}</div>
                    <div class="preview-value">{{ orderForm.payment.installments || 0 }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.periodicPayment') }}</div>
                    <div class="preview-value">¥{{ parseFloat(orderForm.payment.periodicPayment || 0).toFixed(2) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.periodicLength') }}</div>
                    <div class="preview-value">{{ orderForm.payment.periodicLength || 0 }} {{ $t(`order.${orderForm.payment.periodicLengthUnit}`) }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.totalAmount') }}</div>
                    <div class="preview-value">¥{{ totalAmount }}</div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.penaltyType') }}</div>
                    <div class="preview-value">
                      {{ orderForm.payment.penaltyCalculationType === 'DAILY' ? '每天' : 
                         orderForm.payment.penaltyCalculationType === 'WEEKLY' ? '每周' : 
                         orderForm.payment.penaltyCalculationType === 'MONTHLY' ? '每月' : '' }}
                      {{ orderForm.payment.penaltyValue || '0' }}
                      {{ orderForm.payment.penaltyAmountType === 'PERCENTAGE_AMOUNT' ? '%' : '元' }}
                    </div>
                  </div>
                  <div class="preview-grid-item">
                    <div class="preview-label">{{ $t('order.deposit') }}</div>
                    <div class="preview-value">¥{{ parseFloat(orderForm.payment.depositAmount || 0).toFixed(2) }}</div>
                  </div>
                </div>
                <div class="preview-remarks">
                  <div class="preview-label">{{ $t('order.remarks') }}</div>
                  <div class="preview-value">{{ orderForm.remarks || "-"}}</div>
                </div>
              </div>
            </div>
            
            <!-- 付款计划区域 -->
            <div class="preview-card">
              <div class="preview-card-header">
                <div class="card-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <h4>{{ $t('order.paymentPlanTitle') }}</h4>
              </div>
              <div class="preview-card-body">
                <div class="payment-plan-table">
                  <el-table :data="paymentPlan" style="width: 100%" border>
                    <el-table-column prop="index" :label="$t('order.serialNumber')" width="80" align="center">
                      <template #default="scope">
                        <span>{{ scope.$index + 1 }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="installmentNo" :label="$t('order.installmentNumber')" width="120" align="center">
                      <template #default="scope">
                        <span v-if="scope.row.type === 'deposit'">-</span>
                        <span v-else-if="scope.row.type === 'initial'">-</span>
                        <span v-else>{{ $t('order.installmentPeriod', { n: scope.row.installmentNo }) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="type" :label="$t('order.paymentTypeLabel')" width="120" align="center">
                      <template #default="scope">
                        <span v-if="scope.row.type === 'initial'">{{ $t('order.initialPaymentType') }}</span>
                        <span v-else-if="scope.row.type === 'deposit'">{{ $t('order.depositType') }}</span>
                        <span v-else>{{ $t('order.installmentType') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" :label="$t('order.amountLabel')" width="150" align="right">
                      <template #default="scope">
                        <span>{{ parseFloat(scope.row.amount).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="serviceFee" :label="$t('order.serviceFeeLabel')" width="120" align="right">
                      <template #default="scope">
                        <span>{{ parseFloat(scope.row.serviceFee).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="dueDate" :label="$t('order.dueDateLabel')" width="150" align="center" />
                    <el-table-column prop="status" :label="$t('order.statusLabel')" align="center">
                      <template #default="scope">
                        <el-tag type="info" v-if="scope.row.status === 'pending'">{{ $t('order.paymentPending') }}</el-tag>
                        <el-tag type="success" v-else-if="scope.row.status === 'paid'">{{ $t('order.paymentPaid') }}</el-tag>
                        <el-tag type="danger" v-else-if="scope.row.status === 'overdue'">{{ $t('order.paymentOverdue') }}</el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                  
                  <!-- 付款计划统计 -->
                  <div class="payment-plan-summary">
                    <div class="summary-content">
                      <div class="summary-item">
                        <span class="summary-label">{{ $t('order.totalLabel') }}:</span>
                      </div>
                      <div class="summary-values">
                        <div class="summary-detail">
                          <span class="amount-label">{{ $t('order.totalPaymentAmount') }}:</span>
                          <span class="amount-value">¥{{ parseFloat(totalPaymentAmount || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                        </div>
                        <div class="summary-detail">
                          <span class="fee-label">{{ $t('order.totalServiceFee') }}:</span>
                          <span class="fee-value">¥{{ parseFloat(totalServiceFeeAmount || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="activeStep > 0" @click="prev">{{ $t('common.prev') }}</el-button>
        <el-button 
          v-if="activeStep < 4" 
          type="primary" 
          @click="next"
          :disabled="!canProceed"
        >
          {{ $t('common.next') }}
        </el-button>
        <!-- Removed the first submit button -->
        <el-button
          v-if="activeStep === 4"
          type="success"
          @click="handleSubmitAndCompleteOrder"
          :loading="completing"
          style="margin-left: 10px;"
        >
          {{ $t('common.submit') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 创建设备对话框 -->
  <CreateDeviceDialog
    v-model:visible="createDeviceDialogVisible"
    @success="handleDeviceCreated"
    :showStatusFieldOnCreate="false"
    context="order-creation"
  />

  <!-- 创建客户对话框 -->
  <CreateCustomerDialog
    v-model:visible="createCustomerDialogVisible"
    @success="handleCustomerCreated"
  />
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, CirclePlus, Document, User, Monitor, Money } from '@element-plus/icons-vue'
import CreateDeviceDialog from '@/components/Device/CreateDeviceDialog.vue'
import CreateCustomerDialog from '@/components/customer/CreateCustomerDialog.vue'
import { searchDevices as apiSearchDevices } from '@/api/device'
import { searchCustomers as apiSearchCustomers, createCustomer as apiCreateCustomer } from '@/api/customer'
import { createOrder, OrderTypes, PaymentPlanType, PaymentPlanStatus, PenaltyType, completeOrder } from '@/api/order'
import { useUserStore } from '@/stores/modules/user'
import { getDictFields } from '@/api/dictionary'

// 定义props和emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'created'])

// i18n
const { t } = useI18n()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 证件类型列表
// const idTypes = {
// idCard: '身份证',
// passport: '护照',
// driverLicense: '驾驶证'
// };

// 表单引用
const orderFormRef = ref(null);
// const paymentFormRef = ref(null);

// 当前步骤
const activeStep = ref(0);

// 设备相关
const deviceList = ref([]);
const deviceLoading = ref(false);
const selectedDevices = ref([]);
const createDeviceDialogVisible = ref(false);
const deviceBrandInput = ref('');
const deviceSnInput = ref('');
const deviceColorInput = ref('');
const deviceDetailsInput = ref('');
const deviceImei1Input = ref('');
const deviceImei2Input = ref('');
const deviceAttachments = ref([]);
const deviceFound = ref(false);
const selectedDeviceId = ref('');
const deviceModelInput = ref(''); // Add this line
const colorOptions = ref([]);
const brandOptions = ref([]);

// 客户相关
const customerOption = ref('existing');
const customerSearchKeyword = ref('');
const customerList = ref([]);
const customerLoading = ref(false);
const selectedCustomer = ref(null);
const selectedCustomerId = ref('');
const customerFound = ref(false);
const createCustomerDialogVisible = ref(false);
const idTypeOptions = ref([]);
const customerFormData = ref({
  email: '',
  firstName: '',
  lastName: '',
  idType: '',
  idNumber: '',
  licenseAddress: '',
  contactAddress: '',
  phone1: '',
  phone2: ''
});
const sameAddressAsLicense = ref(true);

const idTypeLabel = computed(() => {
  const currentIdType = selectedCustomer.value ? selectedCustomer.value.idType : customerFormDisplay.value.idType;
  const option = idTypeOptions.value.find(opt => opt.value === currentIdType);
  return option ? option.label : currentIdType;
});

const deviceColorLabel = computed(() => {
  if (selectedDevices.value.length > 0) {
    const color = selectedDevices.value[0].color;
    if (color) {
      const option = colorOptions.value.find(opt => opt.value === color);
      return option ? option.label : color;
    }
    return '--'; // Device selected, but no color
  }
  return ''; // Should not happen when used in ternary
});

const brandLabel = computed(() => {
  if (selectedDevices.value.length > 0) {
    const brand = selectedDevices.value[0].brand;
    if (brand) {
      const option = brandOptions.value.find(opt => opt.value === brand);
      return option ? option.label : brand;
    }
    return '--'; // Device selected, but no brand
  }
  return ''; // Should not happen when used in ternary
});

// 客户表单显示数据（用于统一显示选中的客户或默认表单）
const customerFormDisplay = computed(() => {
  if (selectedCustomer.value) {
    // 将选中的客户数据转为表单格式
    const nameParts = selectedCustomer.value.name.split(' ');
    let firstName = selectedCustomer.value.firstName;
    let lastName = selectedCustomer.value.lastName;
    
    // if (nameParts.length > 1) {
    //   firstName = nameParts.slice(1).join(' ');
    //   lastName = nameParts[0];
    // } else if (selectedCustomer.value.name.length > 1) {
    //   // 中文名字处理：姓放在lastName，名放在firstName
    //   lastName = selectedCustomer.value.name.substring(0, 1);
    //   firstName = selectedCustomer.value.name.substring(1);
    // }
    
    return {
      email: selectedCustomer.value.email || '',
      firstName: firstName,
      lastName: lastName,
      idType: selectedCustomer.value.idType || '',
      idNumber: selectedCustomer.value.idNumber || '',
      residenceAddress: selectedCustomer.value.residenceAddress || '',
      mailingAddress: selectedCustomer.value.mailingAddress || '',
      phone1: selectedCustomer.value.phone || '',
      phone2: ''
    };
  } else {
    // 返回默认表单数据
    return customerFormData.value;
  }
});

// 订单信息
const orderForm = ref({
  type: '',
  startDate: new Date().toISOString().split('T')[0],
  duration: 12,
  remarks: '',
  payment: {
    method: 'cash',
    frequency: 'monthly',
    needDeposit: true,
    depositAmount: '',
    depositCurrency: 'CNY',
    initialPayment: '',
    firstPaymentDate: new Date().toISOString().split('T')[0],
    periodicPayment: '',
    installments: '',
    periodicLength: '',
    periodicLengthUnit: 'day',
    penaltyCalculationType: 'DAILY',
    penaltyAmountType: 'FIXED_AMOUNT',
    penaltyValue: '0',
    serviceFee: '',
    lastUpdated: Date.now()
  }
});

// 付款信息
// const paymentForm = ref({
//   method: 'cash',
//   frequency: 'monthly',
//   needDeposit: true,
//   depositAmount: 1000,
//   depositCurrency: 'CNY',
//   initialPayment: '0.00',
//   firstPaymentDate: new Date().toISOString().split('T')[0],
//   periodicPayment: '0.00',
//   installments: '',
//   periodicLength: '',
//   periodicLengthUnit: 'day',
//   penaltyCalculationType: 'DAILY',
//   penaltyAmountType: 'FIXED_AMOUNT',
//   penaltyValue: '0.00',
//   serviceFee: '0.00',
//   lastUpdated: Date.now()
// });

// 订单编号（用于预览页面）
const orderNumber = ref('');

// 订单表单验证规则
const orderRules = ref({
  // 订单基本信息验证规则
  startDate: [
    { required: true, message: t('order.validation.startDateRequired'), trigger: 'change' }
  ],
  type: [
    { required: true, message: t('order.validation.typeRequired'), trigger: 'change' }
  ],
  
  // 付款信息验证规则  
  'payment.initialPayment': [
    { required: true, message: t('order.validation.initialPaymentRequired'), trigger: 'change' },
    { 
      validator: (rule, value, callback) => {
        const num = parseFloat(value);
        if (isNaN(num) || num < 0 || !/^\d+(\.\d{1,2})?$/.test(value.toString())) {
          callback(new Error(t('order.validation.amountInvalid'))); 
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  'payment.firstPaymentDate': [
    { required: true, message: t('order.validation.firstPaymentDateRequired'), trigger: 'change' }
  ],
  'payment.periodicPayment': [
    { required: true, message: t('order.validation.periodicPaymentRequired'), trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        // An empty value is handled by the `required` rule, so we can ignore it here.
        if (!value) {
          callback()
          return
        }

        // First, check if the format is a valid currency format.
        if (!/^\d+(\.\d{1,2})?$/.test(value.toString())) {
          callback(new Error(t('order.validation.amountInvalid')))
        } else if (parseFloat(value) < 0) {
          // Then, check if the numeric value is greater than or equal to 0.
          callback(new Error(t('order.validation.positiveAmount')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  'payment.installments': [
    { required: true, message: t('order.validation.installmentsRequired'), trigger: 'change' },
    { 
      validator: (rule, value, callback) => {
        const num = Number(value);
        if (isNaN(num) || num <= 0 || !Number.isInteger(num)) {
          callback(new Error(t('order.validation.positiveInteger'))); 
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  'payment.periodicLength': [
    { required: true, message: t('order.validation.periodicLengthRequired'), trigger: 'change' },
    { 
      validator: (rule, value, callback) => {
        const num = Number(value);
        if (isNaN(num) || num <= 0 || !Number.isInteger(num)) {
          callback(new Error(t('order.validation.positiveInteger'))); 
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  
  // 其他费用验证规则
  'payment.penaltyCalculationType': [
    { required: true, message: t('order.validation.penaltyTypeRequired'), trigger: 'change' }
  ],
  'payment.penaltyValue': [
    { required: true, message: t('order.validation.penaltyValueRequired'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        const num = Number(value);
        if (isNaN(num) || !Number.isInteger(num) || num <= 0) {
          callback(new Error(t('order.validation.positiveInteger')));
        } else {
          callback();
        }
      }, 
      trigger: 'blur'
    }
  ],
  'payment.penaltyAmountType': [
    { required: true, message: t('order.validation.penaltyAmountTypeRequired'), trigger: 'change' }
  ],
  'payment.depositAmount': [
    { required: true, message: t('order.validation.depositAmountRequired'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          callback(new Error(t('order.validation.positiveAmount')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  'payment.serviceFee': [
    { required: true, message: t('order.validation.serviceFeeRequired'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          callback(new Error(t('order.validation.positiveAmount')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 违约金表单验证规则 (此部分现在已合并到 orderRules，因此可以删除或清空)
const penaltyRules = ref({}); // 清空或删除此行

// 提交状态
const submitting = ref(false);
const completing = ref(false); // New loading state for completing order

// 计算总金额（根据首付、分期付款和其他费用自动计算）
const totalAmount = computed(() => {
  let total = 0;
  
  // 首付金额
  const initialPayment = parseFloat(orderForm.value.payment.initialPayment) || 0;
  
  // 分期付款总额
  const periodicPayment = parseFloat(orderForm.value.payment.periodicPayment) || 0;
  const installments = parseInt(orderForm.value.payment.installments) || 0;
  const installmentTotal = periodicPayment * installments;
  
  // 服务费
  // const serviceFee = parseFloat(orderForm.value.payment.serviceFee) || 0;
  
  // 计算总额
  total = initialPayment + installmentTotal;
  
  // 返回格式化后的总额（保留两位小数）
  return parseFloat(total.toFixed(2));
});

// 总付款金额（从付款计划中计算）
const totalPaymentAmount = computed(() => {
  let total = 0;
  paymentPlan.value.forEach(item => {
    total += parseFloat(item.amount) || 0;
  });
  return total.toFixed(2);
});

// 总服务费（从付款计划中计算）
const totalServiceFeeAmount = computed(() => {
  let total = 0;
  paymentPlan.value.forEach(item => {
    total += parseFloat(item.serviceFee) || 0;
  });
  return total.toFixed(2);
});

// 月付款金额计算
const monthlyAmount = computed(() => {
  if (orderForm.value.type !== 'rental') {
    return 0;
  }
  
  const periodicPayment = parseFloat(orderForm.value.payment.periodicPayment) || 0;
  
  switch (orderForm.value.payment.frequency) {
    case 'monthly':
      return periodicPayment;
    case 'quarterly':
      return parseFloat((periodicPayment / 3).toFixed(2));
    case 'semiannual':
      return parseFloat((periodicPayment / 6).toFixed(2));
    case 'annual':
      return parseFloat((periodicPayment / 12).toFixed(2));
    default:
      return periodicPayment;
  }
});

// 生成付款计划
const paymentPlan = ref([]);

// 重新生成付款计划的函数
const generatePaymentPlan = () => {
  const result = [];
  editedPaymentRows.value = []; // 重置手动编辑记录

  // 首付款
  if (orderForm.value.payment.initialPayment > 0) {
    result.push({
      type: 'initial',
      amount: orderForm.value.payment.initialPayment,
      serviceFee: 0,
      dueDate: orderForm.value.payment.firstPaymentDate || orderForm.value.startDate || new Date().toISOString().slice(0, 10),
      status: 'pending'
    });
  }

  // 押金
  if (orderForm.value.payment.needDeposit && orderForm.value.payment.depositAmount > 0) {
    result.push({
      type: 'deposit',
      amount: orderForm.value.payment.depositAmount,
      serviceFee: 0,
      dueDate: orderForm.value.payment.firstPaymentDate || orderForm.value.startDate || new Date().toISOString().slice(0, 10),
      status: 'pending'
    });
  }

  // 分期付款
  if (orderForm.value.payment.installments > 0) {
    const totalInstallments = parseInt(orderForm.value.payment.installments, 10);
    const totalServiceFee = parseFloat(orderForm.value.payment.serviceFee) || 0;
    
    // 每期平均服务费，四舍五入到两位小数
    const averageServiceFee = parseFloat((totalServiceFee / totalInstallments).toFixed(2));
    
    // 计算除第一期外所有期数的服务费总和
    const otherInstallmentsServiceFee = averageServiceFee * (totalInstallments - 1);
    
    // 第一期的服务费 = 总服务费 - 其他期数服务费之和
    const firstInstallmentServiceFee = parseFloat((totalServiceFee - otherInstallmentsServiceFee).toFixed(2));

    const firstInstallmentEffectiveDate = new Date(orderForm.value.payment.firstPaymentDate || orderForm.value.startDate || new Date());
    const length = parseInt(orderForm.value.payment.periodicLength) || 0;
    const unit = orderForm.value.payment.periodicLengthUnit;

    for (let i = 0; i < orderForm.value.payment.installments; i++) {
      const installmentDueDate = new Date(firstInstallmentEffectiveDate); // Create a new Date object for each installment

      if (unit === 'day') {
        installmentDueDate.setDate(firstInstallmentEffectiveDate.getDate() + (i * length));
      } else if (unit === 'week') {
        installmentDueDate.setDate(firstInstallmentEffectiveDate.getDate() + (i * length * 7));
      } else if (unit === 'month') {
        installmentDueDate.setMonth(firstInstallmentEffectiveDate.getMonth() + (i * length));
      }
      
      const formattedDate = installmentDueDate.toISOString().slice(0, 10);
      
      const installmentPayment = parseFloat(orderForm.value.payment.periodicPayment);
      // 使用新的服务费计算逻辑
      const installmentServiceFee = (i === 0) ? firstInstallmentServiceFee : averageServiceFee;
      
      result.push({
        type: 'installment',
        installmentNo: i + 1,
        amount: installmentPayment,
        serviceFee: installmentServiceFee,
        dueDate: formattedDate,
        status: 'pending'
      });
    }
  }

  paymentPlan.value = result;
};

// 存储用户编辑过的每期付款数据
const editedPaymentRows = ref([]);

// 监听地址复用
watch(sameAddressAsLicense, (val) => {
  if (val) {
    customerFormData.value.contactAddress = customerFormData.value.licenseAddress;
  }
});

watch(() => customerFormData.value.licenseAddress, (val) => {
  if (sameAddressAsLicense.value) {
    customerFormData.value.contactAddress = val;
  }
});

// 检查是否可以进行下一步
const canProceed = computed(() => {
  switch (activeStep.value) {
    case 0:
      return selectedDevices.value.length > 0;
    case 1:
      if (customerOption.value === 'existing') {
        return selectedCustomer.value !== null;
      } else {
        return customerFormDisplay.value.firstName && 
               customerFormDisplay.value.lastName && 
               customerFormDisplay.value.phone1 &&
               customerFormDisplay.value.idType &&
               customerFormDisplay.value.idNumber &&
               customerFormDisplay.value.licenseAddress &&
               (sameAddressAsLicense.value || customerFormDisplay.value.contactAddress);
      }
    case 2:
      return true; // 让 Element Plus 验证器处理所有订单信息字段
    case 3:
      return orderForm.value.payment.method && 
             (orderForm.value.type !== 'rental' || orderForm.value.payment.frequency) &&
             (!orderForm.value.payment.needDeposit || orderForm.value.payment.depositAmount >= 0);
    default:
      return true;
  }
});

// 打开创建设备对话框
const openCreateDeviceDialog = (e) => {
  // 阻止事件冒泡和默认行为
  if (e) {
    e.stopPropagation();
    e.preventDefault();
  }
  
  // 设置延时以避免事件冲突
  setTimeout(() => {
    createDeviceDialogVisible.value = true;
  }, 100);
};

// 处理创建设备
const handleCreateDevice = (e) => {
  // 阻止事件冒泡
  if (e) {
    e.stopPropagation();
    e.preventDefault();
  }
  
  // 通过简单设置变量来打开对话框，避免复杂事件处理
  setTimeout(() => {
    createDeviceDialogVisible.value = true;
  }, 200); // 增加延迟时间
};

// 处理设备创建成功
const handleDeviceCreated = (device) => {
  // 添加新创建的设备到设备列表
  const brandLabel = brandOptions.value.find(opt => opt.value === device.brand)?.label || device.brand;
  const newDevice = {
    id: device.id || `d${Date.now()}`,
    brand: device.brand || '',
    deviceNumber: device.deviceName || device.sn,
    deviceInfo: `${brandLabel} ${device.model}`,
    serialNumber: device.sn,
    status: device.status || 'IN_STOCK',
    imei1: device.imei1 || '',
    imei2: device.imei2 || '',
    color: device.color || '',
    model: device.model || '', // Map API model to frontend model
    details: device.remark || '', // Map API remark to frontend details
    attachments: device.attachments || []
  };
  
  // 重置搜索状态
  deviceList.value = [newDevice];
  deviceFound.value = true;
  
  // 自动选中新创建的设备
  selectedDeviceId.value = newDevice.id;
  selectedDevices.value = [newDevice];
  
  // 设置设备详情数据
  deviceSnInput.value = newDevice.serialNumber || '';
  const colorOption = colorOptions.value.find(opt => opt.value === newDevice.color);
  deviceColorInput.value = colorOption ? colorOption.label : newDevice.color;
  deviceModelInput.value = newDevice.model || ''; // Populate new model input
  deviceDetailsInput.value = newDevice.details || ''; // This will now get remark
  deviceImei1Input.value = newDevice.imei1 || '';
  deviceImei2Input.value = newDevice.imei2 || '';
  deviceAttachments.value = newDevice.attachments || [];
  
  // ElMessage.success(t('device.createSuccess'));
};

// 搜索设备
const searchDevices = async (query) => {
  if (!query) {
    deviceList.value = [];
    return;
  }
  
  deviceLoading.value = true;
  
  try {
    // 调用API搜索设备
    const response = await apiSearchDevices(query);
    
    // 判断API返回值是否成功 (后端返回格式为 {code: 1000, data: [], message: "操作成功"})
    if (response.code === 1000 && response.data) {
      const devices = response.data || [];
      deviceList.value = devices.map(device => {
        const brandLabel = brandOptions.value.find(opt => opt.value === device.brand)?.label || device.brand;
        return {
          id: device.id,
          deviceNumber: device.deviceName || device.sn,
          deviceInfo: `${brandLabel} ${device.model}`,
          brand: device.brand || '',
          serialNumber: device.sn,
          status: device.status || 'IN_STOCK',
          imei1: device.imei1 || '',
          imei2: device.imei2 || '',
          color: device.color || '',
          model: device.model || '', // Map API model to frontend model
          details: device.remark || '', // Map API remark to frontend details
          attachments: device.attachments || []
        };
      });
      
      // 判断是否找到设备
      deviceFound.value = deviceList.value.length > 0;
    } else {
      console.error('搜索设备失败:', response);
      ElMessage.error(t('device.searchFailed'));
      deviceList.value = [];
      deviceFound.value = false;
    }
  } catch (error) {
    console.error('搜索设备失败:', error);
    ElMessage.error(t('common.fetchFailed'));
    deviceList.value = [];
    deviceFound.value = false; 
  } finally {
    deviceLoading.value = false;
  }
};

// 处理设备选择
const handleDeviceSelect = (deviceId) => {
  if (!deviceId) {
    // 清空选择
    selectedDevices.value = [];
    return;
  }
  
  // 查找选中的设备
  const selectedDevice = deviceList.value.find(device => device.id === deviceId);
  if (!selectedDevice) return;
  
  // 更新选中设备
  selectedDevices.value = [selectedDevice];
  
  // 设置设备详情数据
  deviceSnInput.value = selectedDevice.serialNumber || '';
  const colorOption = colorOptions.value.find(opt => opt.value === selectedDevice.color);
  deviceColorInput.value = colorOption ? colorOption.label : selectedDevice.color;
  deviceModelInput.value = selectedDevice.model || ''; // Populate new model input
  deviceDetailsInput.value = selectedDevice.details || ''; // This will now get remark
  deviceImei1Input.value = selectedDevice.imei1 || '';
  deviceImei2Input.value = selectedDevice.imei2 || '';
  deviceAttachments.value = selectedDevice.attachments || [];
};

// 搜索客户
const searchCustomers = async (query) => {
  if (!query) {
    customerList.value = [];
    return;
  }
  
  customerLoading.value = true;
  
  try {
    // 调用API搜索客户
    const response = await apiSearchCustomers(query);
    
    // 判断API返回值是否成功
    if (response.code === 1000 && response.data) {
      const customers = response.data || [];
      customerList.value = customers.map(customer => ({
        id: customer.id,
        code: customer.customerNo || `C${String(Math.floor(Math.random() * 10000)).padStart(6, '0')}`,
        name: customer.name.split('_').join(''),
        lastName: customer.name.split('_')[0],
        firstName: customer.name.split('_')[1],
        phone: customer.phone || customer.phone1,
        email: customer.email || '',
        idNumber: customer.idNumber || '',
        idType: customer.idType || '',
        residenceAddress: customer.residenceAddress || '',
        mailingAddress: customer.mailingAddress || ''
      }));
      
      // 判断是否找到客户
      customerFound.value = customerList.value.length > 0;
    } else {
      console.error('搜索客户失败:', response);
      ElMessage.error(t('customer.searchFailed'));
      customerList.value = [];
      customerFound.value = false;
    }
  } catch (error) {
    console.error('搜索客户失败:', error);
    ElMessage.error(t('customer.searchFailed'));
    customerList.value = [];
    customerFound.value = false;
  } finally {
    customerLoading.value = false;
  }
};

// 获取设备状态类型
const getDeviceStatusType = (status) => {
  const statusMap = {
    'IN_STOCK': 'success',
    'RENTED': 'warning',
    'REPAIRING': 'info',
    'SCRAPPED': 'danger',
    'LOST': 'danger',
    'IN_TRANSIT': 'primary'
  };
  return statusMap[status] || 'info';
};

// 处理客户选择
const handleCustomerSelect = (customerId) => {
  if (!customerId) {
    selectedCustomer.value = null;
    return;
  }
  
  const customer = customerList.value.find(customer => customer.id === customerId);
  console.log(customer);
  if (!customer) return;
  
  selectedCustomer.value = {
    ...customer,
    residenceAddress: customer.residenceAddress || '',
    mailingAddress: customer.mailingAddress || ''
  };
};

// 下一步
const next = async () => {
  if (activeStep.value === 2) {
    if (!orderFormRef.value) return;
    try {
      await orderFormRef.value.validate(); // Rely on Element Plus validation
      generatePaymentPlan(); // 进入下一步时生成付款计划
      activeStep.value++;
    } catch (error) {
      console.error('表单验证失败:', error);
      // ElMessage.warning('请填写完整的订单信息。'); // Let Element Plus automatically display errors
    }
  } else if (activeStep.value === 3) {
    // 如果是付款计划页面，简化验证
    // if (paymentFormRef.value) {
    //   paymentFormRef.value.clearValidate();
    // }
    activeStep.value++;
  } else {
    activeStep.value++;
  }
};

// 上一步
const prev = () => {
  if (activeStep.value === 3) {
    // 从付款计划返回上一步时，将总服务费同步回去
    orderForm.value.payment.serviceFee = totalServiceFeeAmount.value;
  }
  activeStep.value--;
};

// 处理取消
const handleCancel = () => {
  ElMessageBox.confirm(
    t('order.confirmCancel'),
    t('common.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      dialogVisible.value = false;
      resetForm();
    })
    .catch(() => {});
};

// 处理关闭对话框
const handleClose = (done) => {
  if (activeStep.value === 0 && !selectedDevices.value.length) {
    done();
    resetForm();
    return;
  }
  
  ElMessageBox.confirm(
    t('order.confirmCancel'),
    t('common.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      done();
      resetForm();
    })
    .catch(() => {});
};

// 重置表单
const resetForm = () => {
  activeStep.value = 0;
  selectedDeviceId.value = '';
  selectedDevices.value = [];
  deviceList.value = [];
  deviceFound.value = false;
  customerOption.value = 'existing';
  customerSearchKeyword.value = '';
  selectedCustomer.value = null;
  selectedCustomerId.value = '';
  customerFound.value = false;
  customerFormData.value = {
    email: '',
    firstName: '',
    lastName: '',
    idType: '',
    idNumber: '',
    licenseAddress: '',
    contactAddress: '',
    phone1: '',
    phone2: ''
  };
  sameAddressAsLicense.value = true;
  orderForm.value = {
    type: '',
    startDate: new Date().toISOString().split('T')[0],
    duration: 12,
    remarks: '',
    payment: {
      method: 'cash',
      frequency: 'monthly',
      needDeposit: true,
      depositAmount: '',
      depositCurrency: 'CNY',
      initialPayment: '',
      firstPaymentDate: new Date().toISOString().split('T')[0],
      periodicPayment: '',
      installments: '',
      periodicLength: '',
      periodicLengthUnit: 'day',
      penaltyCalculationType: 'DAILY',
      penaltyAmountType: 'FIXED_AMOUNT',
      penaltyValue: '0',
      serviceFee: '',
      lastUpdated: Date.now()
    }
  };
  deviceModelInput.value = ''; // Add this line
};

// 提交订单
const submitOrder = async () => {
  submitting.value = true;
  try {
    // 构建订单数据对象
    const orderData = {
      // 订单基本信息
      basicInfo: {
        orderType: orderForm.value.type,
        startDate: orderForm.value.startDate,
        remarks: orderForm.value.remarks || ''
      },
      
      // 客户信息
      customer: {
        id: selectedCustomer.value ? selectedCustomer.value.id : null,
        name: selectedCustomer.value ? selectedCustomer.value.name : 
              `${customerFormDisplay.value.lastName}${customerFormDisplay.value.firstName}`,
        phone: selectedCustomer.value ? selectedCustomer.value.phone : customerFormDisplay.value.phone1,
        email: selectedCustomer.value ? selectedCustomer.value.email : customerFormDisplay.value.email,
        idType: selectedCustomer.value ? selectedCustomer.value.idType : customerFormDisplay.value.idType,
        idNumber: selectedCustomer.value ? selectedCustomer.value.idNumber : customerFormDisplay.value.idNumber,
        licenseAddress: selectedCustomer.value ? selectedCustomer.value.licenseAddress : customerFormDisplay.value.licenseAddress,
        contactAddress: selectedCustomer.value ? selectedCustomer.value.contactAddress : customerFormDisplay.value.contactAddress
      },
      
      // 设备信息
      devices: selectedDevices.value.map(device => ({
        id: device.id,
        serialNumber: device.serialNumber,
        brand: device.brand || '',
        model: device.model || '',
        color: device.color || '',
        imei1: device.imei1 || '',
        imei2: device.imei2 || '',
        accessories: device.accessories || '',
        status: device.status || 'IN_STOCK'
      })),
      
      // 付款信息
      payment: {
        initialPayment: parseFloat(orderForm.value.payment.initialPayment) || 0,
        firstPaymentDate: orderForm.value.payment.firstPaymentDate,
        periodicPayment: parseFloat(orderForm.value.payment.periodicPayment) || 0,
        installments: parseInt(orderForm.value.payment.installments) || 0,
        periodicLength: parseInt(orderForm.value.payment.periodicLength) || 0,
        periodicLengthUnit: orderForm.value.payment.periodicLengthUnit,
        totalAmount: totalAmount.value,
        needDeposit: orderForm.value.payment.needDeposit,
        depositAmount: parseFloat(orderForm.value.payment.depositAmount) || 0,
        depositCurrency: orderForm.value.payment.depositCurrency,
        penaltyCalculationType: orderForm.value.payment.penaltyCalculationType,
        penaltyAmountType: orderForm.value.payment.penaltyAmountType,
        penaltyValue: parseFloat(orderForm.value.payment.penaltyValue) || 0,
        serviceFee: parseFloat(orderForm.value.payment.serviceFee) || 0
      },
      
      // 付款计划
      paymentPlan: paymentPlan.value.map(item => {
        // 根据不同的付款类型使用对应的常量
        let type = PaymentPlanType.INSTALLMENT; // Default to installment
        if (item.type === 'initial') {
          type = PaymentPlanType.INITIAL;
        } else if (item.type === 'deposit') {
          type = PaymentPlanType.DEPOSIT;
        }
        
        return {
          type: type,
          installmentNo: item.installmentNo || null,
          amount: parseFloat(item.amount) || 0,
          serviceFee: parseFloat(item.serviceFee) || 0,
          dueDate: item.dueDate,
          status: PaymentPlanStatus.PENDING // Default to pending
        };
      })
    };
    
    // 调用API创建订单
    const response = await createOrder(orderData);
    
    // 根据API响应处理结果
    if (response && response.code === 1000) {
      ElMessage.success(t('order.createSuccess'));
      dialogVisible.value = false;
      resetForm();
      emit('created', response.data);
    } else {
      // 处理API返回的错误
      throw new Error(response.message || t('order.createError'));
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    ElMessage.error(error.message || t('order.createError'));
  } finally {
    submitting.value = false;
  }
};

// 提交并完成订单
const handleSubmitAndCompleteOrder = async () => {
  ElMessageBox.confirm(
    t('order.dialog.confirmSaveContent'),
    t('order.dialog.confirmSaveTitle'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    completing.value = true;
    try {
      // 构建订单数据对象 (与 submitOrder 逻辑相同)
      const orderData = {
        basicInfo: {
          orderType: orderForm.value.type,
          startDate: orderForm.value.payment.firstPaymentDate,
          remarks: orderForm.value.remarks || ''
        },
        customer: {
          id: selectedCustomer.value ? selectedCustomer.value.id : null,
          name: selectedCustomer.value ? selectedCustomer.value.name : 
                `${customerFormDisplay.value.lastName}${customerFormDisplay.value.firstName}`,
          phone: selectedCustomer.value ? selectedCustomer.value.phone : customerFormDisplay.value.phone1,
          email: selectedCustomer.value ? selectedCustomer.value.email : customerFormDisplay.value.email,
          idType: selectedCustomer.value ? selectedCustomer.value.idType : customerFormDisplay.value.idType,
          idNumber: selectedCustomer.value ? selectedCustomer.value.idNumber : customerFormDisplay.value.idNumber,
          licenseAddress: selectedCustomer.value ? selectedCustomer.value.licenseAddress : customerFormDisplay.value.licenseAddress,
          contactAddress: selectedCustomer.value ? selectedCustomer.value.contactAddress : customerFormDisplay.value.contactAddress
        },
        devices: selectedDevices.value.map(device => ({
          id: device.id,
          serialNumber: device.serialNumber,
          brand: device.brand || '',
          model: device.model || '',
          color: device.color || '',
          imei1: device.imei1 || '',
          imei2: device.imei2 || '',
          accessories: device.accessories || '',
          status: device.status || 'IN_STOCK'
        })),
        payment: {
          initialPayment: parseFloat(orderForm.value.payment.initialPayment) || 0,
          firstPaymentDate: orderForm.value.payment.firstPaymentDate,
          periodicPayment: parseFloat(orderForm.value.payment.periodicPayment) || 0,
          installments: parseInt(orderForm.value.payment.installments) || 0,
          periodicLength: parseInt(orderForm.value.payment.periodicLength) || 0,
          periodicLengthUnit: orderForm.value.payment.periodicLengthUnit,
          totalAmount: totalAmount.value,
          needDeposit: orderForm.value.payment.needDeposit,
          depositAmount: parseFloat(orderForm.value.payment.depositAmount) || 0,
          depositCurrency: orderForm.value.payment.depositCurrency,
          penaltyCalculationType: orderForm.value.payment.penaltyCalculationType,
          penaltyAmountType: orderForm.value.payment.penaltyAmountType,
          penaltyValue: parseFloat(orderForm.value.payment.penaltyValue) || 0,
          serviceFee: parseFloat(orderForm.value.payment.serviceFee) || 0
        },
        paymentPlan: paymentPlan.value.map(item => {
          let type = PaymentPlanType.INSTALLMENT;
          if (item.type === 'initial') {
            type = PaymentPlanType.INITIAL;
          } else if (item.type === 'deposit') {
            type = PaymentPlanType.DEPOSIT;
          }
          return {
            type: type,
            installmentNo: item.installmentNo || null,
            amount: parseFloat(item.amount) || 0,
            serviceFee: parseFloat(item.serviceFee) || 0,
            dueDate: item.dueDate,
            status: PaymentPlanStatus.PENDING
          };
        })
      };
      
      // 调用API完成订单
      const response = await completeOrder(orderData);
      
      if (response && response.code === 1000) {
        // ElMessage.success(t('order.completeSuccessMessage') || 'Order completed successfully!'); // Fallback i18n
        dialogVisible.value = false;
        resetForm();
        emit('created', response.data); // Assuming 'created' event is suitable
      } else {
        throw new Error(response.message || t('order.completeErrorMessage') || 'Failed to complete order.'); // Fallback i18n
      }
    } catch (error) {
      console.error('完成订单失败:', error);
      ElMessage.error(error.message || t('order.completeErrorMessage') || 'Failed to complete order.'); // Fallback i18n
    } finally {
      completing.value = false;
    }
  }).catch(() => {
    // 用户取消操作，无需提示
  });
};

// 初始化
const init = () => {
  // 重置设备相关数据
  selectedDeviceId.value = '';
  deviceList.value = [];
  selectedDevices.value = [];
  deviceBrandInput.value = '';
  deviceSnInput.value = '';
  deviceColorInput.value = '';
  deviceDetailsInput.value = '';
  deviceImei1Input.value = '';
  deviceImei2Input.value = '';
  deviceAttachments.value = [];
  deviceFound.value = false;
  
  // 重置客户相关数据
  selectedCustomerId.value = '';
  selectedCustomer.value = null;
  customerFormData.value = {
    email: '',
    firstName: '',
    lastName: '',
    idType: '',
    idNumber: '',
    licenseAddress: '',
    contactAddress: '',
    phone1: '',
    phone2: ''
  };
  sameAddressAsLicense.value = true;
  deviceModelInput.value = ''; // Add this line
};

// 监听对话框打开
watch(dialogVisible, (val) => {
  if (val) {
    init();
    
    // 延迟重置表单验证状态，确保表单已经挂载
    nextTick(() => {
      if (orderFormRef.value) {
        orderFormRef.value.clearValidate();
      }
    });
  }
});

// 处理设备附件变更
const handleDeviceAttachmentChange = (file, fileList) => {
  deviceAttachments.value = fileList;
};

// 打开创建客户对话框
const openCreateCustomerDialog = (e) => {
  // 阻止事件冒泡
  if (e) {
    e.stopPropagation();
    e.preventDefault();
  }
  
  // 增加延迟，避免事件冲突
  setTimeout(() => {
    createCustomerDialogVisible.value = true;
  }, 200); // 增加延迟时间
};

// 处理客户创建成功
const handleCustomerCreated = (customer) => {
  // 添加新创建的客户到客户列表
  const newCustomer = {
    id: customer.id || `c${Date.now()}`,
    code: customer.customerNo || `C${String(Math.floor(Math.random() * 10000)).padStart(6, '0')}`,
    name: customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`,
    phone: customer.phone1 || customer.phone,
    email: customer.email || '',
    idNumber: customer.idNumber || '',
    idType: customer.idType || ''
  };
  
  // 重置搜索状态
  customerList.value = [newCustomer];
  customerFound.value = true;
  
  // 自动选中新创建的客户
  selectedCustomerId.value = newCustomer.id;
  selectedCustomer.value = newCustomer;
  
  ElMessage.success(t('customer.createSuccess'));
};

// 分别监听期数和时长，防止交叉触发验证
watch(() => orderForm.value.payment.installments, (newValue) => {
  // 该字段需要正整数，因此如果输入0或负数，则重置为空。
  // (handleIntegerInput 已确保不会有负数，所以这里主要是处理 0 的情况)
  const installments = parseInt(newValue) || 0;
  if (newValue && installments <= 0) {
    orderForm.value.payment.installments = '';
  }
});

watch(() => orderForm.value.payment.periodicLength, (newValue) => {
  // 该字段需要正整数，因此如果输入0或负数，则重置为空。
  const periodicLength = parseInt(newValue) || 0;
  if (newValue && periodicLength <= 0) {
    orderForm.value.payment.periodicLength = '';
  }
});

// 当付款计划中的服务费总额变化时，同步更新到主表单
watch(totalServiceFeeAmount, (newTotal) => {
  const newTotalNum = parseFloat(newTotal);
  if (!isNaN(newTotalNum)) {
    orderForm.value.payment.serviceFee = newTotalNum.toFixed(2);
  }
});

// 监听金额变化，同步更新计算结果
watch([
  () => orderForm.value.payment.initialPayment,
  () => orderForm.value.payment.periodicPayment,
  () => orderForm.value.payment.installments,
  () => orderForm.value.payment.periodicLength,
  () => orderForm.value.payment.periodicLengthUnit,
  () => orderForm.value.payment.serviceFee,
  () => orderForm.value.payment.depositAmount,
  () => orderForm.value.payment.penaltyValue
], () => {
  // 自动更新计算总金额（无需格式化，由失焦事件处理）
  // 当表单值变更后，立即清除所有表单错误状态
  // if (orderFormRef.value) {
  //   nextTick(() => {
  //     orderFormRef.value.clearValidate();
  //   });
  // }
});

// 金额格式化函数 - 用于失焦时的格式化
const formatCurrency = (value) => {
  if (!value) return '';
  
  // 已经是格式化后的值，直接返回
  if (/^\d+\.\d{2}$/.test(value)) return value;
  
  // 解析数字部分
  const numValue = parseFloat(value.toString().replace(/[^\d.]/g, ''));
  
  // 如果不是有效数字，返回空
  if (isNaN(numValue)) return '';
  
  // 格式化为两位小数
  return numValue.toFixed(2);
};

// 格式化百分比值
const formatPenaltyValue = () => {
  if (orderForm.value.payment.penaltyType === 'percentage') {
    // 限制百分比范围为0-100，且保留2位小数
    const raw = orderForm.value.payment.penaltyValue.replace(/[^\d.]/g, '');
    let value = parseFloat(raw) || 0;
    
    if (value > 100) {
      value = 100;
    }
    
    orderForm.value.payment.penaltyValue = value.toFixed(2);
  } else if (orderForm.value.payment.penaltyType === 'fixed') {
    // 固定金额按普通金额格式化
    orderForm.value.payment.penaltyValue = formatCurrency(orderForm.value.payment.penaltyValue);
  }
};

// 处理金额输入框输入事件
const validateAmount = (form, field, propName) => { // form will now be orderForm.value.payment
  if (form[field] === null || form[field] === undefined || String(form[field]).trim() === '') {
    form[field] = '';
    if (orderFormRef.value) {
      nextTick(() => {
        orderFormRef.value.validateField(propName);
      });
    }
    return;
  }
  
  // 数字和一个小数点
  const value = form[field].toString();
  
  // 最多允许一个小数点和两位小数
  const parts = value.split('.');
  if (parts.length > 1) {
    // 有小数点，保留两位小数
    const intPart = parts[0].replace(/\D/g, ''); // 整数部分只保留数字
    const decPart = parts[1].replace(/\D/g, '').slice(0, 2); // 小数部分最多两位
    form[field] = intPart + (decPart ? '.' + decPart : '');
  } else {
    // 没有小数点，只保留数字
    form[field] = value.replace(/\D/g, '');
  }

  // 实时校验该字段，确保错误提示立即更新
  if (orderFormRef.value) {
    nextTick(() => {
      orderFormRef.value.validateField(propName);
    });
  }
};

// 处理整数输入框输入事件
const validateInteger = (form, field, propName) => { // form will now be orderForm.value.payment
  if (form[field] === null || form[field] === undefined || String(form[field]).trim() === '') {
    form[field] = '';
    if (orderFormRef.value) {
      nextTick(() => {
        orderFormRef.value.validateField(propName);
      });
    }
    return;
  }
  
  // 移除非数字字符，只保留整数
  form[field] = String(form[field]).replace(/\D/g, '');
  
  // 去除前导零
  form[field] = form[field].replace(/^0+/, '') || '';
  
  // 实时校验该字段，确保错误提示立即更新
  if (orderFormRef.value) {
    nextTick(() => {
      orderFormRef.value.validateField(propName);
    });
  }
};

// 监听违约金类型变化
watch(() => orderForm.value.payment.penaltyAmountType, (newType) => {
  if (newType === 'PERCENTAGE_AMOUNT') {
    // 默认设置为较小值
    orderForm.value.payment.penaltyValue = '0.50';
  } else if (newType === 'FIXED_AMOUNT') {
    // 默认设置为一个小额
    orderForm.value.payment.penaltyValue = '100.00';
  }
  // 手动清除验证错误，现在可以依赖于 prop 的 trigger
  if (orderFormRef.value) {
    orderFormRef.value.clearValidate('payment.penaltyAmountType');
    orderFormRef.value.clearValidate('payment.penaltyValue');
  }
});

// 添加处理函数
// 处理违约金类型变化
const handlePenaltyTypeChange = () => {
  if (orderForm.value.payment.penaltyCalculationType && orderFormRef.value) {
    // 清除该字段的验证错误
    orderFormRef.value.clearValidate('payment.penaltyCalculationType');
  }
};

// 处理付款金额变更
const handleAmountChange = (row, index) => {
  // 验证输入是否为有效数字
  let value = row.amount.toString().replace(/[^\d.]/g, '');
  
  // 确保只有一个小数点
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // 确保最多两位小数
  if (parts.length > 1 && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].substring(0, 2);
  }
  
  // 更新行数据
  row.amount = value;
  
  // 存储修改过的行数据
  saveEditedRow(row);
  
  // 重新计算总金额
  recalculateTotal();
};

// 处理服务费变更
const handleServiceFeeChange = (row, index) => {
  // 验证输入是否为有效数字
  let value = row.serviceFee.toString().replace(/[^\d.]/g, '');
  
  // 确保只有一个小数点
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // 确保最多两位小数
  if (parts.length > 1 && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].substring(0, 2);
  }
  
  // 更新行数据
  row.serviceFee = value;
  
  // 存储修改过的行数据
  saveEditedRow(row);
  
  // 重新计算总金额
  recalculateTotal();
};

// 存储用户修改过的行数据
const saveEditedRow = (row) => {
  // 只处理分期款类型的数据
  if (row.type !== 'installment') return;
  
  // 检查是否已存在该行的修改记录
  const existingIndex = editedPaymentRows.value.findIndex(
    item => item.type === row.type && item.installmentNo === row.installmentNo
  );
  
  if (existingIndex >= 0) {
    // 更新已存在的记录
    editedPaymentRows.value[existingIndex] = { ...row };
  } else {
    // 添加新记录
    editedPaymentRows.value.push({ ...row });
  }
};

// 重新计算总金额
const recalculateTotal = () => {
  // 由于 paymentPlan 是 ref，Vue 会自动追踪其内部变化并更新依赖它的 computed 属性，
  // 例如 totalPaymentAmount 和 totalServiceFeeAmount。
  // 因此这个函数现在是多余的，可以安全地移除其调用。
};

// 获取特定行的付款金额
const getPaymentForRow = (index) => {
  // 检查该期是否已经存在于存储的自定义数据中
  const existingRow = editedPaymentRows.value.find(row => 
    row.type === 'installment' && row.installmentNo === (index + 1)
  );
  
  if (existingRow) {
    return parseFloat(existingRow.amount);
  } else {
    return parseFloat(orderForm.value.payment.periodicPayment);
  }
};

// 获取特定行的服务费金额
const getServiceFeeForRow = (index, firstInstallmentFee, averageFee) => {
  // 检查该期是否已经存在于存储的自定义数据中
  const existingRow = editedPaymentRows.value.find(row => 
    row.type === 'installment' && row.installmentNo === (index + 1)
  );
  
  // 如果用户手动编辑过，则使用编辑后的值
  if (existingRow && typeof existingRow.serviceFee !== 'undefined') {
    // 确保返回的是数字，以便进行后续计算
    return parseFloat(existingRow.serviceFee) || 0;
  } else {
    // 否则，使用基于总服务费的精确计算值
    return (index === 0) ? firstInstallmentFee : averageFee;
  }
};

// 清空设备选择
const clearDeviceSelection = () => {
  selectedDeviceId.value = null;
  orderForm.deviceId = null;
  deviceBrandInput.value = '';
  deviceSnInput.value = '';
  deviceColorInput.value = '';
  deviceDetailsInput.value = '';
  deviceImei1Input.value = '';
  deviceImei2Input.value = '';
  deviceAttachments.value = [];
  deviceFound.value = false;
};

// 获取用户信息
const userStore = useUserStore();
const creator = computed(() => userStore.userInfo.username || '管理员');

// 处理金额输入框输入事件，限制为两位小数
const handleAmountInput = (value, field) => {
  if (value === null || value === undefined || value === '') {
    orderForm.value.payment[field] = value;
    return;
  }
  let cleanValue = value.toString();
  // 1. 移除非数字和第一个点之外的所有字符
  cleanValue = cleanValue.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1');
  // 2. 匹配并截断至两位小数
  const match = cleanValue.match(/^(\d*\.?\d{0,2})/);
  if (match) {
    cleanValue = match[0];
  } else {
    cleanValue = '';
  }
  // 3. 更新模型值
  orderForm.value.payment[field] = cleanValue;
};

// 处理整数输入框输入事件
const handleIntegerInput = (value, field) => {
  if (value === null || value === undefined) {
    orderForm.value.payment[field] = '';
    return;
  }
  // 移除非数字字符
  const cleanValue = value.toString().replace(/\D/g, '');
  // 更新模型值，v-model.number 会处理类型转换
  orderForm.value.payment[field] = cleanValue;
};

// 新增：专门处理违约金输入的函数
const handlePenaltyValueInput = (value) => {
  if (value === null || value === undefined) {
    orderForm.value.payment.penaltyValue = '';
    return;
  }
  // 只保留数字
  const digits = String(value).replace(/\D/g, '');
  orderForm.value.payment.penaltyValue = digits;
};

// 处理付款金额失焦
const handleAmountBlur = (row) => {
    if (row.amount === '' || row.amount === null || isNaN(parseFloat(row.amount))) {
        row.amount = '0.00';
    } else {
        row.amount = parseFloat(row.amount).toFixed(2);
    }
};

// 处理服务费失焦
const handleServiceFeeBlur = (row) => {
    if (row.serviceFee === '' || row.serviceFee === null || isNaN(parseFloat(row.serviceFee))) {
        row.serviceFee = '0.00';
    } else {
        row.serviceFee = parseFloat(row.serviceFee).toFixed(2);
    }
};

onMounted(async () => {
  try {
    const response = await getDictFields({ module: 'CUSTOMER', fieldCode: 'id_type', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      idTypeOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch id_type options:", error);
  }

  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'color', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      colorOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch color options:", error);
  }

  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'brand', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      brandOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch brand options:", error);
  }
});
</script>

<style scoped>
/* 订单页面样式 */
.section-divider {
  border-top: 1px solid #EBEEF5;
  margin-bottom: 20px;
  margin-top: -10px;
}

.step-order-info .step-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 500;
  margin-top: 25px;
  margin-bottom: 10px;
}

.order-section, .payment-section, .fees-section {
  margin-bottom: 20px;
}

/* 订单表单左移 */
.order-form {
  /* 不需要额外的左边距，保持与上方内容对齐 */
  min-height: 700px; /* 增加表单高度，确保完整显示 */
}

.amount-input {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.currency-symbol {
  position: absolute;
  right: 10px;
  z-index: 1;
  color: #606266;
  font-size: 14px;
  line-height: 1;
}

.unit-symbol {
  position: absolute;
  right: 10px;
  z-index: 1;
  color: #606266;
  font-size: 14px;
}

.amount-input .el-input input {
  text-align: right;
  padding-right: 25px;
}

.penalty-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
  position: relative;
}

.penalty-select {
  width: 60% !important;
  flex-shrink: 0;
}

.penalty-input {
  width: 35% !important;
  margin-left: 8px;
  flex-shrink: 0;
  position: relative;
}

.penalty-input .el-input input {
  text-align: right;
  padding-right: 25px;
}

/* 确保每期长度单位显示正确 */
.length-unit-container {
  display: flex;
  align-items: center;
}

.length-unit-container :deep(.el-select) {
  width: 100%;
}

.length-unit-container :deep(.el-select__popper) {
  min-width: 120px !important;
}

/* 确保币种选择和金额输入对齐 */
.deposit-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.deposit-container .el-col {
  display: flex;
  align-items: center;
}

.deposit-container .amount-input {
  width: 100%;
}

.deposit-container .currency-select {
  width: 100%;
}

.currency-select :deep(.el-select__popper) {
  min-width: 80px !important;
}

/* 修复违约金百分比的显示问题 */
.step-order-info :deep(.el-input.is-disabled .el-input__inner) {
  color: #606266;
  background-color: #f5f7fa;
}

/* 添加错误验证显示 */
.step-order-info :deep(.el-form-item.is-error .el-input__inner) {
}

.step-order-info :deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}

/* 确保按钮样式正确 */
.step-order-info :deep(.el-button) {
  margin-left: 10px;
}

/* 确保下拉菜单位置正确并完整显示内容 */
.step-order-info :deep(.el-select-dropdown) {
  min-width: 120px !important;
}

.step-order-info :deep(.el-select-dropdown__item) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20px;
}

/* 确保输入框内容完整显示 */
.step-order-info :deep(.el-input__inner) {
  font-size: 14px;
  padding: 0 15px;
}

.step-order-info :deep(.el-select__input) {
  margin-left: 0;
}

.create-order-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
}

.dialog-content {
  display: flex;
  gap: 30px;
  min-height: 450px;
  padding-bottom: 70px;
}

.steps-container {
  width: 200px;
  padding-right: 10px;
  border-right: 1px solid var(--el-border-color-lighter);
}

.steps-container :deep(.el-steps) {
  height: 100%;
}

.steps-container :deep(.el-step__title) {
  font-size: 14px;
}

.steps-container :deep(.el-step__line) {
  left: 9px;
}

.form-container {
  flex: 1;
  min-width: 0;
  padding: 0 10px;
}

.step-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.device-select-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
}

.search-input {
  flex: 1;
}

.create-device-option {
  display: flex;
  justify-content: flex-start;
  padding: 5px 20px 10px;
  margin-top: 0;
}

.create-device-btn {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}

.el-icon--left {
  margin-right: 4px;
  font-size: 16px;
  color: var(--el-color-primary);
}

.create-device-btn.el-button--text {
  color: var(--el-color-primary);
  font-size: 14px;
  height: auto;
  line-height: 1;
}

.not-found-device {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px 20px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: left;
}

.searching-device {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px 20px;
  color: #409EFF;
  font-size: 14px;
  text-align: left;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.device-details-section {
  margin-bottom: 20px;
}

.grid-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.grid-col {
  flex: 1;
}

.full-width {
  flex-basis: 100%;
}

.field-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.step-device, 
.step-customer, 
.step-order-info, 
.step-payment, 
.step-preview {
  height: 100%;
}

.preview-section {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 10px;
}

.preview-section:last-child {
  border-bottom: none;
}

.preview-section h4 {
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 15px;
  color: var(--el-text-color-primary);
}

.preview-content {
  padding-left: 20px;
}

.preview-content p {
  margin: 5px 0;
  color: var(--el-text-color-regular);
}

.preview-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.preview-content li {
  margin-bottom: 5px;
  color: var(--el-text-color-regular);
}

.payment-summary {
  margin-bottom: 25px;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.payment-summary h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 15px;
  color: #303133;
}

.payment-info p {
  margin: 5px 0;
  font-size: 14px;
}

/* 确保付款计划表单正确显示 */
.step-payment :deep(.el-input-number) {
  width: 180px;
}

.step-payment :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

.step-payment :deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.step-payment :deep(.el-select) {
  width: 180px;
}

/* 确保表单内容完整显示 */
.step-payment :deep(.el-select-dropdown__item) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20px;
}

.device-table-empty {
  text-align: center;
  padding: 20px 0;
  color: var(--el-text-color-secondary);
}

.step-payment {
  min-height: 500px; /* 确保内容显示完整 */
}

.step-payment .step-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 500;
  margin-top: 25px;
  margin-bottom: 10px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 30px;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #fff;
  width: 100%;
  border-top: 1px solid var(--el-border-color-lighter);
  z-index: 1;
}

/* 确保下拉菜单项左对齐并有hover效果 */
:deep(.el-select-dropdown__item) {
  text-align: left !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
}

:deep(.el-select-dropdown__item:hover) {
  background-color: var(--el-dropdown-menuItem-hover-fill) !important;
  color: var(--el-dropdown-menuItem-hover-color) !important;
}

/* 设备下拉菜单样式 */
:deep(.device-select-dropdown .el-select-dropdown__item) {
  text-align: left !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
}

:deep(.device-select-dropdown .el-select-dropdown__item.selected) {
  color: var(--el-color-primary) !important;
  font-weight: bold !important;
}

:deep(.device-select-dropdown .searching-device),
:deep(.device-select-dropdown .not-found-device) {
  text-align: left !important;
  padding: 10px 20px !important;
  color: var(--el-text-color-secondary) !important;
}

/* 客户下拉菜单样式 */
:deep(.customer-select-dropdown .el-select-dropdown__item) {
  text-align: left !important;
  padding: 0 20px !important;
  margin: 0 !important;
  height: 34px !important;
  line-height: 34px !important;
}

:deep(.customer-select-dropdown .searching-customer),
:deep(.customer-select-dropdown .not-found-customer) {
  text-align: left !important;
  padding: 10px 20px !important;
  color: var(--el-text-color-secondary) !important;
}

:deep(.customer-select-dropdown .create-customer-option),
:deep(.device-select-dropdown .create-device-option) {
  text-align: left !important;
  padding: 8px 20px !important;
  border-top: 1px solid var(--el-border-color-lighter) !important;
  margin-top: 5px !important;
  cursor: pointer !important;
}

:deep(.customer-select-dropdown .create-customer-option:hover),
:deep(.device-select-dropdown .create-device-option:hover) {
  background-color: var(--el-dropdown-menuItem-hover-fill) !important;
  color: var(--el-dropdown-menuItem-hover-color) !important;
}

:deep(.create-customer-text),
:deep(.create-device-text) {
  display: flex !important;
  align-items: center !important;
  color: var(--el-color-primary) !important;
}

:deep(.create-customer-text .el-icon),
:deep(.create-device-text .el-icon) {
  margin-right: 5px !important;
}

/* 调整对话框内容区域的高度，为底部按钮留出空间 */
.dialog-content {
  display: flex;
  gap: 30px;
  min-height: 450px;
  padding-bottom: 70px;
}

.create-customer-option {
  display: flex;
  justify-content: flex-start;
  padding: 5px 20px 10px;
  margin-top: 0;
}

.searching-customer,
.not-found-customer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px 20px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: left;
}

.payment-plan-table {
  margin-top: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.payment-plan-summary {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.summary-total {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-label {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  font-size: 16px;
}

.summary-values {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.amount-label,
.fee-label {
  color: #606266;
  font-size: 14px;
  min-width: 100px;
  text-align: right;
}

.amount-value,
.fee-value {
  font-weight: bold;
  color: #303133;
  min-width: 120px;
  text-align: right;
  font-size: 18px;
}

.amount-value {
  color: #409EFF;
}

.fee-value {
  color: #67c23a;
}

/* 客户表单样式 */
.customer-form {
  text-align: left;
  margin-left: -20px; /* 确保表单左对齐，无意外左外边距 */
}

.customer-form :deep(.el-form-item__label) {
  text-align: left;
}

.customer-form :deep(.el-form-item__content) {
  text-align: left;
  display: flex;
  justify-content: flex-start;
}

.customer-form :deep(.el-input__inner) {
  text-align: left;
}

.customer-form :deep(.el-select) {
  width: 100%;
  text-align: left;
}

.customer-form :deep(.el-select__input) {
  margin-left: 0;
}

/* 添加违约计算类型的样式 */
.penalty-form-item :deep(.el-select .el-input__inner) {
  text-align: left;
}

.penalty-form-item :deep(.el-form-item__error) {
  display: none; /* 已选择时隐藏错误提示 */
}

.penalty-form-item.is-required :deep(.el-form-item__error) {
  display: block; /* 未选择时显示错误提示 */
}

.penalty-form-item :deep(.el-select__placeholder) {
  text-align: left;
  padding-left: 5px;
}

/* 确保违约金计算选项左对齐 */
.penalty-container :deep(.el-select-dropdown__item) {
  text-align: left !important;
  padding-left: 20px !important;
}

/* 请选择违约金类型提示左对齐 */
.penalty-container :deep(.el-select__placeholder) {
  left: 10px;
  text-align: left;
}

/* 添加样式规则，处理表单验证错误的显示 */
.order-form :deep(.el-form-item.is-error .el-input__inner) {
  border-color: #f56c6c;
}

/* 自定义错误提示样式 */
.order-form :deep(.el-form-item.is-error) {
  margin-bottom: 22px;
}

.order-form :deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 0;
  padding-top: 2px;
  font-size: 12px;
  color: #f56c6c;
}

/* 让押金部分与其他输入框对齐 */
.step-order-info .payment-section .el-col,
.step-order-info .fees-section .el-col {
  padding-left: 0;
}

.step-order-info .payment-section .el-form-item,
.step-order-info .fees-section .el-form-item {
  margin-left: 0;
}

/* 统一表单布局样式 */
.step-order-info .el-form {
  width: 100%;
}

.step-order-info .el-row {
  width: 100%;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.step-order-info .el-col {
  padding-left: 0 !important;
  padding-right: 10px !important;
}

.step-order-info .el-form-item {
  margin-bottom: 18px;
}

.step-order-info .el-form-item__label {
  padding-right: 12px;
}

.step-order-info .el-form-item__content {
  margin-left: 0 !important;
}

/* 确保输入框和下拉框宽度一致 */
.step-order-info .el-input,
.step-order-info .el-select {
  width: 100%;
}

/* 处理特殊的下拉菜单容器 */
.length-unit-container,
.deposit-container,
.penalty-container {
  width: 100%;
  margin: 0;
}

/* 让所有表单输入左对齐 */
.step-order-info :deep(.el-input__inner),
.step-order-info :deep(.el-select__input) {
  text-align: left;
}

/* 添加押金相关样式 */
.deposit-container-flex {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 200px; /* 设置为200px与服务费输入框宽度一致 */
  position: relative; /* 添加相对定位，以便内部货币符号的绝对定位 */
}

.deposit-amount-input {
  width: 100%; /* 让输入框填充父容器 */
  position: relative;
}

.currency-symbol-deposit {
  position: absolute;
  right: 10px; /* 相对于父容器右侧定位 */
  z-index: 1;
  color: #606266;
  font-size: 14px;
}

.deposit-currency-select {
  width: 100px;
}

/* 使用Element Plus默认的表单验证样式 */
.step-order-info :deep(.el-form-item.is-error) {
  margin-bottom: 18px;
}

/* 添加违约金计算选项样式 */
/* 使用Element Plus默认样式 */

/* 确保下拉菜单正确定位和显示 */
.penalty-unit-select :deep(.el-input__inner) {
  text-align: center !important;
  padding: 0 8px !important;
}

.penalty-unit-select :deep(.el-select-dropdown__item) {
  padding: 0 8px !important;
  text-align: center !important;
}

.unit-symbol-penalty {
  position: relative;
  margin-left: -25px;
  z-index: 1;
  color: #606266;
  font-size: 14px;
}

/* 确保验证状态不会显示错误 */
.fees-section :deep(.el-form-item.is-error) {
  margin-bottom: 18px !important;
}

/* 服务费样式 */
.service-fee-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 200px;
}

.service-fee-input {
  width: 100%;
}

.currency-symbol-service-fee {
  position: absolute;
  right: 10px;
  z-index: 1;
  color: #606266;
  font-size: 14px;
}

/* 确保所有输入框的样式一致 */
.step-order-info :deep(.el-input__inner) {
  text-align: left;
  padding-left: 10px;
}

/* 修改padding和margin，确保布局整齐 */
.fees-section .el-form-item,
.payment-section .el-form-item {
  margin-bottom: 20px;
}

.fees-section .el-col,
.payment-section .el-col {
  padding-right: 20px;
}

/* 备注区域样式 */
.remarks-form-item {
  width: 100%;
}

.remarks-input {
  width: 100%;
}

/* 确保所有下拉选项左对齐 */
.step-order-info :deep(.el-select-dropdown__item) {
  text-align: left !important;
  padding-left: 15px !important;
}

/* 付款计划表格相关样式 */
.payment-info-alert {
  margin-bottom: 20px;
}

.payment-plan-table {
  margin-top: 20px;
}

.payment-plan-table :deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light);
  --el-table-header-bg-color: #f5f7fa;
}

.payment-plan-table :deep(.el-table__header) th {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 600;
  text-align: center;
}

.payment-plan-table :deep(.el-input__inner) {
  text-align: right;
  padding-right: 8px;
}

.payment-plan-summary {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-total {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-label {
  font-weight: bold;
  color: #606266;
  margin-bottom: 5px;
}

.summary-values {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.amount-label,
.fee-label {
  color: #606266;
}

.amount-value,
.fee-value {
  font-weight: bold;
  color: #303133;
  min-width: 100px;
  text-align: right;
}

/* 确保表格内容左右对齐正确 */
.payment-plan-table :deep(.el-table .cell) {
  padding-left: 10px;
  padding-right: 10px;
}

.payment-plan-table :deep(.el-table--border th) {
  border-right: 1px solid var(--el-table-border-color);
}

.payment-plan-table :deep(.el-table--border td) {
  border-right: 1px solid var(--el-table-border-color);
}

/* 表格输入框样式 */
.amount-input-cell, .fee-input-cell {
  width: 100%;
}

.amount-input-cell :deep(.el-input__inner), 
.fee-input-cell :deep(.el-input__inner) {
  text-align: right;
  padding-right: 8px;
  color: #409EFF;
  font-weight: 500;
}

.payment-plan-table :deep(.el-input__inner) {
  text-align: right;
  padding-right: 8px;
}

.preview-card {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #EBEEF5;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: none;
}

.preview-card-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.preview-card-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.mt-4 {
  margin-top: 16px;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  color: #409EFF;
}

.preview-card-body {
  padding: 12px;
}

.preview-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.preview-grid-item {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 3px 5px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.preview-grid-item.full-width {
  flex: 0 0 100%;
  max-width: 100%;
}

.preview-grid-item > div {
  display: flex;
  align-items: baseline;
  line-height: 1.5;
}

.preview-label {
  min-width: 100px;
  color: #606266;
  font-size: 13px;
}

.preview-value {
  flex: 1;
  color: #303133;
  font-size: 13px;
  word-break: break-word;
}

.preview-remarks {
  border-top: 1px dashed #EBEEF5;
  margin-top: 12px;
  padding-top: 12px;
}

.preview-remarks .preview-label {
  display: block;
  margin-bottom: 4px;
}

.preview-remarks .preview-value {
  padding-left: 8px;
  display: block;
  line-height: 1.5;
}

/* 付款计划表格样式 */
.payment-plan-table {
  width: 100%;
}

.payment-plan-table :deep(.el-table) {
  margin-bottom: 15px;
  border-radius: 2px;
  overflow: hidden;
}

.payment-plan-table :deep(.el-table th) {
  background-color: #F5F7FA;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
  text-align: center;
}

.payment-plan-table :deep(.el-table td) {
  padding: 8px 0;
}

/* 付款计划表格底部总计样式 */
.payment-plan-summary {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 2px;
  border: 1px solid #EBEEF5;
}

.summary-content {
  display: flex;
  align-items: flex-start;
}

.summary-label {
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  margin-right: 8px;
}

.summary-values {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.summary-detail {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  align-items: center;
}

.amount-label,
.fee-label {
  color: #606266;
  font-size: 13px;
}

.amount-value,
.fee-value {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  min-width: 90px;
  text-align: right;
}

/* 响应式样式 */
@media screen and (max-width: 1100px) {
  .preview-grid-item {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .summary-content {
    flex-direction: column;
  }
}

/* 以下是其他样式 */
.amount-value {
  color: #F56C6C;
}

.fee-value {
  color: #E6A23C;
}

/* 确保元素正确显示 */
.step-preview :deep(.el-tag) {
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

.step-preview :deep(.el-table__row) {
  height: 40px;
}

/* 预览确认页整体样式 */
.step-preview {
  padding: 10px;
  padding-left: 0;
  overflow-y: auto;
  max-height: calc(100vh - 250px);
}

.preview-content {
  padding-left: 0;
}

.step-preview .step-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
}

/* 预览卡片样式 */
.preview-card {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #EBEEF5;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: none;
}

.preview-card-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.preview-card-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  color: #409EFF;
}

.preview-card-body {
  padding: 12px;
}

.preview-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.preview-grid-item {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 3px 5px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.preview-grid-item.full-width {
  flex: 0 0 100%;
  max-width: 100%;
}

.preview-grid-item > div {
  display: flex;
  align-items: baseline;
  line-height: 1.5;
}

.preview-label {
  min-width: 100px;
  color: #606266;
  font-size: 13px;
}

.preview-value {
  flex: 1;
  color: #303133;
  font-size: 13px;
  word-break: break-word;
}

.preview-remarks {
  border-top: 1px dashed #EBEEF5;
  margin-top: 12px;
  padding-top: 12px;
}

.preview-remarks .preview-label {
  display: block;
  margin-bottom: 4px;
}

.preview-remarks .preview-value {
  padding-left: 8px;
  display: block;
  line-height: 1.5;
}

/* 付款计划表格样式 */
.payment-plan-table {
  width: 100%;
}

.payment-plan-table :deep(.el-table) {
  margin-bottom: 15px;
  border-radius: 2px;
  overflow: hidden;
}

.payment-plan-table :deep(.el-table th) {
  background-color: #F5F7FA;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
  text-align: center;
}

.payment-plan-table :deep(.el-table td) {
  padding: 8px 0;
}

/* 付款计划表格底部总计样式 */
.payment-plan-summary {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 2px;
  border: 1px solid #EBEEF5;
}

.summary-content {
  display: flex;
  align-items: flex-start;
}

.summary-label {
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  margin-right: 8px;
}

.summary-values {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.summary-detail {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  align-items: center;
}

.amount-label,
.fee-label {
  color: #606266;
  font-size: 13px;
}

.amount-value,
.fee-value {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  min-width: 90px;
  text-align: right;
}

/* 确保表格内容左右对齐正确 */
.payment-plan-table :deep(.el-table .cell) {
  padding-left: 10px;
  padding-right: 10px;
}

.payment-plan-table :deep(.el-table--border th) {
  border-right: 1px solid var(--el-table-border-color);
}

.payment-plan-table :deep(.el-table--border td) {
  border-right: 1px solid var(--el-table-border-color);
}

/* 表格输入框样式 */
.amount-input-cell, .fee-input-cell {
  width: 100%;
}

.amount-input-cell :deep(.el-input__inner), 
.fee-input-cell :deep(.el-input__inner) {
  text-align: right;
  padding-right: 8px;
  color: #409EFF;
  font-weight: 500;
}

.payment-plan-table :deep(.el-input__inner) {
  text-align: right;
  padding-right: 8px;
}

.preview-card {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #EBEEF5;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: none;
}

.preview-card-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.preview-card-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.mt-4 {
  margin-top: 16px;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  color: #409EFF;
}

.preview-card-body {
  padding: 12px;
}

.preview-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.preview-grid-item {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 3px 5px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.preview-grid-item.full-width {
  flex: 0 0 100%;
  max-width: 100%;
}

.preview-grid-item > div {
  display: flex;
  align-items: baseline;
  line-height: 1.5;
}

.preview-label {
  min-width: 100px;
  color: #606266;
  font-size: 13px;
}

.preview-value {
  flex: 1;
  color: #303133;
  font-size: 13px;
  word-break: break-word;
}

.preview-remarks {
  border-top: 1px dashed #EBEEF5;
  margin-top: 12px;
  padding-top: 12px;
}

.preview-remarks .preview-label {
  display: block;
  margin-bottom: 4px;
}

.preview-remarks .preview-value {
  padding-left: 8px;
  display: block;
  line-height: 1.5;
}

/* 隐藏el-input type="number"的浏览器默认增减按钮 */
.step-order-info :deep(input[type="number"]::-webkit-outer-spin-button),
.step-order-info :deep(input[type="number"]::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

.step-order-info :deep(input[type="number"]) {
  -moz-appearance: textfield;
}

.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 
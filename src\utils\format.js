/**
 * 日期时间格式化
 * @param {string|number|Date} time 时间
 * @param {string} format 格式化模式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的字符串
 */
export function formatDateTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return '-'
  
  const date = new Date(time)
  
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const seconds = date.getSeconds()
  
  const formatMap = {
    'YYYY': year.toString(),
    'MM': month.toString().padStart(2, '0'),
    'DD': day.toString().padStart(2, '0'),
    'HH': hours.toString().padStart(2, '0'),
    'mm': minutes.toString().padStart(2, '0'),
    'ss': seconds.toString().padStart(2, '0')
  }
  
  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match])
}

/**
 * 日期格式化
 * @param {string|number|Date} time 时间
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(time) {
  return formatDateTime(time, 'YYYY-MM-DD')
}

/**
 * 格式化电话号码
 * @param {string} phone 电话号码
 * @returns {string} 格式化后的电话号码
 */
export function formatPhone(phone) {
  if (!phone) return '-'
  
  // 如果是标准的11位手机号码，格式化为 XXX-XXXX-XXXX
  if (/^1\d{10}$/.test(phone)) {
    return phone.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1-$2-$3')
  }
  
  return phone
}

/**
 * 货币格式化
 * @param {string} currency 货币代码
 * @returns {string} 格式化后的货币名称
 */
export function formatCurrency(currency) {
  if (!currency) return '-'
  
  const currencyMap = {
    'CNY': '人民币 (CNY)',
    'USD': '美元 (USD)',
    'EUR': '欧元 (EUR)',
    'GBP': '英镑 (GBP)',
    'JPY': '日元 (JPY)'
  }
  
  return currencyMap[currency] || currency
}

/**
 * 文件大小格式化
 * @param {number} size 文件大小，单位字节
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (!size || size < 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  let index = 0
  
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  
  return `${size.toFixed(2)} ${units[index]}`
} 
<script setup>
import { useI18n } from "vue-i18n";
import { useGlobal, isNumber } from "@pureadmin/utils";
import BackTopIcon from "@/assets/svg/back_top.svg?component";
import { h, computed, Transition, defineComponent } from "vue";
import { usePermissionStore } from "@/stores/modules/permission";

const props = defineProps({
  fixedHeader: Boolean
});

const { t } = useI18n();
const permissionStore = usePermissionStore();
const { $storage, $config } = useGlobal();

const isKeepAlive = computed(() => {
  return $config?.KeepAlive;
});

const transitions = computed(() => {
  return route => {
    return route.meta.transition;
  };
});

const hideTabs = computed(() => {
  return $storage?.configure.hideTabs;
});

const hideFooter = computed(() => {
  return $storage?.configure.hideFooter;
});

const stretch = computed(() => {
  return $storage?.configure.stretch;
});

const getMainWidth = computed(() => {
  return isNumber(stretch.value)
    ? stretch.value + "px"
    : stretch.value
      ? "1440px"
      : "100%";
});

const getSectionStyle = computed(() => {
  return [
    hideTabs.value ? "padding-top: 48px;" : "padding-top: 81px;",
    props.fixedHeader
      ? ""
      : `padding-top: 0;${
          hideTabs.value
            ? "min-height: calc(100vh - 48px);"
            : "min-height: calc(100vh - 86px);"
        }`
  ];
});

const transitionMain = defineComponent({
  props: {
    route: {
      type: Object,
      required: true
    }
  },
  render() {
    const transitionName =
      transitions.value(this.route)?.name || "fade-transform";
    const enterTransition = transitions.value(this.route)?.enterTransition;
    const leaveTransition = transitions.value(this.route)?.leaveTransition;
    return h(
      Transition,
      {
        name: enterTransition ? "pure-classes-transition" : transitionName,
        enterActiveClass: enterTransition
          ? `animate__animated ${enterTransition}`
          : undefined,
        leaveActiveClass: leaveTransition
          ? `animate__animated ${leaveTransition}`
          : undefined,
        mode: "out-in",
        appear: true
      },
      {
        default: () => [this.$slots.default()]
      }
    );
  }
});
</script>

<template>
  <section
    :class="[fixedHeader ? 'app-main' : 'app-main-nofixed-header']"
    :style="getSectionStyle"
  >
    <router-view v-slot="{ Component, route }">
      <el-scrollbar
        v-if="fixedHeader"
        :wrap-style="{
          display: 'flex',
          'flex-wrap': 'wrap',
          'max-width': getMainWidth,
          margin: '0 auto',
          transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
        }"
        :view-style="{
          display: 'flex',
          flex: 'auto',
          overflow: 'hidden',
          'flex-direction': 'column'
        }"
      >
        <el-backtop
          :title="t('buttons.pureBackTop')"
          target=".app-main .el-scrollbar__wrap"
        >
          <BackTopIcon />
        </el-backtop>
        <div class="grow">
          <transitionMain :route="route">
            <keep-alive
              v-if="isKeepAlive"
              :include="permissionStore.cachePageList"
            >
              <component
                :is="Component"
                :key="route.fullPath"
                class="main-content"
              />
            </keep-alive>
            <component
              :is="Component"
              v-else
              :key="route.fullPath"
              class="main-content"
            />
          </transitionMain>
        </div>
      </el-scrollbar>
      <div v-else class="grow">
        <transitionMain :route="route">
          <keep-alive
            v-if="isKeepAlive"
            :include="permissionStore.cachePageList"
          >
            <component
              :is="Component"
              :key="route.fullPath"
              class="main-content"
            />
          </keep-alive>
          <component
            :is="Component"
            v-else
            :key="route.fullPath"
            class="main-content"
          />
        </transitionMain>
      </div>
    </router-view>
  </section>
</template>

<style scoped>
.app-main {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
}

.app-main-nofixed-header {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.main-content {
  margin: 24px;
}
</style>
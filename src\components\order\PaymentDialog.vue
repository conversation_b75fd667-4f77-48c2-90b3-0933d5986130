<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${$t('order.payment.title')} - ${orderId}`"
    width="45%"
    destroy-on-close
    class="payment-dialog"
  >
    <div class="payment-dialog-content">
      <div v-if="loading" class="loading-container">
        <el-progress 
          type="circle" 
          :width="24" 
          :stroke-width="3" 
          :percentage="100" 
          status="success" 
          :indeterminate="true"
          :duration="1"
          class="custom-loader"
        />
        <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
      </div>
      
      <template v-else>
        <!-- 未支付交易列表 -->
        <div class="transactions-container">
          <div class="section-header">
            {{ $t('order.payment.unpaidTransactions') }}
          </div>
          
          <div v-if="unpaidTransactions.length === 0" class="empty-table-tip">
            {{ $t('order.payment.noUnpaidTransactions') }}
          </div>
          
          <el-table
            v-else
            ref="transactionTable"
            :data="unpaidTransactions"
            @selection-change="handleSelectionChange"
            stripe
            style="width: 100%"
            max-height="300px"
            :empty-text="$t('common.noData')"
            :border="false"
            :fit="true"
          >
            <el-table-column type="selection" width="45" align="left" />
            
            <el-table-column prop="installmentNo" :label="$t('paymentHistory.table.installmentNo')" min-width="80" align="left">
              <template #default="{ row }">
                {{ row.installmentNo || '-' }}
              </template>
            </el-table-column>
            
            <el-table-column :label="$t('paymentHistory.table.type')" min-width="100" align="left">
              <template #default="{ row }">
                <el-tag :type="getPaymentTypeColor(row.type)" size="small" effect="light">
                  {{ formatPaymentType(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column :label="$t('paymentHistory.table.status')" min-width="80" align="left">
              <template #default="{ row }">
                <el-tag :type="getPaymentStatusColor(row.status)" size="small" effect="light">
                  {{ formatPaymentStatus(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column :label="$t('paymentHistory.table.payableAmount')" min-width="110" align="left">
              <template #default="{ row }">
                ¥{{ formatAmount(row.payableAmount + row.overdueAmount) }}
              </template>
            </el-table-column>
            
            <el-table-column :label="$t('order.payment.overdueAmount')" min-width="110" align="left">
              <template #default="{ row }">
                ¥{{ formatAmount(row.overdueAmount || 0) }}
              </template>
            </el-table-column>
            
            <el-table-column 
              :label="$t('paymentHistory.table.dueDate')" 
              min-width="110"
              align="left"
            >
              <template #default="{ row }">
                {{ formatDate(row.dueDate) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 付款信息 -->
        <div class="payment-info">
          <div class="payment-summary">
            <div class="summary-item">
              <span class="label">{{ $t('order.payment.totalPayable') }}</span>
              <span class="value">¥{{ formatAmount(calculatedTotal) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">{{ $t('order.payment.totalPaid') }}</span>
              <span class="value">¥{{ formatAmount(form.paidAmount) }}</span>
            </div>
          </div>
          
          <!-- 付款表单 -->
          <div class="payment-form">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">  
              <el-form-item :label="$t('order.payment.method')" prop="paymentMethod" required>
                <el-select v-model="form.paymentMethod" class="payment-method-select" :placeholder="$t('order.payment.selectMethod')">
                  <el-option 
                    v-for="method in availablePaymentMethods" 
                    :key="method" 
                    :label="getPaymentMethodLabel(method)" 
                    :value="method" 
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item :label="$t('order.payment.remarks')" prop="remarks">
                <el-input
                  v-model="form.remarks"
                  type="textarea"
                  :rows="3"
                  :maxlength="256"
                  :placeholder="$t('order.payment.remarksPlaceholder')"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('order.payment.cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirmPayment"
          :loading="submitting"
        >
          {{ $t('order.payment.confirmPayment') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getOrderPayments, addOrderPayment } from '@/api/order';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number], // 支持字符串和数字类型
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'success']);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 表单引用
const formRef = ref(null);
const transactionTable = ref(null);

// 加载状态
const loading = ref(false);
const submitting = ref(false);

// 交易数据
const transactions = ref([]);
const selectedTransactions = ref([]);

// 可用支付方式
const availablePaymentMethods = ref([]);

// 仅显示未付款的交易
const unpaidTransactions = computed(() => {
  return transactions.value.filter(transaction => 
    getStatusLower(transaction.status) === 'pending' || getStatusLower(transaction.status) === 'overdue'
  );
});

// 付款表单
const form = ref({
  paymentMethod: 'ONLINE', // 默认选择线上支付
  paidAmount: 0,
  remarks: ''
});

// 表单验证规则
const rules = {
  paymentMethod: [
    { required: true, message: t('order.payment.selectMethodRequired'), trigger: 'change' }
  ],
  remarks: [
    { max: 256, message: t('order.payment.remarksMaxLength'), trigger: 'blur' }
  ]
};

// 计算总金额
const calculatedTotal = computed(() => {
  if (selectedTransactions.value.length === 0) return 0;
  
  return selectedTransactions.value.reduce((sum, transaction) => {
    return sum + (transaction.payableAmount || 0) + (transaction.overdueAmount || 0);
  }, 0);
});

// 监听orderId变化，获取交易数据
watch(() => props.orderId, (newVal) => {
  if (newVal && dialogVisible.value) {
    fetchPayments();
  }
});

// 监听对话框显示状态
watch(() => dialogVisible.value, (val) => {
  if (val && props.orderId) {
    fetchPayments();
  }
});

// 监听选中交易变化，更新付款金额
watch(() => selectedTransactions.value, (newVal) => {
  if (newVal.length > 0) {
    form.value.paidAmount = calculatedTotal.value;
  } else {
    form.value.paidAmount = 0;
  }
});

// 获取订单交易记录
const fetchPayments = async () => {
  if (!props.orderId) return;
  
  loading.value = true;
  
  try {
    // 确保订单ID格式正确
    const safeOrderId = typeof props.orderId === 'object' ? props.orderId.toString() : props.orderId;
    
    // 调用API获取交易记录
    const res = await getOrderPayments(safeOrderId);
    
    // 调试输出
    console.log('API Response:', res);
    
    if (res && res.code === 1000) {
      // 处理API返回的数据结构
      // 获取交易数据，支持多种可能的字段名
      const paymentData = extractPaymentData(res.data);
      if (paymentData.length > 0) {
        transactions.value = paymentData.map(item => ({
          id: extractValue([item.transactionId, item.id, item.paymentId, item.paymentPlanId]),
          orderId: extractValue([item.orderNo, item.orderNumber, item.orderId, safeOrderId]),
          installmentNo: extractValue([item.installmentNumber, item.installmentNo, item.installmentIndex, item.periodNo]),
          type: extractValue([item.transactionType, item.paymentType, item.type], 'installment'),
          payableAmount: extractValue([item.payableAmount, item.amount, item.principal, item.installmentAmount], 0) + extractValue([item.serviceFee, item.serviceCharge, item.fee], 0),
          overdueAmount: extractValue([item.overdueAmount, item.penaltyAmount, item.penaltyIncurred, item.overdueFee, item.overduePenalty], 0),
          serviceFee: extractValue([item.serviceFee, item.serviceCharge, item.fee], 0),
          status: extractValue([item.status, item.paymentStatus], item.isPaid ? 'PAID' : 'PENDING'),
          dueDate: extractValue([item.dueDate, item.paymentDueDate, item.expectedPaymentDate]),
          paidTime: extractValue([item.paymentDate, item.paymentTime, item.paidTime, item.paidDate, item.actualPaymentDate]),
          remainingAmount: extractValue([item.remainingAmount, item.balanceAmount, item.balance], 0),
          canPay: item.canPay !== false
        }));
      } else {
        transactions.value = [];
      }
      
      console.log('Transformed transactions:', transactions.value);
      
      // 获取可用的支付方式
      let availableMethods = extractValue([
        res.data?.availablePaymentMethods,
        res.data?.paymentMethods,
        res.data?.supportedPaymentMethods
      ], []);
      
      if (!availableMethods || availableMethods.length === 0) {
        // 如果API没有返回可用方式，则使用默认方式
        availableMethods = ['ONLINE', 'OFFLINE_TRANSFER', 'BANK_TRANSFER', 'CASH'];
      }
      availablePaymentMethods.value = availableMethods;
      
      // 设置默认支付方式
      let defaultPaymentMethod = 'ONLINE'; // 默认设置为线上支付
      if (availableMethods.length > 0) {
        if (availableMethods.includes('ONLINE')) {
          defaultPaymentMethod = 'ONLINE';
        } else {
          // 如果线上支付不可用，则选择列表中的第一个
          defaultPaymentMethod = availableMethods[0];
        }
      }

      // 重置选择和表单
      selectedTransactions.value = [];
      form.value = {
        paymentMethod: defaultPaymentMethod,
        paidAmount: 0,
        remarks: ''
      };
      
      // 默认选中所有逾期的记录
      nextTick(() => {
        const overdueTransactions = transactions.value.filter(transaction => 
          getStatusLower(transaction.status) === 'overdue'
        );
        
        if (overdueTransactions.length > 0 && transactionTable.value) {
          overdueTransactions.forEach(transaction => {
            transactionTable.value.toggleRowSelection(transaction, true);
          });
        }
      });
    } else {
      const errorMsg = res?.message || t('order.payment.fetchFailed');
      ElMessage.error(errorMsg);
      
      // 如果是特定错误码，可以提供更具体的错误信息
      if (res && (res.code === 4004 || res.code === 5001)) {
        console.warn(`API错误：${res.code} - ${errorMsg}`);
      }
    }
  } catch (error) {
    console.error('获取交易记录失败:', error);
    ElMessage.error(t('order.payment.fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 从多个可能的来源提取支付数据
const extractPaymentData = (data) => {
  if (!data) return [];
  
  // 按优先级尝试不同来源的数据
  if (data.paymentInfo && data.paymentInfo.transactions && data.paymentInfo.transactions.length > 0) {
    console.log('使用paymentInfo.transactions字段数据');
    return data.paymentInfo.transactions;
  }
  
  if (data.transactions && Array.isArray(data.transactions) && data.transactions.length > 0) {
    console.log('使用顶层transactions字段数据');
    return data.transactions;
  }
  
  if (data.paymentInfo && data.paymentInfo.paymentPlans && data.paymentInfo.paymentPlans.length > 0) {
    console.log('使用paymentInfo.paymentPlans字段数据');
    return data.paymentInfo.paymentPlans;
  }
  
  if (data.paymentPlans && Array.isArray(data.paymentPlans) && data.paymentPlans.length > 0) {
    console.log('使用顶层paymentPlans字段数据');
    return data.paymentPlans;
  }
  
  if (data.paymentSchedules && Array.isArray(data.paymentSchedules) && data.paymentSchedules.length > 0) {
    console.log('使用paymentSchedules字段数据');
    return data.paymentSchedules;
  }
  
  if (data.paymentList && Array.isArray(data.paymentList) && data.paymentList.length > 0) {
    console.log('使用paymentList字段数据');
    return data.paymentList;
  }
  
  // 如果所有尝试都失败，返回空数组
  return [];
};

// 从多个可能的字段中提取值
const extractValue = (possibleValues, defaultValue = null) => {
  if (!Array.isArray(possibleValues)) return defaultValue;
  
  for (const value of possibleValues) {
    if (value !== undefined && value !== null) {
      return value;
    }
  }
  return defaultValue;
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedTransactions.value = selection;
};

// 安全获取状态的小写形式
const getStatusLower = (status) => {
  if (!status) return '';
  
  // 处理中文状态值
  if (status === '待支付') return 'pending';
  if (status === '已支付') return 'paid';
  if (status === '已逾期') return 'overdue';
  if (status === '逾期支付') return 'late_paid';
  
  return typeof status === 'string' ? status.toLowerCase() : '';
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 提交付款
const handleConfirmPayment = async () => {
  if (selectedTransactions.value.length === 0) {
    ElMessage.warning(t('order.payment.selectAtLeastOne'));
    return;
  }
  
  if (!form.value.paymentMethod) {
    ElMessage.warning(t('order.payment.selectMethodRequired'));
    return;
  }
  
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 构建付款请求数据
    const paymentData = {
      orderNo: props.orderId,
      paymentIds: selectedTransactions.value.map(item => item.id),
      paymentMethod: form.value.paymentMethod,
      paidAmount: form.value.paidAmount,
      remarks: form.value.remarks || ''
    };
    
    // 调用API提交付款
    const res = await addOrderPayment(props.orderId, paymentData);
    
    if (res.code === 1000) {
      ElMessage.success(t('order.payment.successMessage'));
      dialogVisible.value = false;
      emit('success');
    } else {
      ElMessage.error(res.message || t('order.payment.failedMessage'));
    }
  } catch (error) {
    console.error('提交付款失败:', error);
    ElMessage.error(t('order.payment.systemError'));
  } finally {
    submitting.value = false;
  }
};

// 格式化金额
const formatAmount = (amount) => {
  if (amount === undefined || amount === null) return '0.00';
  return Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit' 
  });
};

// 获取付款类型颜色
const getPaymentTypeColor = (type) => {
  if (!type) return 'info';
  
  // 将类型转换为小写以便匹配
  const lowerType = type.toLowerCase();
  
  const map = {
    'initial': 'success',
    'deposit': 'info',
    'installment': 'primary',
    'refund': 'warning'
  };
  return map[lowerType] || 'info';
};

// 格式化付款类型
const formatPaymentType = (type) => {
  if (!type) return t('common.unknown');
  
  // 将类型转换为小写以便匹配i18n键
  const lowerType = type.toLowerCase();
  
  const typeMap = {
    'initial': t('paymentHistory.types.INITIAL'),
    'deposit': t('paymentHistory.types.DEPOSIT'),
    'installment': t('paymentHistory.types.INSTALLMENT'),
    'refund': t('paymentHistory.types.REFUND')
  };
  
  return typeMap[lowerType] || type;
};

// 获取付款状态颜色
const getPaymentStatusColor = (status) => {
  if (!status) return 'info';
  
  // 将状态转换为小写以便匹配
  const lowerStatus = status.toLowerCase();
  
  const map = {
    'paid': 'success',
    'pending': 'info',
    'late_paid': 'warning',
    'overdue': 'danger',
    'refunded': 'info'
  };
  return map[lowerStatus] || 'info';
};

// 格式化付款状态
const formatPaymentStatus = (status) => {
  if (!status) return t('common.unknown');
  
  // 将状态转换为小写以便匹配i18n键
  const lowerStatus = status.toLowerCase();
  
  const statusMap = {
    'paid': t('paymentHistory.statuses.PAID'),
    'pending': t('paymentHistory.statuses.PENDING'),
    'late_paid': t('paymentHistory.statuses.LATE_PAID'),
    'overdue': t('paymentHistory.statuses.OVERDUE'),
    'refunded': t('paymentHistory.statuses.REFUNDED')
  };
  
  return statusMap[lowerStatus] || status;
};

// 获取支付方式标签
const getPaymentMethodLabel = (method) => {
  if (!method) return '';
  return t(`order.payment.methods.${method.toUpperCase()}`, method);
};

// 组件挂载后，如果对话框显示且有订单ID，则获取交易记录
onMounted(() => {
  if (dialogVisible.value && props.orderId) {
    fetchPayments();
  }
});
</script>

<style lang="scss" scoped>
.payment-dialog {
  :deep(.el-dialog__title) {
    font-weight: 600;
  }
  
  :deep(.el-dialog__body) {
    padding: 15px 15px 5px;
  }
  
  :deep(.el-table) {
    border: none;
    width: 100% !important;
    table-layout: fixed;
    
    .el-table__empty-block {
      min-height: 80px;
    }
    
    .el-table__header th {
      background-color: #F5F7FA;
      color: #606266;
      font-weight: 500;
      height: 36px;
    }
    
    .el-table__body td {
      padding: 6px 0;
    }
    
    .el-table__cell {
      border: none;
    }
    
    .el-table--border {
      border: none;
    }
    
    .el-table__inner-wrapper::before {
      display: none;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    padding-right: 6px;
  }
}

.payment-dialog-content {
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
  }
  
  .transactions-container {
    margin-bottom: 12px;
    
    .section-header {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .payment-info {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--el-border-color-light);
    
    .payment-summary {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12px;
      
      .summary-item {
        display: flex;
        align-items: center;
        margin-right: 30px;
        margin-bottom: 5px;
        
        .label {
          font-size: 13px;
          color: var(--el-text-color-secondary);
          margin-right: 8px;
        }
        
        .value {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-color-danger);
        }
      }
    }
    
    .payment-form {
      .payment-method-select {
        width: 100%;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.empty-table-tip {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  background-color: #F9FAFB;
  border-radius: 0;
  margin-bottom: 0;
  font-size: 14px;
  border: none;
}
</style> 
/* 删除确认弹窗样式 */
.order-delete-confirm-dialog {
  .el-message-box {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    
    .el-message-box__header {
      padding: 24px 24px 20px;
      background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
      border-bottom: 1px solid #f0f0f0;
      
      .el-message-box__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      
      .el-message-box__headerbtn {
        top: 20px;
        right: 20px;
        
        .el-message-box__close {
          color: #909399;
          font-size: 18px;
          
          &:hover {
            color: #606266;
          }
        }
      }
    }
    
    .el-message-box__content {
      padding: 20px 24px;
      
      .el-message-box__container {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        
        .el-message-box__status {
          margin-top: 2px;
          
          &.el-icon {
            font-size: 24px;
            color: #e6a23c;
          }
        }
        
        .el-message-box__message {
          flex: 1;
          font-size: 15px;
          line-height: 1.5;
          color: #606266;
          margin: 0;
          
          p {
            margin: 0;
            line-height: 1.6;
          }
        }
      }
    }
    
    .el-message-box__btns {
      padding: 16px 24px 24px;
      text-align: right;
      
      .el-button {
        margin-left: 12px;
        padding: 10px 20px;
        font-size: 14px;
        border-radius: 6px;
        font-weight: 500;
        
        &.el-button--default {
          border-color: #dcdfe6;
          color: #606266;
          
          &:hover {
            background-color: #f5f7fa;
            border-color: #c0c4cc;
            color: #409eff;
          }
        }
        
        &.el-button--danger {
          background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
          border-color: #f56c6c;
          
          &:hover {
            background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
            border-color: #f78989;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
          }
          
          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
  
  .el-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
  }
} 
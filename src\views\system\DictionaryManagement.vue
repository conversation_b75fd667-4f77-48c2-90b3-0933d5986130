<template>
  <div class="dictionary-management-page">
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ $t('menus.pureHome') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('menus.system') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('dictionary.title') }}</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="header-title">{{ $t('dictionary.title') }}</div>
    </div>
    <div class="page-content">
      <el-card class="shadow-card">
        <el-tabs v-model="activeModule" @tab-click="handleTabChange">
          <el-tab-pane :label="$t('dictionary.moduleOrder')" name="order" />
          <el-tab-pane :label="$t('dictionary.moduleCustomer')" name="customer" />
          <el-tab-pane :label="$t('dictionary.moduleDevice')" name="device" />
          <el-tab-pane :label="$t('dictionary.moduleCompany')" name="enterprise" />
          <el-tab-pane :label="$t('dictionary.modulePayment')" name="payment" />
        </el-tabs>
        <div class="table-actions">
          <el-button type="primary" @click="handleAddField">{{ $t('dictionary.addField') }}</el-button>
        </div>
        <div v-if="fieldList.length === 0" style="padding: 48px 0;">
          <el-empty :description="$t('dictionary.noField') || '暂无自定义字段'" :image-size="80">
            <el-button type="primary" @click="handleAddField">+ {{ $t('dictionary.addField') }}</el-button>
          </el-empty>
        </div>
        <template v-else>
          <el-table v-loading="loading" :data="fieldList" style="width: 100%; margin-top: 16px; margin-right: 16px;">
            <el-table-column type="index" width="60" label="#" />
            <el-table-column prop="name" :label="$t('dictionary.fieldName')" />
            <el-table-column prop="code" :label="$t('dictionary.fieldCode')" />
            <el-table-column prop="type" :label="$t('dictionary.fieldType')" />
            <el-table-column prop="source" :label="$t('dictionary.source')" />
            <el-table-column prop="required" :label="$t('dictionary.required')" />
            <el-table-column :label="$t('dictionary.options')" min-width="200">
              <template #default="scope">
                <div v-if="scope.row.options && scope.row.options !== '-'" style="display: flex; flex-wrap: wrap; gap: 4px;">
                  <el-tag
                    v-for="(option, index) in getOptionsArray(scope.row.options)"
                    :key="isMultilingualOption(option) ? option.code : option"
                    :type="tagTypes[index % tagTypes.length]"
                    size="small"
                    effect="light"
                  >
                    {{ getDisplayText(option, $i18n.locale.value) }}
                  </el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="defaultValue" :label="$t('dictionary.defaultValue')" />
            <el-table-column :label="$t('dictionary.status')" width="100" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status" 
                  :active-value="$t('dictionary.enable')"
                  :inactive-value="$t('dictionary.disable')"
                  @change="handleStatusChange(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('dictionary.fieldOperation')"
              width="150"
              align="center"
              fixed="right"
              class-name="operations-column"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  :icon="Edit"
                  size="small"
                  :disabled="scope.row.used"
                  @click.stop="handleEditField(scope.$index, scope.row)"
                >
                  {{ $t('common.edit') }}
                </el-button>
                <el-button
                  link
                  type="danger"
                  :icon="Delete"
                  size="small"
                  :disabled="scope.row.used || scope.row.source === t('dictionary.system')"
                  @click.stop="handleDeleteField(scope.row)"
                >
                  {{ $t('common.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="padding: 0 24px 16px; display: flex; justify-content: flex-end;">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :small="false"
              :disabled="loading"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              style="margin-top: 20px;"
            />
          </div>
        </template>
      </el-card>

      <!-- 新建字段弹窗 -->
      <el-dialog v-model="addDialogVisible" :title="isEdit ? $t('dictionary.fieldEditTitle') : $t('dictionary.fieldAddTitle')" width="800px" class="big-dialog">
        <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="140px" style="max-width: 680px; margin: 0 auto;">
          <el-form-item :label="$t('dictionary.fieldName')" prop="name">
            <el-input v-model="addForm.name" :placeholder="$t('dictionary.fieldNamePlaceholder')" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item :label="$t('dictionary.fieldCode')" prop="code">
            <el-input v-model="addForm.code" :placeholder="$t('dictionary.fieldCodePlaceholder')" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item :label="$t('dictionary.fieldType')" prop="type">
            <el-select v-model="addForm.type" :placeholder="$t('dictionary.fieldTypeRequired')" :disabled="isEdit">
              <el-option :label="$t('dictionary.fieldTypeText')" value="TEXT" />
              <el-option :label="$t('dictionary.fieldTypeNumber')" value="NUMBER" />
              <el-option :label="$t('dictionary.fieldTypeDate')" value="DATE" />
              <el-option :label="$t('dictionary.fieldTypeDropdown')" value="DROPDOWN" />
              <el-option :label="$t('dictionary.fieldTypeCheckbox')" value="CHECKBOX" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('dictionary.required')" prop="required">
            <el-select v-model="addForm.required" :placeholder="$t('dictionary.required')">
              <el-option :label="$t('dictionary.yes')" value="是" />
              <el-option :label="$t('dictionary.no')" value="否" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX'" :label="$t('dictionary.options')" required>
            <div class="option-list">
              <div v-for="(item, idx) in addForm.optionsArr" :key="idx" class="option-row">
                <el-input
                  :model-value="getOptionDisplayValue(addForm.optionsArr[idx])"
                  :placeholder="$t('dictionary.optionPlaceholder')"
                  class="option-input"
                  size="default"
                  maxlength="30"
                  show-word-limit
                  @input="handleOptionInput(idx, $event)"
                  @blur="handleOptionBlur(idx)"
                />
                <el-button
                  v-if="addForm.optionsArr.length > 1"
                  type="text"
                  @click="removeOption(idx)"
                  class="option-remove-btn"
                  :icon="Minus"
                  circle
                />
              </div>
              <el-button
                type="text"
                @click="addOption"
                class="option-add-btn"
              >
                <el-icon style="margin-right:4px;"><Plus /></el-icon>{{ $t('dictionary.addOption') }}
              </el-button>
            </div>
            <div class="option-tip">{{ $t('dictionary.optionsTip') }}</div>
          </el-form-item>
          <el-form-item :label="$t('dictionary.defaultValue')" prop="defaultValue">
            <el-input v-model="addForm.defaultValue" :placeholder="$t('dictionary.defaultValuePlaceholder')" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item :label="$t('dictionary.status')" prop="status">
            <el-select v-model="addForm.status" :placeholder="$t('dictionary.statusRequired')">
              <el-option :label="$t('dictionary.enable')" value="启用" />
              <el-option :label="$t('dictionary.disable')" value="禁用" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('dictionary.source')">
            <el-input v-model="addForm.source" disabled />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="addDialogVisible = false" size="large">{{ $t('dictionary.cancel') }}</el-button>
          <el-button type="primary" @click="submitAddField" size="large">{{ $t('dictionary.save') }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, onActivated } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, Minus, Plus } from '@element-plus/icons-vue'
import {
  getDictFields,
  addDictField,
  updateDictField,
  deleteDictField,
  getModuleDictFields,
  updateDictFieldStatus,
  checkDictFieldCanDelete
} from '@/api/dictionary'; // 导入API函数
import { 
  buildMultilingualOption, 
  parseOptions, 
  convertToMultilingualOptionsSync, 
  getDisplayText,
  isMultilingualOption,
  buildMultilingualOptions,
  getOptionDisplayValue
} from '@/utils/translate'

const { t } = useI18n()
const activeModule = ref('order')
const addDialogVisible = ref(false)
const addFormRef = ref()
const isEdit = ref(false)
const editIndex = ref(-1)

// Define tag types for cycling through colors
const tagTypes = ['', 'success', 'info', 'warning', 'danger'];

// 分页变量
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const loading = ref(false); // 用于控制表格加载状态

// 模拟字段是否被使用（实际应由后端返回）
// const usedFieldCodes = ref([]) // ['order_code'] // 移除模拟数据

// 模拟各模块字段数据 // 移除模拟数据
// const moduleFields = reactive({
//   order: [
//     { name: '订单编号', code: 'order_code', type: '文本', source: t('dictionary.system'), required: '是', options: '-', defaultValue: '-', status: '启用', used: true },
//     { name: '订单状态', code: 'order_status', type: '下拉选择', source: t('dictionary.system'), required: '是', options: '已创建,进行中,已完成,已逾期', defaultValue: '已创建', status: '启用', used: true }
//   ],
//   customer: [],
//   device: [],
//   company: [],
//   payment: []
// })
const fieldList = ref([])

// 后端枚举值到前端显示文本的映射
const moduleTypeMap = {
  ORDER: t('dictionary.moduleOrder'),
  CUSTOMER: t('dictionary.moduleCustomer'),
  DEVICE: t('dictionary.moduleDevice'),
  ENTERPRISE: t('dictionary.moduleCompany'),
  PAYMENT: t('dictionary.modulePayment'),
};

const fieldTypeMap = {
  TEXT: t('dictionary.fieldTypeText'),
  NUMBER: t('dictionary.fieldTypeNumber'),
  DATE: t('dictionary.fieldTypeDate'),
  DROPDOWN: t('dictionary.fieldTypeDropdown'),
  CHECKBOX: t('dictionary.fieldTypeCheckbox'),
};

const fieldSourceMap = {
  CUSTOM: t('dictionary.new'),
  SYSTEM: t('dictionary.system'),
};

// 前端显示文本到后端枚举值的映射 (用于提交数据)
const reverseFieldTypeMap = Object.fromEntries(
  Object.entries(fieldTypeMap).map(([key, value]) => [value, key])
);

const reverseFieldSourceMap = Object.fromEntries(
  Object.entries(fieldSourceMap).map(([key, value]) => [value, key])
);

const fetchFields = async () => {
  loading.value = true;
  try {
    const response = await getDictFields({
      current: currentPage.value,
      size: pageSize.value,
      module: activeModule.value.toUpperCase(), // 确保模块名大写与后端枚举一致
    });
    fieldList.value = response.data.records.map(item => {
      // 处理选项，如果有 optionList 就使用，否则使用 options 字符串
      let processedOptions = '-'
      if (item.optionList && Array.isArray(item.optionList) && item.optionList.length > 0) {
        // 将 optionList 转换为多语言格式
        const multilingualOptions = item.optionList.map(option => ({
          code: option.value,
          zh_CN: option.label,
          en_US: option.label // 暂时使用中文，后续可以通过翻译服务更新
        }))
        processedOptions = JSON.stringify(multilingualOptions)
      } else if (item.options) {
        processedOptions = item.options
      }
      
      return {
        id: item.id,
        name: item.fieldName,
        code: item.fieldCode,
        type: fieldTypeMap[item.fieldType.toUpperCase()] || item.fieldType, // 映射到中文，确保转换为大写
        source: fieldSourceMap[item.source.toUpperCase()] || item.source, // 映射到中文，确保转换为大写
        required: item.required ? t('common.yes') : t('common.no'), // 映射到中文
        options: processedOptions,
        defaultValue: item.defaultValue ? item.defaultValue : '-',
        status: item.enabled ? t('dictionary.enable') : t('dictionary.disable'), // 映射到中文
        used: item.isUsed || false, // 从后端获取 isUsed 字段
        sortOrder: item.sortOrder,
      }
    });
    total.value = typeof response.total === 'number' ? response.total : fieldList.value.length; // Ensure total is a number or fallback to list length
  } catch (error) {
    console.log('Error object in fetchFields catch:', error); // Added detailed log

    fieldList.value = []; // Always clear the list
    total.value = 0; // Always set total to 0

    let showErrorMessage = true; // Assume we show the error by default

    const errorMessageText = error.message || (error.response && error.response.data && error.response.data.message) || '';
    const lowerCaseErrorMessageText = errorMessageText.toLowerCase();

    // Define generic failure messages that should suppress error display
    const genericFailureMessages = [
      t('common.fetchFailed').toLowerCase(),
      t('common.operationFailed').toLowerCase(),
      '请求失败', // Specific text from user screenshot
      'request failed', // Generic Axios message
      'network error', // Generic browser network error
    ];

    // Priority 1: Backend explicitly indicates empty data (even if status is not 200)
    if (error.response && error.response.data) {
      const responseData = error.response.data;
      // Check if responseData has a 'records' array and it's empty (often for 200 OK with no data)
      if (responseData.records !== undefined && Array.isArray(responseData.records) && responseData.records.length === 0) {
        showErrorMessage = false;
      }
      // Priority 2: Check for generic error messages in response data
      else if (responseData.message) {
        const lowerCaseResponseMessage = responseData.message.toLowerCase();
        if (genericFailureMessages.some(msg => lowerCaseResponseMessage.includes(msg))) {
          showErrorMessage = false;
        }
      }
    }
    // Priority 3: Pure network error (no response from server)
    else if (!error.response) {
        if (genericFailureMessages.some(msg => lowerCaseErrorMessageText.includes(msg))) {
            showErrorMessage = false;
        }
    }

    if (showErrorMessage) {
      ElMessage.error(t('common.fetchFailed') + ': ' + errorMessageText);
    }
  } finally {
    loading.value = false;
  }
};

const handleTabChange = () => {
  currentPage.value = 1;
  fetchFields();
};

onMounted(() => {
  fetchFields();
});

onActivated(() => {
  // 当组件被激活（例如从其他页面返回）时，重新加载数据
  fetchFields();
});

const addForm = reactive({
  id: null, // 新增或编辑时会用到
  name: '',
  code: '',
  type: '', // 后端接收的是 FieldType 枚举的字符串，例如 'TEXT'
  required: '', // 后端接收的是 Boolean 类型
  optionsArr: [''],
  defaultValue: '',
  status: '禁用', // 后端接收的是 Boolean 类型 (enabled)
  source: fieldSourceMap.CUSTOM, // Directly use the backend enum value for new fields
  used: false, // 这个字段主要从后端返回，前端提交时不需设置
  fieldCode: '', // 与后端字段名保持一致
});

const addRules = {
  name: [
    { required: true, message: t('dictionary.fieldNameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('dictionary.fieldNameRequired'), trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/, message: t('dictionary.fieldNameRule'), trigger: 'blur' }
  ],
  code: [
    { required: true, message: t('dictionary.fieldCodeRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('dictionary.fieldCodeRequired'), trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/, message: t('dictionary.fieldCodeRule'), trigger: 'blur' }
  ],
  type: [
    { required: true, message: t('dictionary.fieldTypeRequired'), trigger: 'change' }
  ],
  required: [
    { required: true, message: t('dictionary.requiredRequired'), trigger: 'change' }
  ],
  status: [
    { required: true, message: t('dictionary.statusRequired'), trigger: 'change' }
  ]
}

const addOption = () => {
  if (addForm.optionsArr.length >= 50) {
    ElMessage.warning(t('dictionary.maxOptions'))
    return
  }
  addForm.optionsArr.push('')
}

const handleOptionInput = (idx, value) => {
  // 如果输入的是简单文本，转换为多语言格式
  if (typeof value === 'string') {
    // 检查是否已经是多语言对象
    const currentOption = addForm.optionsArr[idx]
    if (isMultilingualOption(currentOption)) {
      // 更新中文文本，保持英文不变
      currentOption.zh_CN = value
      // 暂时使用中文生成 code，后续在失焦时翻译
      currentOption.code = value.toUpperCase().replace(/[^A-Z0-9]/g, '_')
      // 清空英文，强制重新翻译
      currentOption.en_US = value
    } else {
      // 创建新的多语言对象
      const code = value.toUpperCase().replace(/[^A-Z0-9]/g, '_')
      addForm.optionsArr[idx] = {
        code: code,
        zh_CN: value,
        en_US: value // 暂时使用原文，失焦时翻译
      }
    }
  }
}

// 新增：处理选项失焦事件，在失焦时调用翻译
const handleOptionBlur = async (idx) => {
  const currentOption = addForm.optionsArr[idx]
  if (isMultilingualOption(currentOption) && currentOption.zh_CN && currentOption.zh_CN.trim()) {
    // 调用翻译API
    try {
      const { autoTranslate } = await import('@/api/translation')
      const response = await autoTranslate({ text: currentOption.zh_CN })
              if (response.code === 1000 && response.data && response.data.translatedText) {
          // 更新英文翻译
          currentOption.en_US = response.data.translatedText
          // 根据英文翻译生成code：英文大写，用下划线拼接，去掉末尾下划线
          const words = response.data.translatedText.toUpperCase().split(/[^A-Z0-9]+/).filter(word => word.length > 0)
          const code = words.join('_')
          currentOption.code = code
          console.log(`翻译成功: "${currentOption.zh_CN}" -> "${response.data.translatedText}"`)
          console.log(`生成的code: "${code}"`)
        }
    } catch (error) {
      console.error('翻译失败:', error)
      currentOption.en_US = currentOption.zh_CN // 翻译失败时使用原文
    }
  }
}
const removeOption = idx => {
  if (addForm.optionsArr.length > 1) {
    addForm.optionsArr.splice(idx, 1)
  } else {
    addForm.optionsArr[0] = ''
  }
}
const onTypeChange = () => {
  if (addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX') {
    addForm.optionsArr = ['']
  } else {
    addForm.optionsArr = []
  }
  addForm.defaultValue = ''
}

const handleAddField = () => {
  isEdit.value = false
  editIndex.value = -1
  Object.assign(addForm, {
    id: null, // Reset ID for new field
    name: '', code: '', type: '', required: '', optionsArr: [''], defaultValue: '', status: '禁用', source: fieldSourceMap.CUSTOM, used: false
  })
  addDialogVisible.value = true
  nextTick(() => {
    addFormRef.value && addFormRef.value.resetFields()
  })
}

const handleEditField = (index, row) => {
  isEdit.value = true
  editIndex.value = index // Still useful for splicing if needed, but not for API calls
  addForm.id = row.id // Set the ID for update
  addForm.name = row.name
  addForm.code = row.code
  // Map back to backend enum values for form submission
  addForm.type = reverseFieldTypeMap[row.type] || row.type
  addForm.required = row.required === t('common.yes') ? '是' : '否' // Keep frontend display for dropdown
  addForm.defaultValue = row.defaultValue === '-' ? '' : row.defaultValue
  addForm.status = row.status // Keep frontend display for switch
  addForm.source = row.source // 直接使用已翻译的中文显示值
  addForm.used = row.used

  if (addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX') {
    const optionsArray = getOptionsArray(row.options);
    if (optionsArray.length > 0) {
      // 检查是否已经是多语言格式
      const hasMultilingualOptions = optionsArray.some(opt => isMultilingualOption(opt))
      
      if (hasMultilingualOptions) {
        // 已经是多语言格式，直接使用
        addForm.optionsArr = optionsArray
      } else {
        // 简单文本格式，转换为多语言格式用于编辑
        addForm.optionsArr = optionsArray.map(opt => {
          if (isMultilingualOption(opt)) {
            return opt
          } else {
            // 生成 code：英文大写，多文字用下划线拼接
            const code = opt.toUpperCase().replace(/[^A-Z0-9]/g, '_')
            return { code: code, zh_CN: opt, en_US: opt }
          }
        })
      }
    } else {
      addForm.optionsArr = ['']
    }
  } else {
    addForm.optionsArr = []
  }
  addDialogVisible.value = true
  nextTick(() => {
    addFormRef.value && addFormRef.value.clearValidate()
  })
}

const handleDeleteField = async (row) => {
  if (row.used) {
    ElMessage.warning(t('dictionary.usedFieldDeleteNotAllowed'))
    return
  }
  ElMessageBox.confirm(t('dictionary.deleteConfirm'), t('dictionary.confirmDelete'), {
    confirmButtonText: t('dictionary.confirmDelete'),
    cancelButtonText: t('dictionary.cancel'),
    type: 'warning',
  }).then(async () => {
    try {
      const canDelete = await checkDictFieldCanDelete(row.id);
      if (!canDelete) {
        ElMessage.warning(t('dictionary.usedFieldDeleteNotAllowed'));
        return;
      }
      await deleteDictField(row.id);
      ElMessage.success(t('dictionary.deleteSuccess'));
      fetchFields(); // Refresh list after deletion
    } catch (error) {
      ElMessage.error(t('dictionary.deleteFailed') + ': ' + error.message);
    }
  }).catch(() => {})
}

const validateOptions = async () => {
  if (addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX') {
    console.log("addForm.optionsArr:", addForm.optionsArr)
    
    // 处理选项数组，支持字符串和多语言对象
    const processedOptions = []
    const textOptions = []
    
    for (const opt of addForm.optionsArr) {
      if (typeof opt === 'string') {
        const trimmed = opt.trim()
        if (trimmed) {
          textOptions.push(trimmed)
        }
      } else if (isMultilingualOption(opt)) {
        // 已经是多语言格式，直接使用
        processedOptions.push(opt)
      } else if (opt && typeof opt === 'object' && opt.zh_CN) {
        // 简单的多语言对象，直接使用
        processedOptions.push(opt)
      }
    }
    
    // 验证文本选项
    if (textOptions.length > 0) {
      if (textOptions.length > 50) {
        ElMessage.error(t('dictionary.maxOptions'))
        return false
      }
      if (textOptions.some(opt => opt.length < 2 || opt.length > 30)) {
        ElMessage.error(t('dictionary.fieldOptionLength'))
        return false
      }
      const set = new Set(textOptions)
      if (set.size !== textOptions.length) {
        ElMessage.error(t('dictionary.optionDuplicate'))
        return false
      }
      
      // 只有新增的文本选项才需要翻译
      try {
        console.log('Translating text options:', textOptions)
        const multilingualOptions = await buildMultilingualOptions(textOptions)
        console.log('Translation results:', multilingualOptions)
        processedOptions.push(...multilingualOptions)
      } catch (error) {
        console.error('Failed to convert options to multilingual format:', error)
        // 如果翻译失败，使用同步版本
        const syncOptions = convertToMultilingualOptionsSync(textOptions)
        processedOptions.push(...syncOptions)
      }
      
      // 检查并修复已有的多语言选项（如果英文还是中文，需要重新翻译）
      for (const option of processedOptions) {
        if (isMultilingualOption(option) && option.en_US === option.zh_CN) {
          console.log('Re-translating option:', option.zh_CN)
          try {
            const translated = await translateText(option.zh_CN, 'en-US')
            option.en_US = translated
            // 根据翻译后的英文重新生成 code
            option.code = translated.toUpperCase().replace(/[^A-Z0-9]/g, '_')
            console.log('Re-translation result:', option)
          } catch (error) {
            console.error('Re-translation failed for:', option.zh_CN, error)
          }
        }
      }
    }
    
    if (processedOptions.length === 0) {
      ElMessage.error(t('dictionary.optionsRequired'))
      return false
    }
    
    addForm.optionsArr = processedOptions
    return true
  }
  return true
}

const validateDefaultValue = () => {
  console.log("addForm.defaultValue:", addForm.defaultValue)
  if (!addForm.defaultValue) return true
  if (addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX') {

    console.log(addForm.optionsArr)
    console.log(addForm.defaultValue)
    // 确保默认值在已清理的选项中
    const optionValues = addForm.optionsArr.map(option => {
      if (isMultilingualOption(option)) {
        return option.zh_CN // 使用中文作为默认值的匹配
      }
      return option
    })
    if (!optionValues.includes(addForm.defaultValue)) {
      ElMessage.error(t('dictionary.fieldDefaultValueNotInOptions'))
      return false
    }
  } else if (addForm.type === 'DATE') {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(addForm.defaultValue)) {
      ElMessage.error(t('dictionary.fieldDefaultValueDateFormat'))
      return false
    }
  } else if (addForm.type === 'NUMBER') {
    if (!/^\d{1,9}$/.test(addForm.defaultValue)) {
      ElMessage.error(t('dictionary.fieldDefaultValueNumber'))
      return false
    }
  } else if (addForm.type === 'TEXT') {
    if (addForm.defaultValue.length > 50) {
      ElMessage.error(t('dictionary.fieldDefaultValueText'))
      return false
    }
  }
  return true
}

const submitAddField = async () => {
  addFormRef.value.validate(async (valid) => { // Make it async
    console.log("valid:", valid)
    if (!valid) return

    // 首先验证并清理选项
    if (!(await validateOptions())) return

    // 移除了默认值自动更正的逻辑。现在，如果默认值无效，validateDefaultValue 将捕获它。

    // 最后验证默认值（现在基于已更正的默认值和已清理的选项）
    if (!validateDefaultValue()) return
    // 校验唯一性 - 后端会处理唯一性校验，前端可以移除或保留作为初步校验
    // const codeRepeat = fieldList.value.some((f, idx) => f.code === addForm.code && idx !== editIndex.value)
    // if (codeRepeat) {
    //   ElMessage.error(t('dictionary.fieldCodeDuplicate'))
    //   return
    // }
    // const nameRepeat = fieldList.value.some((f, idx) => f.name === addForm.name && idx !== editIndex.value)
    // if (nameRepeat) {
    //   ElMessage.error(t('dictionary.fieldNameDuplicate'))
    //   return
    // }

    // Prepare data for backend
    const payload = {
      id: addForm.id, // Will be null for add, or ID for edit
      fieldName: addForm.name,
      fieldCode: addForm.code,
      module: activeModule.value.toUpperCase(), // Assuming module is always from current tab
      fieldType: addForm.type, // This is already the ENUM key like 'TEXT'
      required: addForm.required === t('common.yes') ? true : false, // Map to boolean
      options: (addForm.type === 'DROPDOWN' || addForm.type === 'CHECKBOX')
        ? JSON.stringify(addForm.optionsArr.filter(opt => opt && (typeof opt === 'string' || isMultilingualOption(opt)))) // Save as JSON string array
        : null, // If not dropdown/checkbox, set to null
      defaultValue: addForm.defaultValue || null, // Ensure empty string becomes null for backend
      enabled: addForm.status === t('dictionary.enable') ? true : false, // Map to boolean
      source: reverseFieldSourceMap[addForm.source] || addForm.source, // Map to backend enum
      // sortOrder: addForm.sortOrder, // If sortOrder is managed via add/edit, add it here
      // isUsed: addForm.used, // isUsed is returned by backend, not sent
    };

    try {
      if (isEdit.value) {
        await updateDictField(payload.id, payload);
        ElMessage.success(t('dictionary.fieldEditSuccess'));
      } else {
        await addDictField(payload);
        ElMessage.success(t('dictionary.fieldAddSuccess'));
      }
      addDialogVisible.value = false;
      fetchFields(); // Refresh list after add/edit
    } catch (error) {
      ElMessage.error(t('common.operationFailed') + ': ' + error.message);
    }
  })
}

// Function for status switch
const handleStatusChange = async (row) => {
  const newStatusBoolean = row.status === t('dictionary.enable') ? true : false;
  const confirmMsg = newStatusBoolean ? t('dictionary.enableConfirm') : t('dictionary.disableConfirm');
  const confirmTitle = newStatusBoolean ? t('dictionary.enable') : t('dictionary.disable');

  ElMessageBox.confirm(confirmMsg, confirmTitle, {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: 'warning',
  }).then(async () => {
    try {
      await updateDictFieldStatus(row.id, newStatusBoolean);
      ElMessage.success(t('dictionary.fieldStatusChangeSuccess'));
      // Update the status in the local fieldList directly if success
      row.status = newStatusBoolean ? t('dictionary.enable') : t('dictionary.disable');
    } catch (error) {
      ElMessage.error(t('dictionary.fieldStatusChangeFailed') + ': ' + error.message);
      // Revert the switch state on error
      row.status = !newStatusBoolean ? t('dictionary.enable') : t('dictionary.disable');
    }
  }).catch(() => {
    // User cancelled, revert switch state
    row.status = !newStatusBoolean ? t('dictionary.enable') : t('dictionary.disable');
  });
};

const handleSizeChange = (newSize) => {
  currentPage.value = 1;
  pageSize.value = newSize;
  fetchFields();
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
  fetchFields();
}

const getOptionsArray = (optionsStr) => {
  if (!optionsStr || optionsStr === '-') {
    return [];
  }
  // Try parsing as JSON first
  if (typeof optionsStr === 'string' && optionsStr.startsWith('[') && optionsStr.endsWith(']')) {
    try {
      const parsed = JSON.parse(optionsStr);
      if (Array.isArray(parsed)) {
        return parsed.filter(Boolean); // Filter out empty values
      }
    } catch (e) {
      // Parsing failed, fall through to comma-split
    }
  }
  // Fallback to comma-separated for old data or failed JSON parse
  return optionsStr.split(',').filter(Boolean);
};

function getTypeValue(label) {
  switch (label) {
    case t('dictionary.fieldTypeText'): return 'TEXT' // Return backend enum code
    case t('dictionary.fieldTypeNumber'): return 'NUMBER'
    case t('dictionary.fieldTypeDate'): return 'DATE'
    case t('dictionary.fieldTypeDropdown'): return 'DROPDOWN'
    case t('dictionary.fieldTypeCheckbox'): return 'CHECKBOX'
    default: return label // Fallback, though ideally shouldn't happen with proper mapping
  }
}
function getTypeLabel(type) {
  switch (type) {
    case 'TEXT': return t('dictionary.fieldTypeText')
    case 'NUMBER': return t('dictionary.fieldTypeNumber')
    case 'DATE': return t('dictionary.fieldTypeDate')
    case 'DROPDOWN': return t('dictionary.fieldTypeDropdown')
    case 'CHECKBOX': return t('dictionary.fieldTypeCheckbox')
    default: return type
  }
}
</script>

<style lang="scss" scoped>
.dictionary-management-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 16px;
    background-color: #fff;
    padding: 16px 24px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .header-title {
      display: none;
    }

    :deep(.el-breadcrumb) {
      line-height: 1;
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    
    .shadow-card {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      
      :deep(.el-card__body) {
        padding: 0;
      }

      .el-tabs {
        padding: 0 24px;
        margin-bottom: 0;
        border-bottom: 1px solid #ebeef5;
      }

      .table-actions {
        background-color: #fff;
        padding: 16px 24px;
        border-radius: 4px 4px 0 0;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }

      :deep(.el-table) {
        margin: 16px 24px;
        border-radius: 4px;
        overflow: hidden;

        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }

        td {
          padding: 8px 0;
        }

        :deep(.el-button--small) {
          padding-top: 0;
          padding-bottom: 0;
        }
      }

      :deep(.operations-column > .cell) {
        padding-right: 24px;
        box-sizing: border-box;
      }

      .el-empty {
        margin: 32px 0;
      }
    }
  }
}

// 弹窗样式统一
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
  
  .el-dialog__header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      line-height: 1;
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    text-align: right;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 修复弹窗层级问题
:deep(.el-dialog__wrapper) {
  z-index: 2000 !important;
}

:deep(.v-modal) {
  z-index: 1999 !important;
}

// 选项列表样式优化
.option-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;

  .option-row {
    display: flex;
    align-items: center;
    gap: 8px;

    .option-input {
      flex: 1;
      min-width: 300px;
    }

    .option-remove-btn {
      padding: 0;
      margin: 0;
      color: #f56c6c;
      
      &:hover {
        color: #ff4d4f;
        background: rgba(245, 108, 108, 0.1);
      }
    }
  }

  .option-add-btn {
    margin-top: 8px;
    padding: 0;
    color: var(--el-color-primary);
    
    &:hover {
      color: var(--el-color-primary-light-3);
    }
  }
}

.option-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  .el-button {
    min-width: 80px;
  }
}
</style> 
# 客户详情页面编辑附件显示问题修复

## 问题描述
- **问题**：在客户详情页面点击"编辑"按钮时，附件没有在编辑对话框中显示
- **正常情况**：在客户列表页面点击"编辑"按钮时，附件能正常显示

## 问题原因分析

### 列表页面编辑 vs 详情页面编辑

**列表页面编辑（正常工作）**：
```javascript
// handleEdit 函数
const handleEdit = async (row) => {
  // 1. 重新调用API获取完整客户数据
  const res = await getCustomerDetail(row.id);
  
  // 2. 正确格式化附件数据给编辑对话框
  if (res.data.attachments) {
    currentCustomer.value.attachmentsToDisplay = res.data.attachments.map(att => ({
      name: att.fileName,
      url: att.fileUrl,
      uid: att.id || Date.now() + Math.random(),
      status: 'success',
      // ... 其他附件字段
    }));
  }
  
  // 3. 打开编辑对话框
  dialogVisible.value = true;
}
```

**详情页面编辑（问题所在）**：
```javascript
// handleEditFromDetail 函数（修复前）
const handleEditFromDetail = () => {
  // 问题：直接使用当前的 currentCustomer.value
  // 但这个数据结构可能不包含编辑对话框需要的 attachmentsToDisplay 格式
  detailVisible.value = false
  nextTick(() => {
    dialogVisible.value = true  // 附件数据格式不正确
  })
}
```

## 修复方案

### 修复后的 handleEditFromDetail 函数

```javascript
const handleEditFromDetail = async () => {
  if (!currentCustomer.value || !currentCustomer.value.id) return
  
  try {
    // 1. 重新获取完整的客户数据，确保附件信息正确
    const res = await getCustomerDetail(currentCustomer.value.id);
    
    if (res?.code === 1000 && res.data) {
      // 2. 更新客户数据，包含正确格式的附件信息
      currentCustomer.value = {
        ...res.data.customerInfo,
        id: currentCustomer.value.id
      };
      
      // 3. 为编辑对话框准备附件数据（与列表编辑保持一致）
      if (res.data.attachments) {
        currentCustomer.value.attachmentsToDisplay = res.data.attachments.map(att => ({
          name: att.fileName,
          url: att.fileUrl,
          uid: att.id || Date.now() + Math.random(),
          status: 'success',
          thumbnailUrl: att.thumbnailUrl,
          fileSize: att.fileSize,
          fileType: att.fileType,
          uploadTime: att.uploadTime,
          raw: {
            id: att.id,
            fileName: att.fileName,
            fileUrl: att.fileUrl,
            thumbnailUrl: att.thumbnailUrl,
            fileSize: att.fileSize,
            fileType: att.fileType,
            uploadTime: att.uploadTime
          }
        }));
      }
      
      // 4. 关闭详情对话框，打开编辑对话框
      detailVisible.value = false
      nextTick(() => {
        dialogVisible.value = true
      })
    } else {
      ElMessage.error(res?.message || t('customer.details.fetchFailed'));
    }
  } catch (error) {
    console.error('从详情页面编辑失败:', error);
    ElMessage.error(error.message || t('customer.details.fetchFailed'));
  }
}
```

## 修复要点

### 1. 数据格式统一
确保两种编辑方式都使用相同的数据格式：
- `attachmentsToDisplay` - 编辑对话框期望的格式
- 包含 `name`, `url`, `uid`, `status` 等 el-upload 需要的字段
- 包含完整的附件元数据（thumbnailUrl, fileSize, fileType, uploadTime）

### 2. API调用一致性
两种编辑方式都调用 `getCustomerDetail(id)` 获取最新数据：
- 确保数据是最新的
- 确保附件信息完整
- 避免数据格式不一致的问题

### 3. 错误处理
添加适当的错误处理：
- API调用失败时显示错误消息
- 确保用户体验一致

## 测试验证

### 测试步骤
1. **列表页面编辑测试**：
   - 在客户列表中找到有附件的客户
   - 点击"编辑"按钮
   - 验证附件在编辑对话框中正确显示

2. **详情页面编辑测试**：
   - 在客户列表中点击"详情"查看有附件的客户
   - 在详情对话框中点击"编辑"按钮
   - 验证附件在编辑对话框中正确显示

### 预期结果
- ✅ 列表页面编辑：附件正常显示
- ✅ 详情页面编辑：附件正常显示（修复后）
- ✅ 两种方式的附件显示格式一致
- ✅ 附件的缩略图、文件大小、上传时间等信息正确显示

## 相关文件
- `src/views/customer/index.vue` - 客户列表和详情页面
- `src/components/customer/CreateCustomerDialog.vue` - 客户编辑对话框

## 注意事项
1. 确保后端API返回完整的附件字段（thumbnailUrl, fileSize, fileType, uploadTime）
2. 编辑对话框需要正确处理 `attachmentsToDisplay` 格式的数据
3. 保持两种编辑方式的数据格式一致性

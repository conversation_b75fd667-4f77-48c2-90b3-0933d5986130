import { defineStore } from "pinia";
import { storageLocal } from "@pureadmin/utils";
import { responsiveStorageNameSpace } from "@/config";

export const useAppStore = defineStore({
  id: "pure-app",
  state: () => ({
    sidebar: {
      opened: storageLocal().getItem("responsive-state")?.sidebarStatus ?? true,
      withoutAnimation: false,
      isClickCollapse: false
    },
    // 这里我们只用到了部分状态，完整的状态管理应根据实际需求添加
    device: "desktop",
    // 系统设置面板
    panel: {
      opened: false
    },
    // 布局大小
    viewportSize: {
      width: 0,
      height: 0
    }
  }),
  actions: {
    toggleSideBar(opened, type) {
      const sidebar = this.sidebar;
      sidebar.opened = typeof opened === "boolean" ? opened : !sidebar.opened;
      sidebar.withoutAnimation = type === "resize";
      
      if (type !== "resize") {
        sidebar.isClickCollapse = true;
      } else {
        sidebar.isClickCollapse = false;
      }
      
      storageLocal().setItem("responsive-state", {
        sidebarStatus: sidebar.opened
      });
    },
    toggleDevice(device) {
      this.device = device;
    },
    togglePanel() {
      this.panel.opened = !this.panel.opened;
    },
    setViewportSize(size) {
      this.viewportSize.width = size.width;
      this.viewportSize.height = size.height;
    }
  }
});
const TOKEN_KEY = 'Admin-Token'
const USER_INFO_KEY = 'userInfo'

/**
 * 获取token
 * @returns {string} token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置token
 * @param {string} token
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 解析JWT token
 * @param {string} token - JWT token
 * @returns {Object} 解析后的数据
 */
export function parseJwt(token) {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    const decoded = JSON.parse(jsonPayload)
    
    // 确保enterpriseId是数字类型，后端API可能需要数字类型
    if (decoded.enterpriseId && typeof decoded.enterpriseId === 'string') {
      const numericId = parseInt(decoded.enterpriseId, 10)
      if (!isNaN(numericId)) {
        decoded.enterpriseId = numericId
      }
    }
    
    console.log('解析后的token数据:', decoded)
    return decoded
  } catch (error) {
    console.error('解析token失败', error)
    return {}
  }
}

/**
 * 保存用户信息到localStorage
 * @param {Object} userInfo - 用户信息对象
 */
export function saveUserInfo(userInfo) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息
 */
export function getUserInfo() {
  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY)
    if (!userInfoStr) return {}
    
    const userInfo = JSON.parse(userInfoStr)
    
    // 确保enterpriseId是数字类型，后端API可能需要数字类型
    if (userInfo.enterpriseId && typeof userInfo.enterpriseId === 'string') {
      const numericId = parseInt(userInfo.enterpriseId, 10)
      if (!isNaN(numericId)) {
        userInfo.enterpriseId = numericId
      }
    }
    
    return userInfo
  } catch (error) {
    console.error('获取用户信息失败', error)
    return {}
  }
}

/**
 * 检查接口返回状态
 * @param {number} code 
 * @returns {boolean}
 */
export function isSuccess(code) {
  return code === 1000
}
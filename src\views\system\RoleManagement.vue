<template>
  <div class="role-management-container">
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ $t('menus.pureHome') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('menus.system') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('system.roleManagement') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" ref="searchFormRef" :inline="true" class="search-form">
        <el-form-item prop="roleName">
          <el-input
            v-model="searchForm.roleName"
            :placeholder="$t('system.pleaseEnterRoleName')"
            clearable
            style="width: 280px"
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            {{ $t('common.search') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        {{ $t('system.addRole') }}
      </el-button>
    </div>

    <!-- 角色列表 -->
    <div class="table-container" v-loading="loading">
      <el-table
        :data="roleList"
        border
        style="width: 100%"
        :empty-text="$t('common.noData')"
        highlight-current-row
        stripe
        @row-click="handleRowClick"
      >
        <el-table-column type="index" width="70" align="center" :label="$t('common.index')" />
        <el-table-column prop="roleName" :label="$t('system.roleName')" min-width="200" show-overflow-tooltip />
        <el-table-column prop="remark" :label="$t('system.remark')" min-width="300" show-overflow-tooltip />
        <el-table-column prop="createTime" :label="$t('common.createTime')" min-width="180" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operations')" fixed="right" width="220" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              :icon="Edit"
              size="small"
              @click.stop="handleEdit(scope.row)"
            >
              {{ $t('common.edit') }}
            </el-button>
            <el-button
              link
              type="danger"
              :icon="Delete"
              size="small"
              @click.stop="handleDelete(scope.row)"
            >
              {{ $t('common.delete') }}
            </el-button>
            <el-button
              link
              type="success" 
              size="small"
              @click.stop="handleAssignPermission(scope.row)"
            >
              {{ $t('system.assignPermissions') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        {{ $t('system.totalItems', { total: pagination.total }) }}
      </div>
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        :layout="`sizes, prev, pager, next, jumper, ${t('customer.pagination.pageClassifier')}`"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #goto>
          {{ t('customer.pagination.goto') }}
        </template>
        <template #jumper>
           {{ t('customer.pagination.pageClassifier') }}
        </template>
         <template #sizes="{ item }">
          {{ item.value + t('customer.pagination.pagesize') }}
        </template>
      </el-pagination>
    </div>

    <!-- 新增/编辑角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? $t('system.editRole') : $t('system.addRole')"
      width="500px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <el-form
        :model="roleForm"
        :rules="roleRules"
        ref="roleFormRef"
        label-width="100px"
        class="role-form"
      >
        <el-form-item :label="$t('system.roleName')" prop="roleName">
          <el-input
            v-model="roleForm.roleName"
            :placeholder="$t('system.pleaseEnterRoleName')"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item :label="$t('system.remark')" prop="remark">
          <el-input
            v-model="roleForm.remark"
            :placeholder="$t('system.pleaseEnterRemark')"
            type="textarea"
            :rows="4"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ $t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <permission-dialog
      v-model="permissionDialogVisible"
      :role="currentRole"
      @success="handlePermissionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Edit, Delete, Plus } from '@element-plus/icons-vue'
import { getRoleList, createRole, updateRole, deleteRole } from '@/api/role'
import { formatDateTime } from '@/utils/format'
import { getUserInfo } from '@/utils/auth'
import PermissionDialog from '@/components/permission/PermissionDialog.vue'

const { t } = useI18n()

// 搜索防抖定时器
let searchTimer = null

// 获取企业ID
const getEnterpriseId = () => {
  try {
    const userInfo = getUserInfo()
    console.log('从localStorage获取的用户信息:', userInfo)
    
    // 确保enterpriseId是数字类型
    let id = userInfo.enterpriseId
    if (typeof id === 'string') {
      id = parseInt(id, 10)
      if (isNaN(id)) {
        console.warn('企业ID不是有效数字:', userInfo.enterpriseId)
        return 1 // 如果无法转换，默认返回1
      }
    } else if (id === undefined || id === null) {
      console.warn('未找到企业ID，使用默认值')
      return 1 // 默认值
    }
    
    console.log('使用企业ID:', id)
    return id
  } catch (error) {
    console.error('获取企业ID失败:', error)
    return 1 // 出错时返回默认值
  }
}

const enterpriseId = getEnterpriseId()

// 搜索表单
const searchForm = reactive({
  roleName: '',
  enterpriseId
})
const searchFormRef = ref()

// 角色列表
const roleList = ref([])
const loading = ref(false)
// 添加一个请求状态标志，防止重复请求
let isRequesting = false

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 角色表单
const dialogVisible = ref(false)
const isEdit = ref(false)
const roleForm = reactive({
  id: '',
  roleName: '',
  remark: '',
  enterpriseId
})
const roleFormRef = ref()
const submitting = ref(false)

// 表单验证规则
const roleRules = reactive({
  roleName: [
    { required: true, message: t('system.roleNameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('system.roleNameLength'), trigger: 'blur' }
  ],
  remark: [
    { max: 100, message: t('system.remarkLength'), trigger: 'blur' }
  ]
})

// 权限对话框
const permissionDialogVisible = ref(false)
const currentRole = ref(null)

// 获取角色列表
const fetchRoleList = async () => {
  if (isRequesting) return
  
  isRequesting = true
  loading.value = true
  try {
    const params = {
      page: pagination.current > 0 ? pagination.current - 1 : 0,
      size: pagination.size,
      enterpriseId,
      roleName: searchForm.roleName || undefined
    }
    
    console.log('获取角色列表的请求参数 (0-indexed page):', params)
    
    const res = await getRoleList(params)
    console.log('获取角色列表的响应:', res)
    
    if (res.code === 1000) {
      roleList.value = res.data.records || []
      pagination.total = res.data.total || 0
      
      pagination.current = (res.data.current || 0); 
      pagination.size = res.data.size || 10;
    } else {
      ElMessage.error(res.message || t('system.fetchFailed'))
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error(t('system.fetchFailed'))
  } finally {
    loading.value = false
    isRequesting = false
  }
}

// 搜索
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    pagination.current = 1
    fetchRoleList()
  }, 300)
}

// 重置搜索
const handleReset = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchFormRef.value?.resetFields()
  searchForm.roleName = ''
  pagination.current = 1
  fetchRoleList()
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.size = val;
  pagination.current = 1;
  fetchRoleList();
}

const handleCurrentChange = (val) => {
  fetchRoleList();
}

// 添加角色
const handleAdd = () => {
  isEdit.value = false
  Object.assign(roleForm, {
    id: '',
    roleName: '',
    remark: '',
    enterpriseId: enterpriseId
  })
  dialogVisible.value = true
  // 等待DOM更新后设置焦点
  setTimeout(() => {
    roleFormRef.value?.resetFields()
  }, 0)
}

// 编辑角色
const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(roleForm, {
    id: row.roleId,
    roleName: row.roleName,
    remark: row.remark,
    enterpriseId: enterpriseId
  })
  dialogVisible.value = true
}

// 行点击事件
const handleRowClick = (row) => {
  // 可以实现选中行逻辑或其他交互
}

// 对话框关闭
const handleDialogClose = () => {
  roleFormRef.value?.resetFields()
  dialogVisible.value = false
}

// 删除角色
const handleDelete = (row) => {
  ElMessageBox.confirm(
    t('system.deleteRoleConfirm'),
    t('system.deleteRole'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        console.log('要删除的角色:', row)
        const roleId = row.roleId
        console.log('删除角色ID:', roleId)
        
        const res = await deleteRole(roleId)
        console.log('删除角色响应:', res)
        
        if (res.code === 1000) {
          ElMessage.success(t('system.deleteSuccess'))
          pagination.current = 1;
          fetchRoleList()
        } else {
          ElMessage.error(res.message || t('system.deleteFailed'))
        }
      } catch (error) {
        console.error('删除角色失败:', error)
        ElMessage.error(t('system.deleteFailed'))
      }
    })
    .catch(() => {})
}

// 提交表单
const submitForm = async () => {
  roleFormRef.value?.validate(async (valid) => {
    if (!valid) return
    
    submitting.value = true
    try {
      const data = {
        roleName: roleForm.roleName,
        remark: roleForm.remark,
        enterpriseId,
      }
      
      console.log('保存角色的数据:', data)
      
      let res
      if (isEdit.value) {
        data.id = roleForm.id
        res = await updateRole(data)
      } else {
        res = await createRole(data)
      }
      
      console.log('保存角色的响应:', res)
      
      if (res.code === 1000) {
        ElMessage.success(isEdit.value ? t('system.updateSuccess') : t('system.createSuccess'))
        dialogVisible.value = false
        pagination.current = 1;
        fetchRoleList()
      } else {
        ElMessage.error(res.message || (isEdit.value ? t('system.updateFailed') : t('system.createFailed')))
      }
    } catch (error) {
      console.error('操作失败:', error)
      ElMessage.error(isEdit.value ? t('system.updateFailed') : t('system.createFailed'))
    } finally {
      submitting.value = false
    }
  })
}

// 分配权限
const handleAssignPermission = (row) => {
  // currentRole.value = row
  // permissionDialogVisible.value = true
  ElMessage.info('敬请期待，该功能正在开发中...'); // Display coming soon message
}

// 权限分配成功回调
const handlePermissionSuccess = () => {
  ElMessage.success(t('system.savePermissionsSuccess'))
}

// 页面加载时获取数据
onMounted(() => {
  fetchRoleList()
})

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

<style lang="scss" scoped>
.role-management-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;

  .breadcrumb {
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .search-bar {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .toolbar {
    margin-bottom: 16px;
    display: flex;
    gap: 10px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    flex-grow: 1;
    overflow-x: auto;

    .el-table {
      border-radius: 4px;
      overflow: hidden;

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }

      td {
        padding: 8px 0;
      }
      .el-button--small {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
  }
  
  .pagination-container {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .pagination-info {
      color: #606266;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style> 
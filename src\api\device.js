import request from '@/utils/request'

// 分页查询设备
export function getDeviceList(params) {
  return request({
    url: '/crm-service/api/devices',
    method: 'get',
    params
  })
}

// 创建设备
export function createDevice(data) {
  return request({
    url: '/crm-service/api/devices',
    method: 'post',
    data
  })
}

// 更新设备
export function updateDevice(id, data) {
  return request({
    url: `/crm-service/api/devices/${id}`,
    method: 'put',
    data
  })
}

// 删除设备
export function deleteDevice(id) {
  return request({
    url: `/crm-service/api/devices/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除设备
 * @param {Array} ids - 设备ID数组
 * @returns {Promise}
 */
export function batchDeleteDevices(ids) {
  return request({
    url: '/crm-service/api/devices/batch',
    method: 'delete',
    data: { ids }
  })
}

// 更新设备状态
export function updateDeviceStatus(id, status) {
  return request({
    url: `/crm-service/api/devices/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 批量更新设备状态
export function batchUpdateDeviceStatus(ids, status) {
  return request({
    url: '/crm-service/api/devices/batch/status',
    method: 'put',
    params: { status },
    data: ids
  })
}

// 检查设备是否可以删除
export function checkDeviceCanDelete(id) {
  return request({
    url: `/crm-service/api/devices/${id}/can-delete`,
    method: 'get'
  })
}

/**
 * 导出设备列表
 * @param {Object} params - 查询参数
 * @param {string} format - 导出格式：'csv' 或 'excel'
 * @returns {Promise}
 */
export function exportDeviceList(params, format = 'excel') {
  return request({
    url: '/crm-service/api/devices/export',
    method: 'get',
    params: { ...params, format },
    responseType: 'blob'
  })
}

// 为了保持向后兼容，保留原有的函数，但内部调用新的统一函数
/**
 * 导出设备列表为CSV
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportDeviceListCSV(params) {
  return exportDeviceList(params, 'csv')
}

/**
 * 导出设备列表为Excel
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportDeviceListExcel(params) {
  return exportDeviceList(params, 'excel')
}

/**
 * 搜索设备
 * @param {string} keyword - 搜索关键词（设备型号、序列号或IMEI）
 * @returns {Promise}
 */
export function searchDevices(keyword) {
  return request({
    url: '/crm-service/api/devices/search',
    method: 'get',
    params: { keyword, available: true }
  })
}

// 获取设备详情
export const getDeviceDetail = (id) => {
  return request({
    url: `/crm-service/api/devices/${id}`,
    method: 'get'
  })
}

// 同步设备
export function syncDevices(enterpriseId) {
  return request({
    url: '/crm-service/api/devices/sync',
    method: 'post',
    params: { enterpriseId }
  })
} 
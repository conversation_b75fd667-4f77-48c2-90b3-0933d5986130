import { acceptHMRUpdate } from "pinia";

export default {
    menus: {
      pureHome: '首页',
      dashboard: '仪表盘',
      device: '设备管理',
      deviceList: '设备列表',
      customer: '客户管理',
      customerList: '客户列表',
      order: '订单管理',
      orderList: '订单列表',
      system: '系统设置'
    },
    common: {
      upload: '上传',
      email: '邮箱',
      day: '天',
      search: '查询',
      reset: '重置',
      confirm: '确定',
      cancel: '取消',
      create: '创建',
      edit: '编辑',
      delete: '删除',
      export: '导出',
      details: '详情',
      total: '共 {count} 条',
      loading: '加载中...',
      all: '全部',
      selectAll: '全选',
      warning: '提示',
      close: '关闭',
      operations: '操作',
      actions: '操作',
      remarks: '备注',
      save: '保存',
      download: '下载',
      detail: '详情',
      createTime: '创建时间',
      updateTime: '更新时间',
      status: '状态',
      selectDateRange: '请选择日期范围',
      selectDate: '选择日期',
      selectTime: '选择时间',
      clear: '清除',
      to: '至',
      expand: '展开',
      collapse: '收起',
      moreOptions: '更多筛选条件',
      dateRange: '日期范围',
      prev: '上一步',
      next: '下一步',
      submit: '提交',
      refresh: '刷新',
      columnSetting: '列设置',
      fetchFailed: '获取数据失败',
      retry: '重试',
      uploading: '上传中...',
      uploadSuccess: '上传成功',
      uploadFailed: '上传失败',
      show: '显示',
      hide: '隐藏',
      noData: '暂无数据',
      noAttachments: '暂无附件',
      downloading: '下载中...',
      exporting: '导出中...',
      exportSuccess: '导出成功',
      more: '更多',
      yes: '是',
      no: '否',
      index: '序号',
      perPage: '条/页',
      goTo: '前往',
      page: '页',
      of: '/',
      prev: '上一步',
      next: '下一步',
      unknown: '未知',
      noOperation: '不操作',
      deleteSuccess: '删除成功',
      operationSuccess: '操作成功',
      operationFailed: '操作失败',
      ok: '确定',
      back: '返回',
      success: '操作成功',
      error: '操作失败',
      info: '提示',
      view: '查看',
      add: '添加',
      formInvalid: '表单填写有误，请检查。',
      search: '搜索',
      import: '导入',
      export: '导出',
      id: 'ID',
      remark: '备注',
      attachment: '附件',
      timeInfo: '时间信息',
      uploadPlaceholder: '点击或拖拽文件到这里上传',
      uploadLimitTip: '最多上传 {limit} 个文件',
      reset: '重置',
      loading: '加载中',
      success: '操作成功',
      error: '操作失败',
      warning: '警告',
      yes: '是',
      no: '否',
      fetchFailed: '获取数据失败',
      remarks: '备注',
      home: '首页',
      creator: '创建人',
      batchOperations: '批量操作',
      pleaseSelect: '请选择',
      uploadInvalidFormat: '上传失败：仅支持JPG、JPEG、PNG、PDF格式文件',
      uploadExceedTip: '上传失败：单个文件大小不能超过5MB',
      fileDownloadFailed: '文件下载失败！',
      startDate: '开始日期',
      endDate: '结束日期',
    },
    device: {
      id: '设备ID',
      basicInfo: '基本信息',
      identificationInfo: '识别信息',
      additionalInfo: '附加信息',
      deviceInfoLabel: '设备信息',
      autoGenerated: '自动生成',
      deviceInfoHint: '根据品牌+型号+型号编号自动生成，例如：Apple iPhone 13 Pro (A2639)',
      deviceInfoPlaceholder: '设备信息将自动生成，无需手动输入',
      timeInfo: '时间信息',
      brand: {
        placeholder: '请输入设备品牌',
        label: '设备品牌',
        Apple: '苹果',
        Samsung: '三星',
        Huawei: '华为',
        Xiaomi: '小米',
        OPPO: 'OPPO',
        vivo: 'vivo',
        other: '其他'
      },
      sn:{
        label: '设备序列号',
        placeholder: '请输入设备序列号',
      },
      model: '型号',
      modelPlaceholder: '请输入设备型号',
      modelNumber: '型号编号',
      modelNumberPlaceholder: '请输入型号编号',
      deviceInfoTip: '根据品牌+型号+型号编号自动生成，例如：Apple iPhone 13 Pro (A2639)',
      color: {
        label: '设备颜色',
        black: '黑色',
        white: '白色',
        silver: '银色',
        gold: '金色',
        blue: '蓝色',
        red: '红色',
        green: '绿色',
        purple: '紫色',
        gray: '灰色',
        brown: '棕色',
        placeholder: '请输入设备颜色'
      },
      status: {
        label: '设备状态',
        IN_STOCK: '在库',
        IN_USE: '使用中',
        UNDER_REPAIR: '维修中',
        IN_TRANSIT: '物流中',
        SCRAPPED: '报废',
        LOST: '丢失',
        PREPARING: '准备中',
        SOLD: '已售'
      },
      serialNumber: '序列号',
      serialNumberPlaceholder: '请输入设备序列号',
      serialNumberTip: '序列号通常可以在设备背面或设置中找到',
      imei1: 'IMEI 1',
      imei2: 'IMEI 2',
      imeiPlaceholder: '请输入有效的 IMEI1（15位数字）',
      imei2Placeholder: '请输入有效的 IMEI2（15位数字）',
      remarks: '备注',
      remarksPlaceholder: '可输入设备的其他信息',
      syncStatus: '同步状态',
      syncTime: '同步时间',
      createTime: '创建时间',
      orderNo: '关联订单号',
      costAmount: '成本金额',
      attachments: '附件',
      uploadText: '拖放文件到此处上传',
      uploadTip: '支持图片(JPG/PNG/JPEG/PDF)格式，单个文件最大5MB',
      uploadLimitExceeded: '上传失败：最多上传5个文件',
      deviceInfo: '设备信息',
      preview: '文件预览',
      notFound: '未找到设备',
      operationFailed: '操作设备失败',
      other: '其他',
      error: {
        snExists: '该序列号已存在，请插入其他序列号',
        imei1Exists: '该IMEI1已存在，请输入其他IMEI1',
        imei2Exists: '该IMEI2已存在，请输入其他IMEI2'
      },
      information: '设备信息',
      pleaseSelect: '请选择设备',
      pleaseSearchOrCreate: '请搜索或创建设备',
      searching: '正在搜索设备...',
      noAttachments: '无附件',
      createSuccess: '设备创建成功',
      searchFailed: '设备搜索失败',
      fetchListFailed: '获取设备列表失败',
      fetchDetailFailed: '获取设备详情失败',
      fetchDetailFailedRetry: '获取设备详情失败，请重试',
      selectDeviceForBatchOperation: '请选择要操作的设备',
      colorInfo: '颜色',
      deviceDetails: '设备详情',
      details: {
        title: '设备详情',
        placeholder: '请输入设备详情'
      },
      search: {
        placeholder: '设备编号/名称/序列号/IMEI',
        status: '设备状态',
        brand: '设备品牌',
        color: '设备颜色',
        syncStatus: '同步状态',
        synced: '已同步',
        pendingSync: '待同步'
      },
      info: {
        title: '设备详情'
      },
      action: {
        create: '创建新设备'
      },
      table: {
        number: '设备信息',
        info: '设备信息',
        sn: '序列号',
        status: '状态'
      },
      export: {
        csv: '导出为CSV',
        excel: '导出为Excel',
        error: '导出失败',
        csvSuccess: 'CSV导出成功',
        excelSuccess: 'Excel导出成功',
        exportSuccess: '设备列表已导出，文件下载成功！',
        emptyListError: '设备列表为空，无法导出。',
        exportFailedError: '设备导出失败，请稍后重试。'
      },
      batchDelete: {
        title: '批量删除',
        dialogTitle: '批量删除设备',
        confirm: '确定要删除选中的 {count} 个设备吗？',
        confirmMessage: '您确定要删除选中的 {count} 台设备吗？',
        irreversibleWarning: '此操作不可撤销！',
        selectedDevicesTitle: '选中的设备：',
        confirmationCheckbox: '我已确认以上信息，并了解部分设备可能因当前状态无法变更（如租赁中的设备）',
        confirmButton: '确认删除',
        success: '设备删除成功',
        error: '设备删除失败，请稍后重试',
        deleteErrorPartial: '部分设备无法删除，请检查设备状态或关联合同情况。'
      },
      delete: {
        confirmSingle: '确定要删除该设备吗？',
        title: '删除设备'
      },
      batchChangeStatus: {
        title: '批量变更状态',
        pleaseSelect: '请选择目标状态',
        dialogTitle: '批量变更设备状态',
        changeToLabel: '变更为',
        targetStatusPlaceholder: '请选择目标状态',
        infoMessage: '您将批量变更 {count} 台设备的状态',
        selectedDevicesTitle: '选中的设备：',
        rentedWarning: '注意：有关联订单的设备（租用中）可能无法变更状态',
        confirmationCheckbox: '注意：有关联订单的设备（使用中）可能无法变更状态',
        confirmButton: '确认变更',
        success: '批量变更状态成功',
        error: '批量变更状态失败'
      },
      rules: {
        brandRequired: '请选择设备品牌',
        modelRequired: '请输入设备型号',
        modelFormat: '请输入有效的8-20位字母、数字或短横线',
        modelNumberRequired: '请输入型号编号',
        colorRequired: '请选择设备颜色',
        statusRequired: '请选择设备状态',
        serialNumberRequired: '请输入设备序列号',
        imei1Required: '请输入IMEI号码',
        imei1Format: '请输入有效的IMEI1（15位数字）',
        imei2Format: '请输入有效的IMEI2（15位数字）',
        imei2Required: '请输入IMEI2号码',
        imei1DuplicateOnImei2: '当前企业中已存在相同的 IMEI2，请检查后重新填写。',
        imei2DuplicateOnImei1: '当前企业中已存在相同的 IMEI1，请检查后重新填写。',
        costAmountFormat: '成本金额必须为数字',
        costAmountRequired: '请输入有效的成本金额（0-9,999,999的整数）',
        costAmountPlaceholder: '请输入有效的成本金额（0-9,999,999的整数）',
        costAmountInvalid: '成本金额必须是0到9999999之间的整数',
        idCardFormatError: '请输入有效的身份证号码',
        passportFormatError: '请输入有效的护照号码',
        driverLicenseFormatError: '请输入有效的驾驶证号码',
        snRequired: '请输入设备序列号',
        snInvalid: '请输入有效的8-20位字母或数字'
      },
      list: {
        brandModel: '设备信息',
        filenamePrefix: '设备列表',
        deviceNumber: '设备编号',
        deviceInfo: '设备信息',
        status: '状态',
        serialNumber: '序列号',
        imei1: 'IMEI1',
        imei2: 'IMEI2',
        syncStatus: '同步状态',
        syncTime: '同步时间',
        createTime: '创建时间',
        orderNo: '关联订单号',
        costAmount: '成本金额',
        operations: '操作'
      },
      buttons: {
        create: '新增设备',
        import: '导入设备',
        export: '导出设备',
        batchDelete: '批量删除',
        reset: '重置'
      },
      deviceName: '型号编号',
      type: {
        label: '设备类型',
        phone: '手机',
        tablet: '平板',
        laptop: '笔记本'
      },
      placeholder: {
        brand: '请选择品牌',
        costAmount: '请输入成本金额',
        model: '请输入设备型号，像12 pro',
        modelNumber: '请输入型号编号，例如 A2639',
        color: '请选择颜色',
        imei1: '请输入 IMEI1',
        imei2: '请输入 IMEI2'
      },
      description: '设备描述',
      accessories: '设备配件',
      createTimeRange: '创建时间范围',
    },
    dialog: {
      warning: '警告',
      confirm: '确认',
      cancel: '取消',
      delete: '删除',
      edit: '编辑',
      add: '新增',
      save: '保存',
      createDevice: '创建设备',
      editDevice: '编辑设备',
      deleteConfirm: '确认删除该设备吗？',
      fileLimit: '最多只能上传5个文件',
      // warning: {
      //   fileLimit: '最多只能上传5个文件'
      // },
      success: {
        create: '创建成功',
        edit: '更新成功',
        delete: '删除成功',
        logout: '退出成功',
        upload: '上传成功'
      },
      error: {
        create: '创建失败',
        edit: '更新失败',
        delete: '删除失败',
        export: '导出失败',
        logout: '退出失败，请稍后重试',
        upload: '上传失败',
        fileType: '不支持的文件类型',
        fileSize: '文件大小不能超过10MB'
      }
    },
    header: {
      language: '语言',
      logout: '退出登录',
      logoutConfirm: '确定要退出登录吗？'
    },
    dashboard: {
      welcome: '欢迎回来，{name}',
      welcomeDesc: '欢迎使用本系统，祝您使用愉快！',
      recentActivity: '最近活动',
      performance: '性能指标',
      selectCompany: '选择企业',
      currentStatistics: '当前数据统计',
      enterpriseTotalStatistics: '企业及子企业合计数据',
      mainCompany: '总公司',
      branchA: '分公司A',
      branchB: '分公司B',
      branchC: '分公司C',
      newOrdersCount: '新增订单数',
      completedOrdersCount: '完成订单数',
      badDebtOrdersCount: '坏账订单数',
      totalOrders: '订单数',
      overdueOrders: '逾期订单数',
      actualReceived: '实收金额',
      accountsReceivable: '应收金额',
      expenditure: '支出金额',
      initialPaymentIncome: '首付收入',
      installmentIncome: '分期收入',
      penaltyIncome: '罚款收入',
      depositReceived: '押金实收',
      initialPaymentReceivable: '首付应收',
      installmentReceivable: '分期应收',
      penaltyReceivable: '罚款应收',
      overdueIncome: '逾期收入',
      depositReceivable: '押金应收',
      deviceCost: '设备成本',
      badDebtAmount: '坏账金额',
      fetchDataFailed: '获取数据失败，请稍后重试',
      pleaseSelectDateRange: '请先选择日期范围再进行搜索',
      resetToDefaultDateRange: '日期范围已重置为最近一个月'
    },
    buttons: {
      pureLoginOut: '退出登录',
      pureAccountSettings: '账户设置',
      pureOpenSystemSet: '打开系统设置',
      pureBackTop: '回到顶部',
      fullScreen: '全屏',
      cancelFullScreen: '取消全屏',
      language: '语言'
    },
    settings: {
      title: '系统设置',
      themeMode: '主题模式',
      themeColor: '主题颜色',
      interfaceSettings: '界面设置',
      fixedHeader: '固定头部',
      showLogo: '显示Logo',
      showTags: '显示标签页',
      showFooter: '显示页脚'
    },
    search: {
      title: '菜单搜索',
      placeholder: '输入关键词搜索',
      noResults: '没有找到相关菜单',
      tip: '可以使用上下键和回车键快速选择'
    },
    notice: {
      title: '消息通知',
      notification: '通知',
      message: '消息',
      todo: '待办',
      total: '共 {count} 条',
      markAllAsRead: '全部已读',
      empty: '暂无消息',
      fetchFailed: '获取消息失败'
    },
    status: {
      pureLoad: '加载中...'
    },
    customer: {
      id: '客户ID',
      customerNo: '客户编号',
      idInfo: '证件信息',
      addressInfo: '地址信息',
      name: '客户姓名',
      email: '邮箱',
      phone: '电话',
      idType: '证件类型',
      idNumber: '证件号码',
      residenceAddress: '居住地址',
      mailingAddress: '通信地址',
      delete:{
        success: '删除成功',
        failed: '删除失败',
        confirm: '确定要删除此客户吗？'
      },
      details: {
        title: '客户详情',
        basicInfo: '基本信息',
        idInfo: '证件信息',
        orderInfo: '订单信息',
        attachments: '附件信息',
        timeInfo: '时间信息',
        noOrders: '客户暂无关联订单',
        noAttachments: '暂无附件'
      },
      batch:{
        noSelection: '请先选择要操作的客户',
        deleteConfirm: '您确定要删除选中的 {count} 个客户吗？此操作不可撤销！',
        deleteSuccess: '批量删除成功',
        deleteFailed: '批量删除失败'
      },
      placeholder: {
        email: '请输入邮箱地址',
        firstName: '请输入客户名',
        lastName: '请输入客户姓',
        idType: '请选择证件类型',
        idNumber: '请输入证件号码',
        phone: '请输入主要电话号码',
        phone2: '请输入备用电话号码',
        licenseAddress: '请输入证件地址',
        contactAddress: '请输入通信地址',
        search: '输入邮箱或电话号码进行搜索'
      },
      rules: {
        emailRequired: '请输入客户邮箱',
        emailFormat: '请输入有效的邮箱地址',
        emailLength: '请输入有效的5-50邮箱地址',
        firstNameRequired: '请输入有效的1-30位姓名',
        lastNameRequired: '请输入有效的1-30位姓氏',
        firstNameLength: '请输入有效的1-30位姓名',
        idTypeRequired: '请选择证件类型',
        idNumberRequired: '请输入有效的证件号码',
        idNumberLength: '证件号码长度必须在6到20位之间',
        idNumberInvalid: '证件号码仅支持包含字母和数字',
        idCardFormatError: '请输入有效的身份证号码',
        passportFormatError: '请输入有效的护照号码',
        driverLicenseFormatError: '请输入有效的驾驶证号码',
        licenseAddressRequired: '请输入有效的1-100位证件地址',
        contactAddressRequired: '请输入有效的1-100位通信地址',
        phoneRequired: '请输入联系电话',
        phone1Required: '请输入客户电话1',
        phone1FormatInvalid: '请输入有效的10-15位电话号码',
        phone2FormatInvalid: '请输入有效的10-15位电话号码',
        emailExists: '该邮箱已被注册，请使用其他邮箱。'
      },  
      idTypes: {
        idCard: '身份证',
        passport: '护照',
        driverLicense: '驾驶证',
        other: '其他证件',
        id_card: '身份证'
      },
      status: {
        linked: '已关联',
        unlinked: '未关联'
      },
      filter: {
        linked: '已关联订单',
        unlinked: '未关联订单',
        normal: '正常',
        overdue: '有逾期',
        overdueRange: '逾期次数范围',
        minValue: '最小值',
        maxValue: '最大值',
        error: {
          minMaxOverdue: '逾期次数最小值不能大于最大值'
        }
      },
      dialog: {
        createCustomer: '创建客户',
        editCustomer: '编辑客户'
      },
      uploadText: '点击或拖拽文件到此处上传',
      uploadTip: '支持jpg、jpeg、png、pdf格式，单个文件不超过5MB',
      search: {
        placeholder: '请输入客户编号/姓名/证件号/手机号',
        idType: '证件类型',
        orderRelation: '订单关联',
        status: '客户状态'
      },
      table: {
        code: '客户编号',
        orderId: '关联订单',
        name: '客户姓名',
        idInfo: '证件信息',
        phone: '联系电话',
        email: '电子邮箱',
        orderStatus: '订单状态',
        overdueCount: '逾期次数',
        createTime: '创建时间',
        updateTime: '最近更新',
        operation: '操作'
      },
      button: {
        add: '新增客户',
        export: '导出',
        batchOperation: '批量操作',
        batchDelete: '批量删除',
        search: '查询',
        reset: '重置',
        save: '保存',
        cancel: '取消'
      },
      export: {
        success: '导出成功',
        error: '导出失败，请重试',
        selected: '导出选中数据',
        all: '导出全部数据',
        csv: '导出CSV',
        excel: '导出Excel',
        selectTip: '请先选择要导出的客户'
      },
      form: {
        firstName: '客户名',
        lastName: '客户姓',
        idType: '证件类型',
        idNumber: '证件号码',
        licenseAddress: '证件地址',
        contactAddress: '通信地址',
        sameAsLicense: '同居住地址',
        phone1: '客户电话1',
        phone2: '客户电话2',
        attachments: '附件',
        uploadTip: '点击或拖拽文件到此处上传',
        uploadDesc: '支持jpg、png、pdf格式，单个文件不超过10MB'
      },
      pagination: {
        total: '共 {total} 条',
        goto: '前往',
        pageClassifier: '页',
        pagesize: ' 条/页'
      },
      information: '客户信息',
      newCustomer: '新建客户',
      notFound: '未找到客户',
      searching: '正在搜索客户...',
      selectTip: '请搜索并选择客户，或创建新客户',
      createSuccess: '客户创建成功',
      createFailed: '客户创建失败',
      searchFailed: '客户搜索失败',
      action: {
        create: '创建客户'
      },
      list: {
        customerNumber: '客户编号',
        customerName: '客户姓名',
        phone: '电话',
        idType: '证件类型',
        idNumber: '证件号码',
        email: '邮箱',
        createTime: '创建时间',
        operations: '操作'
      },
      search: {
        placeholder: '姓名/证件号/联系电话',
        orderRelation: '订单关联',
        status: '客户状态',
        idType: '证件类型'
      },
      basicInfo: '基本信息',
      contactInfo: '联系信息',
      fullName: '客户姓名',
      idType: '证件类型',
      idNumber: '证件号码',
      licenseAddress: '居住地址',
      contactAddress: '通信地址',
      phone: '联系电话',
      email: '客户邮箱'
    },
    order: {
      statusAndProgress: '订单状态与进度',
      financialOverview: '财务概览',
      periodLease: '分期',
      periodInfo: '周期信息',
      orderNo: '订单编号',
      orderType: '订单类型',
      yuan: '元',
      endDate: '到期时间',
      firstPaymentDate: '首付日期',
      currentInstallment: '当前期数',
      management: '订单管理',
      information: '订单信息',
      rentPeriod: '租期',
      months: '个月',
      deviceInfo: '设备信息',
      penaltyFine: '逾期罚款',
      penaltyCalculationMethod: '逾期计算方式',
      penaltyCalculationMethods: {
        daily: '按日计息',
        month: '按月计息',
        weekly: '按周计息'
      },
      list: {
        title: '订单列表',
        orderNo: '订单号',
        orderType: '订单类型',
        firstPaymentDate: '首付日期',
        currentInstallment: '本期',
        orderStatus: '订单状态',
        badDebt: '坏账状态',
        customerName: '客户名称',
        customerPhone: '客户电话',
        deviceInfo: '设备信息',
        serialNumber: '序列号',
        imei: 'IMEI',
        financialStatus: '信贷状态',
        createDate: '创建日期',
        updateDate: '更新日期',
        operations: '操作',
        normal: '正常',
        overdue: '逾期',
        completed: '已完成',
        days: '天',
        yes: '是',
        no: '否',
        installment: '分期',
        rental: '租赁',
        installmentWithTag: '分期',
        rentalWithTag: '租赁',
        of: '/',
        actions: {
          view: '查看',
          delete: '删除',
          edit: '编辑',
          payment: '付款',
          attachment: '附件',
          statusUpdate: '状态更新'
        }
      },
      status: {
        ACTIVE: '进行中',
        COMPLETED: '已完成',
        OVERDUE: '已逾期',
        CANCELLED: '已取消',
        UNKNOWN: '未知状态',
        NORMAL: '正常',
        NOT_STARTED: '未开始',
        DEFAULTED: '已违约',
        BAD_DEBT: '坏账'
      },
      search: {
        placeholder: '请输入订单号/客户姓名/电话/序列号/IMEI'
      },
      create: '创建订单',
      createDate: '创建日期',
      orderStatus: '订单状态',
      paymentStatus: '付款状态',
      nextPaymentAmount: '下次付款金额',
      includeOverdue: '含逾期罚金',
      contractAmount: '合同金额',
      searchPlaceholder: '订单号/客户名称/手机号/设备信息',
      button: {
        search: '查询',
        reset: '重置',
        add: '新建订单',
        export: '导出',
        batchOperation: '批量操作'
      },
      export: {
        excel: '导出Excel',
        csv: '导出CSV',
        success: '文件导出成功，正在下载...',
        error: '导出失败'
      },
      batch: {
        updateStatus: '批量状态更新',
        onlyOverdueForBadDebt: '批量操作失败：仅允许状态为“逾期”的订单标记为坏账。请重新筛选订单',
        delete: '批量删除',
        noSelection: '请先选择要操作的订单',
        deleteConfirm: '确定要删除选中的 {count} 个订单吗？',
        deleteSuccess: '批量删除成功',
        deleteError: '批量删除失败',
        statusUpdateAllSuccess: '批量状态更新成功',
        statusUpdateAllFailed: '批量状态更新失败',
        statusUpdateSuccess: '状态更新成功',
        statusUpdateError: '状态更新失败',
        statusUpdateAllConfirm: '确定要批量更新选中的 {count} 个订单的状态吗？',
        statusUpdateConfirm: '确定要更新此订单的状态吗？',
      },
      delete: {
        title: '删除订单',
        confirmMessage: '确定要删除此订单吗？删除后无法恢复！',
        confirm: '确定要删除此订单吗？删除后无法恢复！',
        cannotDeleteWithTransactions: '该订单包含交易记录，无法删除',
        success: '删除成功',
        error: '删除失败'
      },
      pagination: {
        total: '共 {total} 条记录',
        goto: '前往',
        pageClassifier: '页',
        pagesize: '条/页'
      },
      steps: {
        selectDevice: '选择设备',
        customerInfo: '客户信息',
        orderInfo: '订单信息',
        paymentPlan: '付款计划',
        preview: '预览确认'
      },
      types: {
        rental: '租赁',
        sale: '销售',
        installment: '分期'
      },
      statusInfo: '订单状态',
      basicInfo: '订单详情',
      attachments: '订单附件',
      noAttachments: '暂无附件',
      orderDate: '订单日期',
      financialStatusInfo: '财务状态',
      financialStatus: {
        NORMAL: '正常',
        OVERDUE: '逾期',
        SETTLED: '已结清',
        COMPLETED: '已完成',
        NOT_STARTED: '未开始',
        IN_PROGRESS: '进行中',
        DEFAULTED: '已违约',
        UNKNOWN: '未知'
      },
      financialStatusOptions: {
        NORMAL: '正常',
        OVERDUE: '逾期',
        SETTLED: '已结清',
        UNKNOWN: '未知',
        COMPLETED: '已完成',
        NOT_STARTED: '未开始',
        IN_PROGRESS: '进行中',
        DEFAULTED: '已违约'
      },
      overdueCount: '逾期次数',
      overdueAmount: '逾期金额',
      firstPaymentDate: '首付日期',
      currentInstallment: '当前期数',
      depositStatus: '押金状态',
      nextPaymentDate: '下次付款日期',
      penaltyAmount: '罚款金额',
      penalty: '逾期罚款',
      statusOverview: '订单状态概览',
      progressInfo: '订单进度',
      periodProgress: '付款周期进度',
      completed: '已完成',
      currentPeriodLabel: '当前期数',
      paidAmount: '已付金额',
      remainingAmount: '剩余金额',
      orderHistory: '订单历史记录',
      noHistoryRecords: '暂无历史记录',
      paymentDetails: '支付明细',
      paymentSummary: '支付汇总信息',
      activity: {
        created: '订单创建',
        paymentReceived: '收到付款',
        statusChanged: '状态变更',
        completed: '订单完成',
        cancelled: '订单取消',
        paymentOverdue: '付款逾期',
        paymentReminder: '付款提醒',
        other: '其他操作'
      },
      statusUpdate: '状态更新',
      statusUpdates: {
        title: '状态更新',
        markAsBadDebt: '标记为坏账',
        status: '订单状态',
        badDebtTitle: "确认标记为坏账",
        badDebtConfirm: '标记为坏账后将停止计算罚息，且订单状态将变更为已完成。请确认是否继续？',
        badDebtCheckboxDescription: '勾选后将该订单标记为坏账，状态显示为"坏账"，标记坏账订单将停止计算逾期罚金',
        recalculatePenaltyQuestion: '是否重新补计算标记坏账期间的逾期罚金？',
        onlyOverdueCanMark: '只有逾期状态的订单才能标记为坏账。',
        success: '状态更新成功',
        failed: '状态更新失败',
        batchTitle: '批量变更订单状态',
        confirm: '确定',
        cancel: '取消',
        noAction: '不操作',
        changeStatusTo: "变更状态为",
        confirmUpdate: '确认变更',
        selectedOrdersCount: '您将批量变更{count}笔订单的状态',
        yes: '是',
        no: '否',
        warning: '注意：变更状态、标记坏账或完成操作可能影响订单的计费、支付等相关业务',
        cancelBadDebtTitle: '取消坏账标记'
      },
      markAsBadDebt: '标记为坏账',
      badDebtDescription: '勾选后将标记订单为坏账，状态会变为"坏账"',
      actions: {
        updateStatus: '状态更新',
        attachments: '附件',
        payment: '付款'
      },
      statusUpdateSuccess: '订单状态更新成功',
      statusUpdateFailed: '订单状态更新失败',
      startDate: '开始日期',
      duration: '租期',
      remarks: '备注',
      selectStartDate: '选择开始日期',
      remarksPlaceholder: '请输入订单备注信息',
      orderBasicInfo: '订单基本信息',
      paymentInfo: '付款信息',
      initialPayment: '首付金额',
      periodicPayment: '每期金额',
      numberOfInstallments: '付款期数',
      enterNumberOfInstallments: '请输入付款期数',
      periodicLength: '每期时长',
      enterPeriodicLength: '请输入每期长度',
      day: '天',
      month: '月',
      year: '年',
      week: '周',
      totalAmount: '合计总金额',
      backToList: '返回订单列表',
      otherFees: '其他费用信息',
      penaltyType: '逾期计算',
      penaltyTypes: {
        daily: '按日计算',
        fixed: '固定金额',
        percentage: '按比例计算',
        none: '无违约金'
      },
      selectPenaltyType: '请选择违期计算',
      deposit: '押金',
      totalServiceFee: '总服务费',
      selectFirstPaymentDate: '请选择首付日期',
      rules: {
        typeRequired: '请选择订单类型',
        startDateRequired: '请选择开始日期',
        durationRequired: '请输入租期'
      },
      details: {
        title: '订单详情'
      },
      confirmCancel: '确定要取消创建订单吗？已填写的信息将不会保存。',
      createSuccess: '订单创建成功',
      createError: '订单创建失败',
      buttons: {
        create: '新建订单',
        export: '导出',
        batchDelete: '批量删除'
      },
      batchDelete: {
        confirm: '确定要删除选中的 {count} 个订单吗？',
        success: '批量删除成功',
        error: '批量删除失败'
      },
      dialog: {
        createTitle: '创建订单',
        paymentSummary: '付款摘要',
        totalAmount: '总金额',
        monthlyAmount: '月付金额',
        paymentMethod: '付款方式',
        paymentFrequency: '付款频率',
        monthly: '每月',
        quarterly: '每季度',
        semiannual: '每半年',
        annual: '每年',
        needDeposit: '需要押金',
        depositAmount: '押金金额',
        selectedDevices: '已选择设备',
        items: '项',
        penaltyValue: '违约金额',
        confirmSaveContent: '是否确认保存，生成订单？',
        confirmSave: '确认保存',
        cancelSave: '取消保存',
        confirmSaveTitle: '确认保存并生成订单'
      },
      validation: {
        startDateRequired: '请选择开始日期',
        typeRequired: '请选择订单类型',
        initialPaymentRequired: '请输入首付金额',
        firstPaymentDateRequired: '请选择首付日期',
        periodicPaymentRequired: '请输入每期金额',
        installmentsRequired: '请输入付款期数',
        periodicLengthRequired: '请输入每期长度',
        penaltyTypeRequired: '请选择违约计算方式',
        selectDevice: '请选择设备',
        selectCustomer: '请选择客户',
        amountInvalid: '金额不能为空',
        positiveInteger: '请输入正整数',
        positiveAmount: '金额必须大于0',
        firstPaymentDateInvalid: '首付日期不能早于订单日期',
        depositAmountRequired: '请输入押金金额',
        serviceFeeRequired: '请输入总服务费',
      },
      paymentPlanTitle: '付款计划',
      initialPaymentType: '首付款',
      depositType: '押金',
      installmentType: '分期款',
      installmentPeriod: '第{n}期',
      paymentPending: '待付款',
      paymentPaid: '已付款',
      paymentOverdue: '已逾期',
      totalPaymentAmount: '总应付金额',
      amountLabel: '应付金额',
      serviceFeeLabel: '服务费',
      dueDateLabel: '应付时间',
      statusLabel: '状态',
      paymentTypeLabel: '付款类型',
      serialNumber: '序号',
      installmentNumber: '期数',
      totalLabel: '合计',
      paymentPlanTip: '您可以调整每期分期金额和服务费，系统会自动重新计算订单总金额。调整时请注意：总服务费保持不变。',
      deviceInfoTab: {
        uploadDevicePhoto: '上传设备照片',
        attachmentName: '文件名称',
        attachmentActions: '操作',
        uploadHint: '请上传设备实物图/检测报告等必要文件。',
        imeiExplanationTitle: 'IMEI 说明',
        imeiExplanationText: 'IMEI（国际移动设备识别码）是用于唯一识别移动电话的号码。'
      },
      filter: {
        searchPlaceholder: '订单号/客户名称/手机号/设备信息',
        queryTimeType: '请选择时间类型',
        queryTimeOperator: '请选择时间操作符',
        enterDays: '请输入天数',
        search: '查询',
        reset: '重置',
        expand: '展开',
        collapse: '收起',
        advancedOptions: '高级选项',
        orderID: '订单号',
        customerName: '客户名称',
        orderType: '订单类型',
        all: '全部',
        installment: '分期',
        rental: '租赁',
        orderStatus: '订单状态',
        normal: '正常',
        overdue: '逾期',
        completed: '已完成',
        financialStatus: '信贷状态',
        selectDate: '选择日期',
        dueDate: '到期时间',
        paymentTime: '支付时间',
        overdueTime: '逾期时间',
        badDebtTime: '坏账时间',
        firstPaymentDate: '首付日期',
        last1Month: '近1个月',
        last3Months: '近3个月',
        last6Months: '近6个月',
        last1Year: '近1年',
        customDate: '自定义时间',
        greaterThan: '大于',
        lessThan: '小于',
        equalTo: '等于',
        days: '天',
        dueInWeek: '一周内到期',
        dueInMonth: '一月内到期',
        overdue: '已逾期'
      },
      actions: {
        create: '创建订单',
        createOrder: '创建订单',
        export: '导出',
        exportCSV: '导出CSV',
        exportExcel: '导出Excel',
        batchOperation: '批量操作',
        batchStatusUpdate: '批量更新状态',
        customColumns: '自定义列',
        deleteConfirm: '确定要删除此订单吗？删除后不可恢复！',
        deleteSuccess: '删除成功',
        deleteFailed: '删除失败',
        exportSuccess: '导出成功',
        exportFailed: '导出失败',
        noDataToExport: '没有数据可导出',
        pleaseSelectOrders: '请选择要操作的订单',
        batchUpdateStatus: '批量更新状态'
      },
      create: {
        steps: {
          chooseDevice: '选择设备',
          customerInfo: '客户信息',
          orderInfo: '订单信息',
          paymentPlan: '付款计划',
          previewFinish: '预览确认'
        }
      },
      payment: {
        methods: {
          ONLINE: '线上支付',
          OFFLINE_TRANSFER: '线下转账',
          BANK_TRANSFER: '银行转账',
          CASH: '现金支付',
          POS: 'POS机刷卡',
          WECHAT: '微信支付',
          ALIPAY: '支付宝支付',
          OTHER: '其他方式'
        },
        title: '订单付款',
        total: '合计',
        status: {
          pending: '待付款',
          paid: '已支付',
          overdue: '已逾期',
          latePaid: '逾期支付'
        },
        method: '支付方式',
        submitPayment: '确认付款',
        cancel: '取消',
        success: '付款成功',
        failed: '付款失败',
        selectTransaction: '请选择至少一笔交易进行支付',
        selectMethod: '请选择支付方式',
        amount: '应付金额',
        paidAmount: '实付金额',
        dueDate: '应付时间',
        type: '付款类型',
        remarks: '备注',
        selectOneTransaction: '请选择至少一笔交易',
        unpaidTransactions: '未支付交易列表',
        totalPayable: '总应付金额',
        totalPaid: '总实付金额',
        overdueAmount: '逾期金额',
        selectTransactionRequired: '请选择至少一笔交易进行支付',
        selectMethodRequired: '请选择支付方式',
        remarksPlaceholder: '请输入备注信息，最多256个字符',
        remarksMaxLength: '备注长度不能超过256个字符',
        confirmPayment: '确认付款',
        successMessage: '支付成功',
        failedMessage: '支付失败，请稍后重试',
        systemError: '支付失败，系统异常',
        fetchFailed: '获取交易记录失败',
        noUnpaidTransactions: '暂无待支付的交易记录',
        selectAtLeastOne: '请选择至少一笔交易进行支付'
      },
      statusUpdate: {
        title: '状态更新',
        markAsBadDebt: '标记为坏账',
        status: '订单状态',
        badDebtConfirm: '标记为坏账后将停止计算罚息，且订单状态将变更为已完成。请确认是否继续？',
        cancelBadDebtConfirm: '是否重新补计算标记坏账期间的逾期罚金？',
        onlyOverdueCanMark: '只有逾期订单可以标记坏账',
        success: '状态更新成功',
        failed: '状态更新失败',
        confirm: '确认',
        cancel: '取消',
        yes: '是',
        no: '否',
        warning: '注意：变更状态、标记坏账或完成操作可能影响订单的计费、支付等相关业务。'
      },
      batchUpdate: {
        title: '批量变更订单状态',
        changeStatusTo: '变更状态为',
        markAsBadDebt: '标记为坏账',
        markAsCompleted: '标记为完成操作',
        selectedOrders: '选中的订单',
        confirmBatchUpdate: '我已确认以上信息，并了解状态变更，坏账标记及完成操作可能带来的业务影响',
        batchUpdateWarning: '注意：变更状态、标记坏账或完成操作可能影响订单的计费、支付等相关业务',
        batchUpdateImpact: '修改状态后可能导致计费规则变化，请谨慎操作',
        selectAtLeastOneOption: '请至少选择一个选项',
        confirmBatchUpdateMessage: '确定要更新这 {count} 条订单的状态吗？',
        batchUpdateSuccess: '批量更新订单状态成功',
        batchUpdateFailed: '批量更新订单状态失败',
        list: {
          title: '订单列表',
          orderNo: '订单号',
          customerName: '客户姓名',
          customerPhone: '客户电话',
          status: '状态',
          orderType: '订单类型',
          totalAmount: '订单金额',
          paidAmount: '已付金额',
          createTime: '创建时间',
          createOrder: '创建订单',
          batchUpdateStatus: '批量更新状态',
          updateStatus: '更新状态',
          badDebt: '坏账',
          yes: '是',
          no: '否',
          featureNotImplemented: '功能尚未实现'
        },
        type: {
          rent: '租赁',
          sale: '销售',
          installment: '分期'
        },
        status: {
          active: '正常',
          normal: '正常',
          overdue: '逾期',
          completed: '已完成',
          cancelled: '已取消',
          notStarted: '未开始',
          defaulted: '已违约'
        },
      },
      attachment: {
        title: '附件管理',
        dragHint: '拖放文件到此处或点击上传',
        formatLimit: '支持JPG、PNG、JPEG、PDF格式文件',
        sizeLimit: '单个文件大小不能超过5MB',
        invalidFormat: '上传失败：仅支持JPG、PNG、JPEG、PDF格式文件',
        fileName: '文件名称',
        fileSize: '文件大小',
        fileType: '文件类型',
        uploadDate: '上传日期',
        operations: '操作',
        preview: '预览',
        download: '下载',
        delete: '删除',
        deleteConfirm: '确定要删除此文件吗？删除后不可恢复',
        fetchFailed: '获取附件列表失败',
        uploadSuccess: '文件 {fileName} 上传成功',
        uploadFailed: '文件 {fileName} 上传失败',
        uploadFailedInvalidResponse: '上传失败：无效的服务端响应 ({fileName})',
        deleteSuccess: '附件删除成功',
        deleteFailed: '附件删除失败',
        downloadFailed: '下载链接无效，无法下载',
        previewFailed: '该文件格式不支持预览',
        noOrderId: '订单ID缺失，无法上传附件',
        uploadLimit: '上传失败：最多只能上传5个文件',
        noFilesToSave: '没有待保存的附件',
        saveSuccess: '附件保存成功',
        filesToBeSaved: '待上传附件',
        fileRemoved: '文件 {fileName} 已移除',
        uploadedAttachments: '已上传附件',
        allAttachments: '所有附件',
        invalidLink: '附件链接无效。',
        noPreviewAvailable: '此文件类型暂不支持预览。',
        downloadUrlMissing: '下载链接缺失或无效。'
      },
      penaltyCalculationType: '逾期计算方式',
      selectPenaltyCalculationType: '请选择逾期计算方式',
      penaltyCalculationTypes: {
        daily: '每天',
        weekly: '每周',
        monthly: '每月'
      },
      penaltyAmountType: '违约金类型',
      selectPenaltyAmountType: '请选择违约金类型',
      penaltyAmountTypes: {
        fixed: '固定金额',
        percentage: '百分比金额'
      },
      penaltyValue: '违约金数值',
      enterFixedAmount: '请输入固定金额',
      enterPercentage: '请输入百分比值',
      penaltyCalculationTypeRequired: '请选择逾期计算方式',
      penaltyAmountTypeRequired: '请选择违约金类型',
      penaltyValueRequired: '请输入违约金数值',
      penaltyValueTooLarge: '固定金额不能超过99999',
      percentageTooLarge: '百分比不能超过100%'
    },
    payment: {
      status: {
        paid: '已付款',
        unpaid: '未付款',
        partial: '部分付款'
      }
    },
    dateRange: {
      lastWeek: '最近一周',
      lastMonth: '最近一月',
      lastQuarter: '最近三月',
      lastSixMonths: '最近半年',
      lastYear: '最近一年',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    date: {
      months: [
        '一月', '二月', '三月', '四月', '五月', '六月',
        '七月', '八月', '九月', '十月', '十一月', '十二月'
      ],
      weekDays: [
        '星期日', '星期一', '星期二', '星期三',
        '星期四', '星期五', '星期六'
      ]
    },
    paymentHistory: {
      summary: {
        totalPayable: '应付总金额',
        totalPaid: '实付总金额',
        overdueAmount: '逾期金额',
        nextPaymentAmount: '下次应付金额',
        unpaidPenalty: '未支付罚金'
      },
      filters: {
        typePlaceholder: '交易类型',
        statusPlaceholder: '交易状态',
        paymentDateStart: '支付日期开始',
        paymentDateEnd: '支付日期结束'
      },
      types: {
        INITIAL: '首付款',
        DEPOSIT: '押金',
        INSTALLMENT: '分期付款',
        REFUND: '退款'
      },
      statuses: {
        PAID: '已支付',
        PENDING: '待支付',
        LATE_PAID: '逾期支付',
        OVERDUE: '已逾期',
        REFUNDED: '已退款'
      },
      table: {
        index: '序号',
        transactionDate: '交易日期',
        type: '交易类型',
        installmentNo: '期数',
        payableAmount: '应付金额',
        paidAmount: '实付金额',
        overdueAmount: '逾期金额',
        serviceFee: '服务费',
        status: '状态',
        dueDate: '应付时间',
        paidTime: '支付时间',
        paymentMethod: '支付方式',
        remarks: '备注',
        createdTime: '创建时间'
      },
      actions: {
        makePayment: '付款',
        pay: '支付',
        details: '详情'
      },
      overdueDays: '逾期{days}天',
      noData: '暂无付款记录',
      noPayableTransactions: '没有可支付的交易'
    },
    system: {
      socialAccountDeleteSuccess: '社交媒体账号删除成功',
      socialAccountDeleteFailed: '社交媒体账号删除失败',
      companyInfo: '企业信息',
      basicInfo: '基本信息',
      lastUpdated: '最后更新',
      logoSizeRequirement: 'Logo尺寸建议',
      logoFormatSupport: '支持格式',
      logoSizeLimit: '大小限制',
      uploadLogo: '上传Logo',
      changeLogo: '更换Logo',
      deleteLogo: '删除Logo',
      viewLargerLogo: '查看大图',
      deleteLogoConfirm: '确定要删除企业Logo吗？',
      logoDeleteSuccess: '企业Logo删除成功',
      logoDeleteFailed: '企业Logo删除失败',
      uploadSuccess: '上传成功',
      deleteSuccess: '删除成功',
      operationFailed: '操作失败',
      enabledStatus: '启用',
      disabledStatus: '禁用',
      companyName: '企业名称',
      companyNameRequired: '企业名称不能为空',
      companyNameLengthError: '企业名称长度需要在2-100个字符之间',
      address: '企业地址',
      addressRequired: '企业地址不能为空',
      addressLengthError: '企业地址长度需要在5-200个字符之间',
      phone: '联系电话',
      phoneRequired: '联系电话不能为空',
      phoneFormatError: '联系电话格式不正确，支持手机号或固定电话',
      email: '电子邮箱',
      emailRequired: '电子邮箱不能为空',
      emailFormatError: '电子邮箱格式不正确',
      settlementCurrency: '结算币种',
      currencyRequired: '请选择结算币种',
      currencyCNY: '人民币 (CNY)',
      currencyUSD: '美元 (USD)',
      currencyEUR: '欧元 (EUR)',
      currencyGBP: '英镑 (GBP)',
      socialMedia: '社交媒体账号',
      addSocialAccount: '添加社交媒体账号',
      noSocialAccounts: '暂无社交媒体账号',
      editDialogTitle: '编辑{field}',
      pleaseEnter: '请输入',
      platformType: '平台类型',
      platform: '平台',
      accountName: '账号名称',
      accountUrl: '账号链接',
      accountUrlPlaceholder: '请输入账号链接，如https://example.com',
      wechat: '微信',
      linkedin: '领英 (LinkedIn)',
      facebook: '脸书 (Facebook)',
      twitter: '推特 (Twitter)',
      other: '其他',
      pleaseSelectPlatform: '请选择平台类型',
      pleaseEnterAccountName: '请输入账号名称',
      pleaseEnterAccountUrl: '请输入账号链接',
      fieldRequired: '该字段不能为空',
      invalidUrl: '无效的URL格式',
      platformRequired: '平台类型不能为空',
      urlRequired: '账号链接不能为空',
      logoPreview: 'Logo预览',
      dataProcessingError: '数据处理出错',
      fetchCompanyInfoFailed: '获取企业信息失败',
      confirmDelete: '确认删除',
      confirmDeleteSocialAccount: '确认删除该社交媒体账号吗？',
      socialAccountAddSuccess: '添加社交媒体账号成功',
      socialAccountAddFailed: '添加社交媒体账号失败',
      updateSuccess: '更新成功',
      updateFailed: '更新失败',
      deleteRoleConfirm: '确定要删除该角色吗？',
      roleManagement: '角色管理',
      roleList: '角色列表',
      roleName: '角色名称',
      pleaseEnterRoleName: '请输入角色名称',
      remark: '备注',
      pleaseEnterRemark: '请输入备注信息',
      addRole: '添加角色',
      editRole: '编辑角色',
      deleteRole: '删除角色',
      deleteRoleConfirm: '确定要删除该角色吗？',
      roleNameRequired: '角色名称不能为空',
      roleNameLength: '角色名称长度应在2-50个字符之间',
      remarkLength: '备注信息不能超过100个字符',
      createSuccess: '创建成功',
      deleteFailed: '删除失败',
      noPermission: '无操作权限',
      permissionManagement: '权限管理',
      assignPermissions: '分配权限',
      permissionList: '权限列表',
      permissionName: '权限名称',
      permissionCode: '权限编码',
      permissionDesc: '权限描述',
      permissionCategory: '权限类别',
      selectPermissions: '选择权限',
      allPermissions: '全部权限',
      loadingPermissions: '正在加载权限数据...',
      noPermissionData: '暂无权限数据',
      savePermissions: '保存权限设置',
      savePermissionsSuccess: '保存权限成功',
      savePermissionsFailed: '保存权限失败',
      systemManagement: '系统管理',
      dataManagement: '数据管理',
      userManagement: '用户管理',
      orderManagement: '订单管理',
      customerManagement: '客户管理',
      deviceManagement: '设备管理',
      viewPermission: '查看权限',
      createPermission: '创建权限',
      editPermission: '编辑权限',
      deletePermission: '删除权限',
      approvePermission: '审批权限',
      exportPermission: '导出权限',
      importPermission: '导入权限',
      searchRole: '搜索角色',
      roleDetail: '角色详情',
      rolePermission: '角色权限',
      tableOperation: '表格操作',
      createTime: '创建时间',
      updateTime: '更新时间',
      creator: '创建人',
      updater: '更新人',
      dataEmpty: '数据为空',
      searchResult: '搜索结果',
      totalItems: '共 {total} 条',
      currentPage: '当前页：{current}',
      totalPages: '总页数：{total}',
      accountManagement: '账号管理',
      accountList: '账号列表',
      addAccount: '添加账号',
      editAccount: '编辑账号',
      deleteAccount: '删除账号',
      resetPassword: '重置密码',
      username: '用户名',
      password: '密码',
      confirmPassword: '确认密码',
      passwordValidity: '密码有效期',
      phoneNumber: '手机号码',
      accountType: '账号类型',
      status: '状态',
      active: '启用',
      disabled: '禁用',
      trial: '试用',
      formal: '正式',
      deleteAccountConfirm: '确定要删除该账号吗？删除后不可恢复！',
      resetPasswordConfirm: '确定要重置该账号密码吗？',
      accountCreatedSuccess: '账号创建成功',
      accountCreatedFailed: '账号创建失败',
      accountUpdatedSuccess: '账号更新成功',
      accountUpdatedFailed: '账号更新失败',
      accountDeletedSuccess: '账号删除成功',
      accountDeletedFailed: '账号删除失败',
      passwordResetSuccess: '密码重置邮件已发送',
      passwordResetFailed: '密码重置邮件发送失败',
      statusUpdatedSuccess: '状态更新成功',
      statusUpdatedFailed: '状态更新失败',
      usernameRequired: '请输入用户名',
      emailRequired: '请输入邮箱',
      emailFormat: '邮箱格式不正确',
      passwordRequired: '请输入密码',
      confirmPasswordRequired: '请确认密码',
      passwordNotMatch: '两次输入的密码不一致',
      phoneRequired: '请输入手机号码',
      phoneFormat: '手机号码格式不正确',
      roleRequired: '请选择角色',
      accountTypeRequired: '请选择账号类型',
      statusRequired: '请选择状态',
      validityRequired: '请选择密码有效期',
      passwordPolicy: '密码需包含字母、数字，至少8位',
      usernameUnmodifiable: '用户名一旦创建不可修改',
      newPassword: '新密码',
      inputNewPassword: '请输入新密码',
      days: '天',
      searchPlaceholder: '搜索用户名或手机号',
      allStatus: '所有状态',
      allTypes: '所有类型',
      enableAccount: '启用账号',
      disableAccount: '禁用账号',
      enableAccountSuccess: '账号已启用',
      disableAccountSuccess: '账号已禁用',
      usernameLength: '用户名长度应在3-50个字符之间',
      accountSettings: '账号设置',
      currencyCNY: '人民币 (CNY)',
      currencyUSD: '美元 (USD)',
      currencyEUR: '欧元 (EUR)',
      currencyGBP: '英镑 (GBP)',
      accountUrlPlaceholder: '请输入账号链接，如：https://',
      editCompanyName: '编辑企业名称',
      editAddress: '编辑企业地址',
      editPhone: '编辑联系电话',
      editEmail: '编辑邮箱',
      editCurrency: '编辑结算币种'
    },
    dictionary: {
      title: '字典管理',
      addField: '新增字段',
      editField: '编辑字段',
      moduleOrder: '订单管理',
      moduleCustomer: '客户管理',
      moduleDevice: '设备管理',
      moduleCompany: '企业管理',
      modulePayment: '付款管理',
      noField: '暂无自定义字段',
      addOption: '添加选项',
      optionPlaceholder: '请输入选项',
      options: '字段选项',
      fieldName: '字段名称',
      fieldNamePlaceholder: '请输入字段名称',
      fieldNameRequired: '字段名称不能为空，且长度应在2-50个字符之间。',
      fieldType: '字段类型',
      fieldTypeRequired: '请选择字段类型。',
      fieldCode: '字段编码',
      fieldCodePlaceholder: '请输入字段编码',
      fieldCodeRequired: '字段编码不能为空，且长度应在2-50个字符之间。',
      source: '来源',
      required: '是否必填',
      requiredRequired: '请选择是否必填项。',
      optionsTip: '仅当字段类型为下拉选择（Dropdown）或多选（Checkbox）时，需要输入可供用户选择的选项值。',
      optionsRequired: '请至少添加一个有效选项，每个选项长度在2-30字符之间。',
      optionPlaceholder: '请输入选项',
      addOption: '添加选项',
      defaultValue: '默认值',
      defaultValuePlaceholder: '请输入默认值',
      defaultValueInvalid: '默认值格式不正确。对于下拉选择和多选，请输入选项中的一个值。对于日期，请使用YYYY-MM-DD格式。',
      status: '字段状态',
      statusRequired: '请选择字段状态。',
      enableConfirm: '确定要启用该字段吗？启用后将在前端表单中显示。',
      disableConfirm: '确定要禁用该字段吗？禁用后将在前端表单中隐藏，但仍保留在后台。',
      deleteConfirm: '确定要删除该字段吗？删除后不可恢复！',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      editNotAllowed: '字段类型不可修改',
      usedFieldDeleteNotAllowed: '已使用的字段不可删除，但可选择禁用。',
      fieldTypeText: '文本',
      fieldTypeNumber: '数字',
      fieldTypeDate: '日期',
      fieldTypeDropdown: '下拉选择',
      fieldTypeCheckbox: '多选',
      yes: '是',
      no: '否',
      new: '新建',
      system: '系统默认',
      save: '保存',
      cancel: '取消',
      confirm: '确认',
      confirmDelete: '确认删除',
      enable: '启用',
      disable: '禁用',
      fieldUsed: '字段已被使用，无法删除。',
      fieldNotUsed: '字段未被使用，可以删除。',
      fieldStatusEnabled: '启用',
      fieldStatusDisabled: '禁用',
      fieldStatus: '字段状态',
      fieldRequired: '必填',
      fieldNotRequired: '非必填',
      fieldSourceNew: '新建',
      fieldSourceSystem: '系统默认',
      fieldLengthLimit: '长度应在{min}-{max}个字符之间',
      fieldOptionLengthLimit: '每个选项长度在2-30字符之间',
      maxOptions: '最多允许添加50个选项',
      minOptions: '请至少添加一个有效选项',
      optionDuplicate: '选项值不能重复',
      fieldCodeDuplicate: '字段编码不能重复',
      fieldNameDuplicate: '字段名称不能重复',
      fieldCodeRule: '只能输入中英文字符、数字和常规符号（如 -、_）',
      fieldNameRule: '只能输入中英文字符、数字和常规符号（如 -、_）',
      fieldDefaultValueRule: '默认值类型必须符合字段类型要求',
      fieldDateFormat: 'YYYY-MM-DD',
      fieldNumberLimit: '小于等于9位整数',
      fieldTextLimit: '最多50字符',
      fieldOptionTip: '选项值',
      fieldDefaultValueTip: '默认值',
      fieldStatusTip: '状态',
      fieldOperation: '操作',
      fieldEdit: '编辑',
      fieldDelete: '删除',
      fieldSave: '保存',
      fieldCancel: '取消',
      fieldConfirm: '确认',
      fieldAdd: '新增字段',
      fieldEditTitle: '编辑字段',
      fieldAddTitle: '新增字段',
      fieldUsedTip: '字段已被使用，无法删除。',
      fieldNotUsedTip: '字段未被使用，可以删除。',
      fieldEnableTip: '启用后将在前端表单中显示',
      fieldDisableTip: '禁用后将在前端表单中隐藏，但仍保留在后台',
      fieldDeleteTip: '删除后不可恢复！',
      fieldConfirmDelete: '确认删除',
      fieldConfirmEnable: '确认启用',
      fieldConfirmDisable: '确认禁用',
      fieldSaveSuccess: '保存成功',
      fieldSaveFailed: '保存失败',
      fieldEditSuccess: '编辑成功',
      fieldEditFailed: '编辑失败',
      fieldAddSuccess: '新增成功',
      fieldAddFailed: '新增失败',
      fieldStatusChangeSuccess: '状态切换成功',
      fieldStatusChangeFailed: '状态切换失败',
      fieldDefaultValueNotInOptions: '默认值不在选项中',
      fieldDefaultValueAutoSet: '未设置默认值，已自动设置为第一个选项。',
    },
    orderDialog: {
      preview: {
        statusPendingConfirmation: '待确认',
        remarksPlaceholder: '请输入备注信息',
        imei1Label: 'IMEI'
      }
    }
  };
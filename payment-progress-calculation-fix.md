# 付款周期进度计算修复

## 问题描述
付款周期进度的计算公式不正确，当前使用的是 `当前期数 / 总期数`，但应该使用 `(当前期数 - 1) / 总期数`。

## 问题示例
- **当前计算**：第1期时显示 1/1 = 100%（错误）
- **正确计算**：第1期时显示 (1-1)/1 = 0%（正确）

## 修复的文件

### 1. src/components/order/tabs/OrderStatusTab.vue
**修复前**：
```javascript
const calculateProgressPercentage = computed(() => {
  // ...
  const currentInstallment = props.orderInfo.currentInstallment || 0;
  const totalInstallments = props.orderInfo.totalInstallments || 1;
  
  if (totalInstallments === 0) return 0;
  
  return Math.floor((currentInstallment / totalInstallments) * 100);
});
```

**修复后**：
```javascript
const calculateProgressPercentage = computed(() => {
  // ...
  const currentInstallment = props.orderInfo.currentInstallment || 0;
  const totalInstallments = props.orderInfo.totalInstallments || 1;
  
  if (totalInstallments === 0) return 0;
  
  // 修正计算公式：(当前期数 - 1) / 总期数
  // 这样第1期时进度为0%，第2期时进度为1/总期数，最后一期时进度为100%
  const progressRatio = Math.max(0, currentInstallment - 1) / totalInstallments;
  return Math.floor(progressRatio * 100);
});
```

### 2. src/components/order/OrderDetailDialog.vue
**修复前**：
```javascript
// 计算订单状态进度百分比
if (orderInfo.numberOfInstallments > 0 || orderInfo.installments > 0) {
  const currentInstallment = orderInfo.currentInstallment || orderInfo.currentPeriod || 0;
  orderInfo.totalInstallments = orderInfo.numberOfInstallments || orderInfo.installments;
  orderInfo.progressPercentage = Math.floor((currentInstallment / orderInfo.totalInstallments) * 100);
} else {
  orderInfo.progressPercentage = 0;
}
```

**修复后**：
```javascript
// 计算订单状态进度百分比
if (orderInfo.numberOfInstallments > 0 || orderInfo.installments > 0) {
  const currentInstallment = orderInfo.currentInstallment || orderInfo.currentPeriod || 0;
  orderInfo.totalInstallments = orderInfo.numberOfInstallments || orderInfo.installments;
  // 修正计算公式：(当前期数 - 1) / 总期数
  const progressRatio = Math.max(0, currentInstallment - 1) / orderInfo.totalInstallments;
  orderInfo.progressPercentage = Math.floor(progressRatio * 100);
} else {
  orderInfo.progressPercentage = 0;
}
```

## 计算逻辑说明

### 新的计算公式
```javascript
const progressRatio = Math.max(0, currentInstallment - 1) / totalInstallments;
const percentage = Math.floor(progressRatio * 100);
```

### 计算示例

#### 总期数为12期的情况：
- **第1期**：(1-1)/12 = 0/12 = 0%
- **第2期**：(2-1)/12 = 1/12 ≈ 8%
- **第6期**：(6-1)/12 = 5/12 ≈ 41%
- **第12期**：(12-1)/12 = 11/12 ≈ 91%

#### 总期数为1期的情况：
- **第1期**：(1-1)/1 = 0/1 = 0%

### 边界情况处理
- 使用 `Math.max(0, currentInstallment - 1)` 确保进度不会为负数
- 当 `totalInstallments` 为0时，直接返回0%
- 订单完成或已结清时，强制显示100%

## 业务逻辑解释

### 为什么要用 (当前期数 - 1) / 总期数？

1. **第1期开始时**：客户还没有完成任何付款，进度应该是0%
2. **第2期开始时**：客户已经完成了1期付款，进度应该是1/总期数
3. **最后一期开始时**：客户已经完成了(总期数-1)期付款，进度应该是(总期数-1)/总期数

这样的计算方式更符合实际的付款进度逻辑。

## 特殊情况处理

### 订单完成状态
当订单状态为以下情况时，强制显示100%进度：
- `orderStatus === 'completed'`
- `financialStatus === 'settled'`
- `financialStatus === '已结清'`

### 数据异常处理
- 当前期数为0或负数时，进度显示为0%
- 总期数为0时，进度显示为0%
- 当前期数大于总期数时，使用 `Math.max(0, currentInstallment - 1)` 确保计算正确

## 测试验证

### 测试用例
1. **1期订单，当前第1期**：应显示0%
2. **12期订单，当前第1期**：应显示0%
3. **12期订单，当前第2期**：应显示8%
4. **12期订单，当前第6期**：应显示41%
5. **12期订单，当前第12期**：应显示91%
6. **已完成订单**：应显示100%

### 验证方法
1. 在订单详情页面查看"付款周期进度"
2. 确认进度条显示的百分比与计算公式一致
3. 确认进度文本显示正确的百分比

## 影响范围
- 订单详情页面的付款周期进度显示
- 订单状态标签页的进度计算
- 所有使用订单进度计算的相关组件

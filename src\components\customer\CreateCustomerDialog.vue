<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
    align-center
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="customer-form"
    >
      <el-form-item :label="t('customer.email')" prop="email">
        <el-input v-model="form.email" :placeholder="t('customer.placeholder.email')"  maxlength="50" minlength="5"/>
      </el-form-item>

      <el-form-item :label="t('customer.form.firstName')" prop="firstName" style="margin-bottom: 18px">
        <el-col :span="10">
          <el-input v-model="form.firstName" :placeholder="t('customer.placeholder.firstName')" maxlength="30" minlength="1"  @input="handleNameInput('firstName')" />
        </el-col>
        <el-col :span="4" style="text-align: center; padding: 0 8px">
          <span style="color: red; margin-right: 4px;">*</span>{{ t('customer.form.lastName') }}
        </el-col>
        <el-col :span="10">
          <el-form-item prop="lastName" style="margin-bottom: 0;">
            <el-input v-model="form.lastName" :placeholder="t('customer.placeholder.lastName')" maxlength="30" minlength="1" @input="handleNameInput('lastName')" />
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item :label="t('customer.form.idType')" prop="idType" style="margin-bottom: 18px">
        <el-col :span="10">
          <el-select v-model="form.idType" :placeholder="t('customer.placeholder.idType')" style="width: 100%">
            <el-option
              v-for="item in idTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4" style="text-align: center; padding: 0 8px">
          <span style="color: red; margin-right: 4px;">*</span>{{ t('customer.form.idNumber') }}
        </el-col>
        <el-col :span="10">
          <el-form-item prop="idNumber" style="margin-bottom: 0;">
            <el-input v-model="form.idNumber" :placeholder="t('customer.placeholder.idNumber')" minlength="6" maxlength="20" />
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item :label="t('customer.form.licenseAddress')" prop="licenseAddress">
        <el-input v-model="form.licenseAddress" :placeholder="t('customer.placeholder.licenseAddress')" maxlength="100" />
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="sameAsLicense" style="margin-left: 0">{{ t('customer.form.sameAsLicense') }}</el-checkbox>
      </el-form-item>

      <el-form-item :label="t('customer.form.contactAddress')" prop="contactAddress">
        <el-input v-model="form.contactAddress" :placeholder="t('customer.placeholder.contactAddress')" maxlength="100" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('customer.form.phone1')" prop="phone1">
            <el-input
              v-model="form.phone1"
              :placeholder="t('customer.placeholder.phone')"
              @input="form.phone1 = form.phone1.replace(/[^0-9+ ]/g, '')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('customer.form.phone2')" prop="phone2">
            <el-input
              v-model="form.phone2"
              :placeholder="t('customer.placeholder.phone2')"
              @input="form.phone2 = form.phone2.replace(/[^0-9+ ]/g, '')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item :label="t('customer.form.attachments')">
        <el-upload
          class="upload-area"
          drag
          :action="null"
          accept=".jpg,.jpeg,.png,.pdf"
          :auto-upload="true"
          :http-request="handleCustomerAttachmentUpload"
          v-model:file-list="fileList"
          :on-remove="handleElUploadRemove"
          :before-upload="handleBeforeUpload"
          multiple
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            {{ t('customer.uploadText') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">{{ t('customer.uploadTip') }}</div>
          </template>
          <template #file="{ file }">
            <div class="el-upload-list__item" :class="{ 'is-success': file.status === 'success', 'is-ready': file.status === 'ready', 'is-uploading': file.status === 'uploading', 'is-fail': file.status === 'fail' }">
              <a class="el-upload-list__item-name" @click="handlePreview(file)">
                <el-icon><Document /></el-icon>
                <span style="margin-left: 5px;">{{ file.name }}</span>
              </a>
              <label class="el-upload-list__item-status-label">
                <el-icon v-if="file.status === 'success'" class="el-icon--upload-success el-icon--check"><Check /></el-icon>
                <el-icon v-else-if="file.status === 'fail'" class="el-icon--upload-fail el-icon--close"><Close /></el-icon>
              </label>
              <el-icon class="el-icon--close" @click="triggerRemoveFile(file)"><Close /></el-icon>
              <el-progress
                v-if="file.status === 'uploading' && file.percentage"
                type="line"
                :stroke-width="2"
                :percentage="file.percentage"
                style="margin-top: 5px"
              />
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="uploading">{{ t('common.save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElDialog } from 'element-plus'
import { UploadFilled, Document, Check, Close, View } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { createCustomer, updateCustomer } from '@/api/customer'
import { uploadFile } from '@/api/upload'
import { getDictFields } from '@/api/dictionary'

const { t } = useI18n()

const props = defineProps({
  visible: Boolean,
  customerData: {
    type: Object,
    default: () => null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const visible = ref(props.visible)
const formRef = ref(null)
const loading = ref(false)
const uploading = ref(false)
const sameAsLicense = ref(false)
const fileList = ref([])
const idTypeOptions = ref([])
const isEdit = computed(() => !!(props.customerData && props.customerData.id))

const dialogTitle = computed(() => {
  return isEdit.value ? t('customer.dialog.editCustomer') : t('customer.dialog.createCustomer')
})

const previewDialogVisible = ref(false)
const previewImageUrl = ref('')
const previewFileUrl = ref('')

const serverErrorMessages = reactive({})

// 映射后端错误字段到前端表单字段
const backendErrorFieldMap = {
  phone: 'phone1',
  email: 'email',
  // 如果后端对 firstName 或 lastName 有单独的错误字段，可以在这里添加映射
  // firstName: 'firstName',
  // lastName: 'lastName',
};

const form = reactive({
  customerNo: '',
  email: '',
  firstName: '',
  lastName: '',
  idType: '',
  idNumber: '',
  licenseAddress: '',
  contactAddress: '',
  phone1: '',
  phone2: '',
  attachments: []
})

watch(() => form.phone1, () => {
  if (serverErrorMessages.phone1) {
    delete serverErrorMessages.phone1;
    formRef.value?.clearValidate('phone1');
  }
});

// 当用户开始编辑邮箱时，清除后端返回的错误
watch(() => form.email, () => {
  if (serverErrorMessages.email) {
    delete serverErrorMessages.email;
    formRef.value?.clearValidate('email');
  }
});

const handleNameInput = (fieldName) => {
  // 移除非字母、数字、汉字、空格和连字符的字符
  const nameRegex = /[^a-zA-Z0-9\u4e00-\u9fa5\s-]/g;
  if (form[fieldName]) {
    form[fieldName] = form[fieldName].replace(nameRegex, '');
  }
};

const rules = {
  email: [
    { required: true, message: t('customer.rules.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('customer.rules.emailFormat'), trigger: 'blur' },
    { min: 5, max: 50, message: t('customer.rules.emailLength'), trigger: 'blur' }
  ],
  firstName: [
    { required: true, min: 1, max: 30, message: t('customer.rules.firstNameRequired'), trigger: 'blur' }
  ],
  lastName: [
    { required: true, min: 1, max: 30, message: t('customer.rules.lastNameRequired'), trigger: 'blur' }
  ],
  idType: [{ required: true, message: t('customer.rules.idTypeRequired'), trigger: 'change' }],
  idNumber: [
    { required: true, message: t('customer.rules.idNumberRequired'), trigger: 'blur' },
    { min: 6, max: 20, message: t('customer.rules.idNumberLength'), trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9]*$/,
      message: t('customer.rules.idNumberInvalid'),
      trigger: 'change'
    }
  ],
  licenseAddress: [
    { required: true, min: 1, max: 100, message: t('customer.rules.licenseAddressRequired'), trigger: 'blur' }
  ],
  contactAddress: [{ required: true, message: t('customer.rules.contactAddressRequired'), trigger: 'blur' }],
  phone1: [
    { required: true, message: t('customer.rules.phone1Required'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        console.log('phone1 validator called with value:', value);
        const internationalPhoneRegex = /^\+?\d{10,15}$/;
        if (!value) {
          callback();
        } else if (!internationalPhoneRegex.test(value)) {
          callback(new Error(t('customer.rules.phone1FormatInvalid')));
        } else if (serverErrorMessages.phone1) {
          callback(new Error(serverErrorMessages.phone1));
        }
        else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  phone2: [
    {
      validator: (rule, value, callback) => {
        const internationalPhoneRegex = /^\+?\d{10,15}$/;
        if (!value) {
          callback();
        } else if (!internationalPhoneRegex.test(value)) {
          callback(new Error(t('customer.rules.phone1FormatInvalid')));
        } else if (serverErrorMessages.phone2) {
          callback(new Error(serverErrorMessages.phone2));
        }
        else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
}

const initFormData = () => {
  Object.assign(form, {
    customerNo: '', email: '', firstName: '', lastName: '', idType: '', idNumber: '',
    licenseAddress: '', contactAddress: '', phone1: '', phone2: '', attachments: []
  })
  sameAsLicense.value = false
  fileList.value = []
  Object.keys(serverErrorMessages).forEach(key => delete serverErrorMessages[key]);
}

const fillFormData = (data) => {
  if (!data) return
  try {
    const fullName = data.name || ''
    let lastNameVal = '', firstNameVal = ''
    console.log('fullName:', fullName)
    firstNameVal = fullName.split('_')[1]
    lastNameVal = fullName.split('_')[0]

    Object.assign(form, {
      customerNo: data.customerNo || '',
      email: data.email || '',
      firstName: firstNameVal,
      lastName: lastNameVal,
      idType: data.idType || '',
      idNumber: data.idNumber || '',
      licenseAddress: data.residenceAddress || '',
      contactAddress: data.mailingAddress || '',
      phone1: data.phone || '',
      phone2: data.phone2 || '',
      attachments: []
    })

    if (data.attachmentsToDisplay && Array.isArray(data.attachmentsToDisplay)) {
      fileList.value = data.attachmentsToDisplay.map(att => ({
        name: att.name,
        url: att.url,
        uid: att.uid || Date.now() + Math.random(),
        status: 'success',
        raw: att.raw || { id: att.original_id, fileName: att.name, fileUrl: att.url } 
      }));
      form.attachments = fileList.value.map(f => ({ fileName: f.name, fileUrl: f.url, id: f.raw?.id }));

    } else {
      fileList.value = []
      form.attachments = []
    }
     console.log('Filled form attachments:', JSON.parse(JSON.stringify(form.attachments)));
     console.log('Filled fileList for upload:', JSON.parse(JSON.stringify(fileList.value)));
     Object.keys(serverErrorMessages).forEach(key => delete serverErrorMessages[key]);

  } catch (error) {
    console.error('处理客户数据时出错：', error)
  }
}

watch(() => props.visible, async (val) => {
  visible.value = val
})

onMounted(async () => {
  try {
    const response = await getDictFields({
      module: 'CUSTOMER',
      enabled: true
    });
    if (response.data && response.data.records) {
      const idTypeField = response.data.records.find(field => field.fieldCode === 'id_type');
      if (idTypeField && idTypeField.optionList) {
        idTypeOptions.value = idTypeField.optionList;
      }
    }
  } catch (error) {
    console.error('Failed to fetch custom fields for Customer module:', error);
  }
});


watch(() => props.customerData, (val) => {
  if (props.visible && val) {
    fillFormData(val)
  } else if (props.visible && !val) {
    initFormData()
  }
}, { immediate: true, deep: true })

watch(() => sameAsLicense.value, (val) => {
  if (val) {
    form.contactAddress = form.licenseAddress
  }
})

const isImage = (url) => {
  if (!url) return false;
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
  const extension = url.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
}

const handlePreview = (file) => {
  previewFileUrl.value = file.url || '';
  if (isImage(file.url)) {
    previewImageUrl.value = file.url || '';
  } else {
    previewImageUrl.value = '';
  }
  previewDialogVisible.value = true;
}

const triggerRemoveFile = (fileToRemove) => {
  const index = fileList.value.findIndex(f => f.uid === fileToRemove.uid);
  if (index !== -1) {
    const removedFile = fileList.value.splice(index, 1)[0];
    console.log('Removed file:', removedFile);
    
    if (removedFile.status === 'success' && removedFile.raw.url) {
      const attachmentIndex = form.attachments.findIndex(att => att.fileUrl === removedFile.raw.url);
      if (attachmentIndex !== -1) {
        form.attachments.splice(attachmentIndex, 1);
      }
    }
  }
};

const handleElUploadRemove = (file, /* uploadFiles */ ) => {
  console.log('Element Plus on-remove called for:', file.name, file.status, file.url);
  if (file.status === 'success' && file.url) {
    const attachmentIndex = form.attachments.findIndex(att => att.fileUrl === file.url);
    if (attachmentIndex !== -1) {
      form.attachments.splice(attachmentIndex, 1);
      console.log(`Removed from form.attachments: ${file.name}`);
    }
  }
};

const handleClose = () => {
  formRef.value?.resetFields()
  initFormData()
  emit('update:visible', false)
}

const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    loading.value = true;

    // Clear previous server errors before new submission
    Object.keys(serverErrorMessages).forEach(key => delete serverErrorMessages[key]);
    formRef.value.clearValidate();

    const submitData = {
      name: form.lastName && form.firstName ? `${form.lastName}_${form.firstName}` : form.firstName,
      idType: form.idType,
      idNumber: form.idNumber,
      phone: form.phone1,
      email: form.email,
      residenceAddress: form.licenseAddress,
      mailingAddress: form.contactAddress,
      attachments: form.attachments,
      phone2: form.phone2
    };
    if (isEdit.value && props.customerData && props.customerData.id) {
       submitData.customerNo = form.customerNo;
    }

    console.log('Submitting final data with attachments:', JSON.parse(JSON.stringify(submitData)));

    let res;
    if (isEdit.value && props.customerData && props.customerData.id) {
      res = await updateCustomer(props.customerData.id, submitData);
    } else {
      res = await createCustomer(submitData);
    }

    if (res.code === 1000) {
      ElMessage.success(t(isEdit.value ? 'dialog.success.edit' : 'dialog.success.create'));
      emit('success', res.data);
      handleClose();
    } else {
      console.log('API response is not success (res.code !== 1000):', res);
      let handledByFieldError = false;
      if (res.data && res.data.errorField) {
        const targetFormField = backendErrorFieldMap[res.data.errorField];
        console.log('Potential errorField detected. backendErrorField:', res.data.errorField, 'mapped to targetFormField:', targetFormField);
        if (targetFormField) {
          serverErrorMessages[targetFormField] = res.message;
          formRef.value.validateField(targetFormField);
          handledByFieldError = true;
        }
      }
      console.log('handledByFieldError:', handledByFieldError);
      // 只有当没有字段特定的错误被处理时，才显示通用错误消息
      if (!handledByFieldError) {
        ElMessage.error(res.message || t(isEdit.value ? 'dialog.error.edit' : 'dialog.error.create'));
      }
      // 不再抛出错误，因为我们已经处理了消息显示
      // throw new Error(res.message || t(isEdit.value ? 'dialog.error.edit' : 'dialog.error.create'));
    }
  } catch (errorInfo) {
    // 如果是表单校验失败，则不需要额外处理，因为 Element Plus 会显示校验信息
    if (errorInfo instanceof Error) {
      // 对于非表单字段特定错误，或者后端没有提供 errorField 的情况，可以在这里处理
      console.error(isEdit.value ? '更新客户失败：' : '创建客户失败：', errorInfo);
      // ElMessage.error(errorInfo.message);
    } else {
      console.log('表单校验失败:', errorInfo);
    }
  } finally {
    loading.value = false;
  }
}

const handleBeforeUpload = (rawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const isValidType = allowedTypes.includes(rawFile.type);
  if (!isValidType) {
    ElMessage.error(t('common.uploadInvalidFormat', '不支持的文件格式！请上传 JPG, PNG, 或 PDF 文件。'));
    return false;
  }

  const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
  const isLt5M = rawFile.size < maxSizeInBytes;
  if (!isLt5M) {
    ElMessage.error(t('common.uploadSizeExceeds5MB', '上传文件大小不能超过 5MB！'));
    return false;
  }
  
  return true;
};

const handleCustomerAttachmentUpload = async (options) => {
  const { file, onSuccess, onError } = options;
  uploading.value = true;

  try {
    const uploadRes = await uploadFile(file, 'customer');

    if (uploadRes.code === 1000 && uploadRes.data) {
      const serverResponseData = uploadRes.data;
      const fileUrl = typeof serverResponseData === 'object' ? serverResponseData.url : serverResponseData;
      const fileName = typeof serverResponseData === 'object' ? (serverResponseData.name || serverResponseData.originalFilename || file.name) : file.name;

      // 使用后端返回的完整附件信息
      const thumbnailUrl = typeof serverResponseData === 'object' ? serverResponseData.thumbnailUrl : null;
      const fileSize = typeof serverResponseData === 'object' ? serverResponseData.fileSize : file.size;
      const fileType = typeof serverResponseData === 'object' ? serverResponseData.fileType : file.type;
      const uploadTime = typeof serverResponseData === 'object' ? serverResponseData.uploadTime : new Date().toISOString();

      form.attachments.push({
        fileName,
        fileUrl,
        thumbnailUrl,
        fileSize,
        fileType,
        uploadTime
      });

      file.url = fileUrl;
      file.status = 'success';
      // 保存完整的附件信息到文件对象
      file.thumbnailUrl = thumbnailUrl;
      file.fileSize = fileSize;
      file.fileType = fileType;
      file.uploadTime = uploadTime;

      onSuccess(uploadRes, file);
      ElMessage.success(t('common.uploadSuccess', { fileName: file.name }, `${file.name} 上传成功！`));
    } else {
      throw new Error(uploadRes.message || 'File upload failed with no specific message');
    }
  } catch (error) {
    console.error('Upload error:', error);
    file.status = 'fail';
    onError(error, file);
    ElMessage.error(t('common.uploadFailed', { fileName: file.name, error: error.message || 'Unknown error' }, `${file.name} 上传失败: ${error.message || '未知错误'}`));
  } finally {
    uploading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.customer-form {
  padding: 20px 0;

  :deep(.el-form-item) {
    margin-bottom: 22px;

    .el-form-item__label {
      font-weight: normal;
      color: #606266;
    }

    &.is-required .el-form-item__label::before {
      margin-right: 4px;
    }

    .el-form-item__content {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        
        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }
  }

  .upload-area {
    width: 100%;
    :deep(.el-upload-dragger) {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    :deep(.el-upload-list--text) {
    }
     :deep(.el-upload-list__item) {
        transition: none !important;
     }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  
  .el-button + .el-button {
    margin-left: 12px;
  }
}
</style> 
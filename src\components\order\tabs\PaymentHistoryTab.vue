<template>
  <div class="payment-history-tab">
    <!-- 金额汇总信息 -->
    <div class="info-section">
      <div class="section-header">{{ $t('order.paymentSummary') }}</div>
      <div class="summary-section">
        <div class="summary-cards">
          <div class="summary-card">
            <div class="card-label">{{ $t('paymentHistory.summary.totalPayable') }}</div>
            <div class="card-value total-amount">¥{{ formattedSummary.totalPayable }}</div>
          </div>
          <div class="summary-card">
            <div class="card-label">{{ $t('paymentHistory.summary.totalPaid') }}</div>
            <div class="card-value paid-amount">¥{{ formattedSummary.totalPaid }}</div>
          </div>
          <div class="summary-card">
            <div class="card-label">{{ $t('paymentHistory.summary.overdueAmount') }}</div>
            <div class="card-value overdue-amount">¥{{ formattedSummary.overdueAmount }}</div>
          </div>
          <div class="summary-card">
            <div class="card-label">{{ $t('paymentHistory.summary.nextPaymentAmount') }}</div>
            <div class="card-value next-payment-amount">¥{{ formattedSummary.nextPaymentAmount }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和导出 -->
    <div class="info-section">
      <div class="section-header">
        <span>{{ $t('order.paymentDetails') }}</span>
        <div class="action-buttons">
          <!-- <el-button type="primary" size="small" @click="handleMakePayment" :icon="Money">
            {{ $t('paymentHistory.actions.makePayment') }}
          </el-button> -->
          <el-button
            type="success"
            size="small"
            @click="handleExport"
            :icon="Download"
            :loading="exportLoading"
            :disabled="total === 0"
          >
            {{ exportLoading ? $t('common.exporting') : $t('common.export') }}
          </el-button>
        </div>
      </div>
      
      <div class="filter-export-section">
        <div class="filters">
          <!-- <el-select 
            v-model="filters.transactionType" 
            :placeholder="$t('paymentHistory.filters.typePlaceholder')" 
            clearable 
            class="filter-item"
          >
            <el-option :label="$t('paymentHistory.types.INITIAL')" value="INITIAL" />
            <el-option :label="$t('paymentHistory.types.DEPOSIT')" value="DEPOSIT" />
            <el-option :label="$t('paymentHistory.types.INSTALLMENT')" value="INSTALLMENT" />
            <el-option :label="$t('paymentHistory.types.REFUND')" value="REFUND" />
          </el-select> -->
          <el-select 
            v-model="filters.status" 
            :placeholder="$t('paymentHistory.filters.statusPlaceholder')" 
            clearable 
            class="filter-item"
            @change="handleSearch"
          >
            <el-option :label="$t('paymentHistory.statuses.PAID')" value="PAID" />
            <el-option :label="$t('paymentHistory.statuses.PENDING')" value="PENDING" />
            <!-- <el-option :label="$t('paymentHistory.statuses.LATE_PAID')" value="LATE_PAID" /> -->
            <el-option :label="$t('paymentHistory.statuses.OVERDUE')" value="OVERDUE" />
            <!-- <el-option :label="$t('paymentHistory.statuses.REFUNDED')" value="REFUNDED" /> -->
          </el-select>
          <!-- <el-date-picker
            v-model="filters.paymentDateRange"
            type="daterange"
            unlink-panels
            range-separator="-"
            size="default"
            :start-placeholder="$t('paymentHistory.filters.paymentDateStart')"
            :end-placeholder="$t('paymentHistory.filters.paymentDateEnd')"
            value-format="YYYY-MM-DD"
            class="filter-item date-picker"
            :prefix-icon="Calendar"
            style="width: 280px;"
          /> -->
          <el-button type="primary" @click="handleSearch" :icon="Search">{{ $t('common.search') }}</el-button>
          <el-button @click="resetFilters" :icon="Refresh">{{ $t('common.reset') }}</el-button>
        </div>
      </div>

      <!-- 交易明细表格 -->
      <div class="payment-table">
        <el-table
          :data="tableData"
          v-loading="loading"
          border
          style="width: 100%"
          :default-sort="{ prop: 'dueDate', order: 'ascending' }"
          :empty-text="$t('paymentHistory.noData')"
          class="payment-history-table"
        >
          <el-table-column type="index" :label="$t('paymentHistory.table.index')" width="80" align="center">
            <template #default="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="transactionDate" :label="$t('paymentHistory.table.transactionDate')" width="120" align="center" sortable>
            <template #default="{ row }">
              {{ formatDate(row.transactionDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" :label="$t('paymentHistory.table.type')" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)" size="small">
                {{ formatTransactionType(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="installmentNo" :label="$t('paymentHistory.table.installmentNo')" width="100" align="center">
             <template #default="{ row }">
              {{ row.installmentNo ? `${row.installmentNo}/${totalInstallments}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="payableAmount" :label="$t('paymentHistory.table.payableAmount')" width="120" align="right" sortable>
             <template #default="{ row }">
              {{ formatCurrency(row.payableAmount + row.overdueAmount)  }}
            </template>
          </el-table-column>
          <el-table-column prop="paidAmount" :label="$t('paymentHistory.table.paidAmount')" width="120" align="right" sortable>
            <template #default="{ row }">
              {{ row.paidAmount != null ? formatCurrency(row.paidAmount + row.penaltyPaid) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="overdueAmount" :label="$t('paymentHistory.table.overdueAmount')" width="120" align="right" sortable>
            <template #default="{ row }">
              <span :class="{ 'overdue-text': row.overdueAmount > 0 }">
                {{ row.overdueAmount > 0 ? formatCurrency(row.overdueAmount) : '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('paymentHistory.table.status')" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">{{ formatPaymentStatus(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="dueDate" :label="$t('paymentHistory.table.dueDate')" width="120" align="center" sortable>
            <template #default="{ row }">
              {{ formatDate(row.dueDate) }}
            </template>
          </el-table-column> -->
          <el-table-column prop="paidTime" :label="$t('paymentHistory.table.paidTime')" width="150" align="center" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.paidTime) || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="paymentMethod" :label="$t('paymentHistory.table.paymentMethod')" width="110" align="center">
            <template #default="{ row }">
              {{ formatPaymentMethod(row.paymentMethod) || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="remarks" :label="$t('common.remarks')" min-width="140" align="center">
            <template #default="{ row }">
              <span v-if="row.status === 'OVERDUE'" class="overdue-text">
                {{ $t('paymentHistory.overdueDays', { days: calculateOverdueDays(row.dueDate) }) }}
              </span>
              <el-tooltip
                v-else-if="row.remarks"
                :content="row.remarks"
                placement="top"
                effect="dark"
              >
                <div class="ellipsis-text">{{ row.remarks }}</div>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('common.actions')" width="100" align="center" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="row.status === 'PENDING' || row.status === 'OVERDUE'" 
                type="primary" 
                size="small" 
                @click="handlePayTransaction(row)"
                :disabled="!row.canPay"
              >
                {{ $t('paymentHistory.actions.pay') }}
              </el-button>
              <el-button 
                v-else-if="row.status === 'PAID' || row.status === 'LATE_PAID'" 
                type="info" 
                size="small" 
                @click="handleViewPaymentDetails(row)"
              >
                {{ $t('paymentHistory.actions.details') }}
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>

        <el-pagination
          v-if="total > 0"
          class="pagination-container"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        
        <div v-if="!loading && tableData.length === 0" class="empty-payments">
          {{ $t('paymentHistory.noData') }}
        </div>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <payment-dialog 
      v-model="showPaymentDialog" 
      :order-id="props.orderId"
      @success="handlePaymentSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, reactive, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Search, Refresh, Download, Money, Calendar } from '@element-plus/icons-vue';
import { getOrderPayments, addOrderPayment, exportOrderPayments } from '@/api/order';
import { ElMessage, ElMessageBox } from 'element-plus';
import PaymentDialog from '@/components/order/PaymentDialog.vue';

const { t } = useI18n();

const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  },
  paymentInfo: {
    type: Object,
    default: () => null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);

const loading = ref(false);
const exportLoading = ref(false);
const summaryData = ref({
  totalPayable: 0,
  totalPaid: 0,
  overdueAmount: 0,
  nextPaymentAmount: 0
});

const fullFilteredPaymentData = ref([]); // 新增：存储所有筛选后的支付数据

// 更改为计算属性，用于显示当前页数据
const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return fullFilteredPaymentData.value.slice(start, end);
});

// 更改为计算属性，表示总条数
const total = computed(() => fullFilteredPaymentData.value.length);

const currentPage = ref(1);
const pageSize = ref(10);
const totalInstallments = ref(12); // 默认值，实际应从API获取

// 支付弹窗控制
const showPaymentDialog = ref(false);
const selectedTransaction = ref(null);

const filters = reactive({
  transactionType: '',
  status: '',
  paymentDateRange: null,
});

// 计算逾期天数
const calculateOverdueDays = (dueDate) => {
  if (!dueDate) return 0;
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dueDateObj = new Date(dueDate);
  dueDateObj.setHours(0, 0, 0, 0);
  
  if (dueDateObj >= today) return 0;
  
  const diffTime = Math.abs(today - dueDateObj);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// 格式化金额
const formatCurrency = (amount) => {
  if (amount === undefined || amount === null) return '0.00';
  
  // 将字符串转换为数值
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // 检查是否为有效数值
  if (isNaN(numAmount)) return '0.00';
  
  // 四舍五入到两位小数并格式化
  return Number(numAmount).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  try {
    // 确保统一处理ISO时间格式
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // 如果无法解析，直接返回原始字符串
    
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit' 
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
};

// 格式化日期和时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('日期时间格式化错误:', error);
    return dateString;
  }
};

// 格式化交易类型
const formatTransactionType = (type) => {
  if (!type) return t('common.unknown');
  
  // 将类型转换为小写以便匹配i18n键
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  const typeMap = {
    'initial': t('paymentHistory.types.INITIAL'),
    'deposit': t('paymentHistory.types.DEPOSIT'),
    'installment': t('paymentHistory.types.INSTALLMENT'),
    'refund': t('paymentHistory.types.REFUND')
  };
  
  return typeMap[lowerType] || type;
};

// 获取交易类型对应的标签类型
const getTypeTagType = (type) => {
  if (!type) return 'info';
  
  // 将类型转换为小写以便匹配
  const lowerType = typeof type === 'string' ? type.toLowerCase() : '';
  
  const map = {
    'initial': 'success',
    'deposit': 'info',
    'installment': 'primary',
    'refund': 'warning'
  };
  
  return map[lowerType] || 'info';
};

// 格式化支付状态
const formatPaymentStatus = (status) => {
  if (!status) return t('common.unknown');
  
  // 将状态转换为小写以便匹配i18n键
  const lowerStatus = typeof status === 'string' ? status.toLowerCase() : '';
  
  const statusMap = {
    'paid': t('paymentHistory.statuses.PAID'),
    'pending': t('paymentHistory.statuses.PENDING'),
    'late_paid': t('paymentHistory.statuses.LATE_PAID'),
    'overdue': t('paymentHistory.statuses.OVERDUE'),
    'refunded': t('paymentHistory.statuses.REFUNDED')
  };
  
  return statusMap[lowerStatus] || status;
};

// 获取支付状态对应的标签类型
const getStatusTagType = (status) => {
  if (!status) return 'info';
  
  // 将状态转换为小写以便匹配
  const lowerStatus = typeof status === 'string' ? status.toLowerCase() : '';
  
  const map = {
    'paid': 'success',
    'pending': 'info',
    'late_paid': 'warning',
    'overdue': 'danger',
    'refunded': 'info'
  };
  
  return map[lowerStatus] || 'info';
};

// 获取支付方式标签
const formatPaymentMethod = (method) => {
  if (!method) return '-';
  
  // 支持中文直接显示
  if (method === '线上支付' || method === '线下转账' || 
      method === '现金支付' || method === '银行转账' ||
      method === '微信支付' || method === '支付宝支付') {
    return method;
  }
  
  const methodMap = {
    'ONLINE': '线上支付',
    'OFFLINE_TRANSFER': '线下转账',
    'BANK_TRANSFER': '银行转账',
    'CASH': '现金支付',
    'POS': 'POS机刷卡',
    'WECHAT': '微信支付',
    'ALIPAY': '支付宝支付',
    'OTHER': '其他方式',
    // 支持小写映射
    'online': '线上支付',
    'offline_transfer': '线下转账',
    'bank_transfer': '银行转账',
    'cash': '现金支付',
    'pos': 'POS机刷卡',
    'wechat': '微信支付',
    'alipay': '支付宝支付',
    'other': '其他方式'
  };
  
  return methodMap[method] || method;
};

// 确保汇总数据的响应性
const formattedSummary = computed(() => {
  return {
    totalPayable: formatCurrency(summaryData.value.totalPayable),
    totalPaid: formatCurrency(summaryData.value.totalPaid),
    overdueAmount: formatCurrency(summaryData.value.overdueAmount),
    nextPaymentAmount: formatCurrency(summaryData.value.nextPaymentAmount)
  };
});

// 监听汇总数据变化
watch(() => summaryData.value, (newVal) => {
  console.log('汇总数据已更新:', newVal);
}, { deep: true });

// 尝试自动重试的获取支付记录
let retryCount = 0;
const maxRetries = 2;

// 获取支付记录数据，支持自动重试
const fetchPaymentData = async (isRetry = false) => {
  if (!props.orderId) {
    console.warn('未提供订单ID，无法获取支付记录');
    return;
  }
  
  if (!isRetry) {
    retryCount = 0; // 重置重试计数
  } else if (retryCount >= maxRetries) {
    console.warn(`已达到最大重试次数(${maxRetries})，停止重试`);
    return;
  }
  
  loading.value = true;
  
  try {
    // 确保订单ID格式正确
    const safeOrderId = typeof props.orderId === 'object' ? props.orderId.toString() : props.orderId;
    console.log(`获取订单[${safeOrderId}]的付款计划，订单ID类型:`, typeof safeOrderId);
    
    if (isRetry) {
      console.log(`这是第 ${retryCount + 1} 次重试`);
    }
    
    // 构建请求参数
    const params = {};
    if (filters.transactionType) {
      params.type = filters.transactionType;
    }
    if (filters.status) {
      params.status = filters.status;
    }
    if (filters.paymentDateRange && filters.paymentDateRange.length === 2) {
      params.startDate = filters.paymentDateRange[0];
      params.endDate = filters.paymentDateRange[1];
    }
    
    console.log(`请求订单(${safeOrderId})的支付记录, 参数:`, params);
    
    // 调用API获取支付记录 - 确保使用订单号作为路径参数
    const response = await getOrderPayments(safeOrderId, params);
    
    console.log('支付记录API响应:', response);
    
    if (response && response.code === 1000) {
      // 更新汇总数据
      if (response.data) {
        // 保存原始数据以便调试
        console.log('API返回的原始数据:', JSON.stringify(response.data));
        
        // 检查响应中是否有错误消息
        if (response.data.message && 
            (response.data.message.includes('不存在') || 
             response.data.message.includes('找不到'))) {
          console.error('API返回错误消息:', response.data.message);
          ElMessage.error(response.data.message || t('common.fetchFailed'));
          fullFilteredPaymentData.value = [];
          loading.value = false;
          return;
        }
        
        // 使用paymentInfo字段获取支付汇总信息
        const paymentInfo = response.data.paymentInfo || {};
        
        // 设置汇总数据，支持多种可能的字段名
        // 确保获取正确的数值，避免显示0
        summaryData.value = {
          totalPayable: extractValue([
            paymentInfo.totalPayableAmount,
            paymentInfo.totalAmount,
            response.data.totalAmount,
            response.data.totalPayableAmount
          ], 0),
          totalPaid: extractValue([
            paymentInfo.totalPaidAmount,
            paymentInfo.paidAmount,
            response.data.totalPaidAmount,
            response.data.paidAmount
          ], 0),
          overdueAmount: extractValue([
            paymentInfo.currentOverduePrincipal,
            paymentInfo.totalUnpaidPenalty,
            paymentInfo.totalOverdueAmount,
            paymentInfo.overdueAmount,
            response.data.overdueAmount,
            response.data.totalOverdueAmount
          ], 0),
          nextPaymentAmount: extractValue([
            paymentInfo.nextPaymentDueAmount,
            paymentInfo.nextPaymentAmount,
            paymentInfo.nextInstallmentAmount,
            response.data.nextPaymentAmount,
            response.data.nextPaymentDueAmount
          ], 0)
        };
        
        // 记录汇总数据用于调试
        console.log('处理后的汇总数据:', summaryData.value);
        
        // 更新期数信息 - 从orderInfo中获取总期数
        if (response.data.orderInfo && response.data.orderInfo.numberOfInstallments) {
          totalInstallments.value = response.data.orderInfo.numberOfInstallments;
        } else if (response.data.orderInfo && response.data.orderInfo.installments) {
          totalInstallments.value = response.data.orderInfo.installments;
        } else if (response.data.totalInstallments) {
          totalInstallments.value = response.data.totalInstallments;
        }
        
        // 打印调试信息
        console.log('汇总数据:', summaryData.value);
        console.log('总期数:', totalInstallments.value);
        
        // 更新表格数据 - 支持从多种可能的字段获取数据
        let paymentData = extractPaymentData(response.data);
        
        console.log('找到的支付数据:', paymentData);
        
        if (paymentData && paymentData.length > 0) {
          let processedData = paymentData.map(item => processPaymentItem(item))
            .filter(item => item !== null); // 过滤掉无效项

          // 客户端过滤：根据filters应用过滤条件
          processedData = processedData.filter(item => {
            let match = true;
            // 过滤交易类型
            if (filters.transactionType) {
              match = match && (item.type && item.type.toLowerCase() === filters.transactionType.toLowerCase());
            }
            // 过滤支付状态
            if (filters.status) {
              match = match && (item.status && item.status.toLowerCase() === filters.status.toLowerCase());
            }
            // 过滤支付日期范围
            if (filters.paymentDateRange && filters.paymentDateRange.length === 2 && item.transactionDate) {
              const itemDate = new Date(item.transactionDate);
              const startDate = new Date(filters.paymentDateRange[0]);
              const endDate = new Date(filters.paymentDateRange[1]);
              startDate.setHours(0, 0, 0, 0); // 设置为开始日期的0点
              endDate.setHours(23, 59, 59, 999); // 设置为结束日期的23:59:59.999
              match = match && (itemDate >= startDate && itemDate <= endDate);
            }
            return match;
          });

          fullFilteredPaymentData.value = processedData;
          
          // total 会自动从 fullFilteredPaymentData 的长度计算
          // tableData 会自动从 fullFilteredPaymentData 截取
        } else {
          fullFilteredPaymentData.value = [];
          console.warn('未找到支付计划数据');
        }
      }
    } else {
      const errorMessage = response?.message || t('common.fetchFailed');
      ElMessage.error(errorMessage);
      
      // 特定错误码可以尝试重试
      if (response && (response.code === 5001 || response.code === 4004)) { // 假设这些是需要重试的错误码
        retryCount++;
        setTimeout(() => {
          console.log(`正在进行第 ${retryCount} 次重试...`);
          fetchPaymentData(true);
        }, 1000 * retryCount); // 随着重试次数增加等待时间
        return;
      }
      fullFilteredPaymentData.value = []; // 错误时清空数据
    }
  } catch (error) {
    console.error('获取支付记录失败:', error);
    ElMessage.error(t('common.fetchFailed'));
    fullFilteredPaymentData.value = []; // 错误时清空数据
    
    // 对网络错误进行重试
    if (error.message && (error.message.includes('network') || error.message.includes('timeout'))) {
      retryCount++;
      setTimeout(() => {
        console.log(`网络错误，正在进行第 ${retryCount} 次重试...`);
        fetchPaymentData(true);
      }, 1000 * retryCount);
      return;
    }
  } finally {
    if (!isRetry || retryCount >= maxRetries) {
      loading.value = false;
    }
  }
};

// 从多个可能的来源提取支付数据
const extractPaymentData = (data) => {
  // 按优先级尝试不同来源的数据
  if (data.paymentInfo && data.paymentInfo.transactions && data.paymentInfo.transactions.length > 0) {
    console.log('使用paymentInfo.transactions字段数据');
    return data.paymentInfo.transactions;
  }
  
  if (data.transactions && Array.isArray(data.transactions) && data.transactions.length > 0) {
    console.log('使用顶层transactions字段数据');
    return data.transactions;
  }
  
  if (data.paymentInfo && data.paymentInfo.paymentPlans && data.paymentInfo.paymentPlans.length > 0) {
    console.log('使用paymentInfo.paymentPlans字段数据');
    return data.paymentInfo.paymentPlans;
  }
  
  if (data.paymentPlans && Array.isArray(data.paymentPlans) && data.paymentPlans.length > 0) {
    console.log('使用顶层paymentPlans字段数据');
    return data.paymentPlans;
  }
  
  if (data.paymentSchedules && Array.isArray(data.paymentSchedules) && data.paymentSchedules.length > 0) {
    console.log('使用paymentSchedules字段数据');
    return data.paymentSchedules;
  }
  
  if (data.paymentList && Array.isArray(data.paymentList) && data.paymentList.length > 0) {
    console.log('使用paymentList字段数据');
    return data.paymentList;
  }
  
  return [];
};

// 处理单个支付项数据
const processPaymentItem = (item) => {
  if (!item) {
    console.warn('发现无效的支付项数据');
    return null;
  }
  
  try {
    const processed = {
      id: extractValue([item.transactionId, item.id, item.paymentId, item.paymentPlanId]),
      transactionDate: extractValue([
        item.creationDate, 
        item.createTime, 
        item.transactionDate, 
        item.dueDate, 
        item.paymentDate
      ]),
      type: extractValue([
        item.transactionType, 
        item.paymentType, 
        item.type
      ], 'INSTALLMENT'),
      installmentNo: extractValue([
        item.installmentNumber, 
        item.installmentNo, 
        item.installmentIndex, 
        item.periodNo
      ]),
      payableAmount: extractValue([
        item.payableAmount, 
        item.amount, 
        item.principal, 
        item.installmentAmount
      ], 0) + extractValue([item.serviceFee, item.serviceCharge, item.fee], 0),
      paidAmount: extractValue([
        item.actualPaidAmount, 
        item.paidAmount, 
        item.paymentAmount, 
        item.paidAmount
      ]),
      overdueAmount: extractValue([
        item.overdueAmount, 
        item.overdueFee, 
        item.overduePenalty
      ], 0),
      serviceFee: extractValue([item.serviceFee, item.serviceCharge, item.fee], 0),
      status: extractValue([
        item.status, 
        item.paymentStatus
      ], item.isPaid ? 'PAID' : 'PENDING'),
      dueDate: extractValue([item.dueDate, item.paymentDueDate, item.expectedPaymentDate]),
      paidTime: extractValue([
        item.paymentDate, 
        item.paymentTime, 
        item.paidTime, 
        item.paidDate, 
        item.actualPaymentDate
      ]),
      penaltyPaid: extractValue([item.penaltyPaid], 0),
      paymentMethod: item.paymentMethod,
      remarks: extractValue([item.paymentRemark, item.remarks, item.description, item.comment, item.note]),
      canPay: item.canPay !== false
    };
    console.log('原始支付项:', item, '处理后:', processed);
    return processed;
  } catch (err) {
    console.error('处理支付项数据时出错:', err, item);
    return null;
  }
};

// 从多个可能的字段中提取值
const extractValue = (possibleValues, defaultValue = null) => {
  for (const value of possibleValues) {
    if (value !== undefined && value !== null) {
      return value;
    }
  }
  return defaultValue;
};

// 处理支付按钮点击
const handlePayTransaction = (row) => {
  selectedTransaction.value = row;
  showPaymentDialog.value = true;
};

// 处理支付成功回调
const handlePaymentSuccess = () => {
  showPaymentDialog.value = false;
  fetchPaymentData(); // 刷新数据
  emit('retry-fetch'); // 通知父组件刷新整个订单详情
};

// 处理查看支付详情
const handleViewPaymentDetails = (row) => {
  ElMessageBox.alert(
    `<div class="payment-details-box">
      <p><strong>${t('paymentHistory.table.type')}:</strong> ${formatTransactionType(row.type)}</p>
      <p><strong>${t('paymentHistory.table.payableAmount')}:</strong> ¥${formatCurrency(row.payableAmount)}</p>
      <p><strong>${t('paymentHistory.table.paidAmount')}:</strong> ¥${formatCurrency(row.paidAmount)}</p>
      <p><strong>${t('paymentHistory.table.paymentMethod')}:</strong> ${formatPaymentMethod(row.paymentMethod)}</p>
      <p><strong>${t('paymentHistory.table.paidTime')}:</strong> ${formatDateTime(row.paidTime)}</p>
      <p><strong>${t('common.remarks')}:</strong> ${row.remarks || '-'}</p>
    </div>`,
    t('paymentHistory.actions.details'),
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: t('common.confirm')
    }
  );
};

// 处理统一支付按钮点击
const handleMakePayment = () => {
  // 筛选出可支付的交易
  const payableTransactions = tableData.value.filter(item => 
    (item.status === 'PENDING' || item.status === 'OVERDUE') && item.canPay !== false
  );
  
  if (payableTransactions.length === 0) {
    ElMessage.warning(t('paymentHistory.noPayableTransactions'));
    return;
  }
  
  showPaymentDialog.value = true;
};

// 处理导出
const handleExport = async () => {
  if (!props.orderId) {
    ElMessage.warning(t('payment.export.noOrderId'));
    return;
  }
  exportLoading.value = true;
  try {
    const params = {
      format: 'excel',
      page: currentPage.value,
      size: pageSize.value,
      status: filters.status || null
    };

    // 移除值为null的参数
    Object.keys(params).forEach((key) => {
      if (params[key] === null) {
        delete params[key];
      }
    });

    const response = await exportOrderPayments(props.orderId, params);

    const blob = new Blob([response.data], {
      type: response.headers['content-type']
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);

    const contentDisposition = response.headers['content-disposition'];
    let fileName = `payment_details_${props.orderId}.xlsx`;
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(
        /filename\*?=['"]?(?:UTF-\d['"]*)?([^;"\n]*?)['";\n]?$/i
      );
      if (fileNameMatch && fileNameMatch[1]) {
        try {
          fileName = decodeURIComponent(fileNameMatch[1]);
        } catch (e) {
          fileName = fileNameMatch[1];
        }
      }
    }

    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    ElMessage.success(t('common.exportSuccess'));
  } catch (error) {
    console.error('Export failed:', error);
    let errorMessage = t('common.exportFailed');
    if (error.response && error.response.data) {
      try {
        const errorText = await error.response.data.text();
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // 忽略解析错误
      }
    }
    ElMessage.error(errorMessage);
  } finally {
    exportLoading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchPaymentData();
};

// 重置筛选条件
const resetFilters = () => {
  filters.transactionType = '';
  filters.status = '';
  filters.paymentDateRange = null;
  handleSearch();
};

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 切换页面大小时，回到第一页
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
};

// 处理重试获取数据
const handleRetryFetch = () => {
  emit('retry-fetch');
};

// 监听订单ID变化
watch(() => props.orderId, (newValue) => {
  if (newValue) {
    console.log(`订单ID变化为: ${newValue}, 类型: ${typeof newValue}`);
    resetFilters();
    fetchPaymentData();
  }
});

// 初始化时获取数据
onMounted(() => {
  if (props.orderId) {
    console.log(`组件挂载时订单ID: ${props.orderId}, 类型: ${typeof props.orderId}`);
    handleSearch()
    
    // 检查是否已有父组件传入的paymentInfo数据
    if (props.paymentInfo) {
      console.log('组件挂载时已有paymentInfo数据:', props.paymentInfo);
      
      // 直接处理父组件传入的数据
      const paymentInfo = props.paymentInfo;
      summaryData.value = {
        totalPayable: paymentInfo.totalPayableAmount || paymentInfo.totalAmount || 0,
        totalPaid: paymentInfo.totalPaidAmount || paymentInfo.paidAmount || 0,
        overdueAmount: paymentInfo.totalUnpaidPenalty || paymentInfo.totalOverdueAmount || 
                      paymentInfo.currentOverduePrincipal || paymentInfo.overdueAmount || 0,
        nextPaymentAmount: paymentInfo.nextPaymentDueAmount || paymentInfo.nextPaymentAmount || 
                          paymentInfo.nextInstallmentAmount || 0
      };
      
      console.log('组件挂载时设置的汇总数据:', summaryData.value);
      
      // 如果有交易记录，处理交易记录
      if (paymentInfo.transactions && paymentInfo.transactions.length > 0) {
        console.log('组件挂载时使用现有交易记录数据');
        let processedData = paymentInfo.transactions.map(item => processPaymentItem(item))
          .filter(item => item !== null); // 过滤掉无效项

        // 客户端过滤：根据filters应用过滤条件
        processedData = processedData.filter(item => {
          let match = true;
          if (filters.transactionType) {
            match = match && (item.type && item.type.toLowerCase() === filters.transactionType.toLowerCase());
          }
          if (filters.status) {
            match = match && (item.status && item.status.toLowerCase() === filters.status.toLowerCase());
          }
          if (filters.paymentDateRange && filters.paymentDateRange.length === 2 && item.transactionDate) {
            const itemDate = new Date(item.transactionDate);
            const startDate = new Date(filters.paymentDateRange[0]);
            const endDate = new Date(filters.paymentDateRange[1]);
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            match = match && (itemDate >= startDate && itemDate <= endDate);
          }
          return match;
        });

        fullFilteredPaymentData.value = processedData;
      } else {
        // 没有交易记录，需要请求数据
        fetchPaymentData();
      }
    } else {
      // 没有父组件传入的paymentInfo数据，主动获取
      fetchPaymentData();
    }
  }
});

// 监听父组件传入的支付信息变化
watch(() => props.paymentInfo, (newValue) => {
  if (newValue) {
    console.log('接收到父组件传入的支付信息:', newValue);
    
    // 确保paymentInfo包含正确的字段
    const paymentInfo = newValue || {};
    
    // 更新汇总数据 - 从多个可能的字段名取值
    summaryData.value = {
      totalPayable: paymentInfo.totalPayableAmount || paymentInfo.totalAmount || 0,
      totalPaid: paymentInfo.totalPaidAmount || paymentInfo.paidAmount || 0,
      overdueAmount: paymentInfo.totalUnpaidPenalty || paymentInfo.totalOverdueAmount || 
                    paymentInfo.currentOverduePrincipal || paymentInfo.overdueAmount || 0,
      nextPaymentAmount: paymentInfo.nextPaymentDueAmount || paymentInfo.nextPaymentAmount || 
                        paymentInfo.nextInstallmentAmount || 0
    };
    
    // 打印更新后的汇总数据
    console.log('更新后的汇总数据:', summaryData.value);
    
    // 如果父组件传递的数据中包含交易记录，直接使用
    if (newValue.transactions && newValue.transactions.length > 0) {
      console.log('使用父组件传入的交易记录数据:', newValue.transactions.length, '条');
      
      let processedData = newValue.transactions.map(processPaymentItem)
        .filter(item => item !== null); // 直接填充 fullFilteredPaymentData

      // 客户端过滤：根据filters应用过滤条件
      processedData = processedData.filter(item => {
        let match = true;
        if (filters.transactionType) {
          match = match && (item.type && item.type.toLowerCase() === filters.transactionType.toLowerCase());
        }
        if (filters.status) {
          match = match && (item.status && item.status.toLowerCase() === filters.status.toLowerCase());
        }
        if (filters.paymentDateRange && filters.paymentDateRange.length === 2 && item.transactionDate) {
          const itemDate = new Date(item.transactionDate);
          const startDate = new Date(filters.paymentDateRange[0]);
          const endDate = new Date(filters.paymentDateRange[1]);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);
          match = match && (itemDate >= startDate && itemDate <= endDate);
        }
        return match;
      });

      fullFilteredPaymentData.value = processedData;
    } else if (!props.isLoading) {
      // 如果父组件没有传递交易记录或是空数组，则主动获取数据
      fetchPaymentData();
    }
  }
}, { immediate: true });

// 监听加载状态变化
watch(() => props.isLoading, (newValue) => {
  if (!newValue && props.orderId) {
    fetchPaymentData();
  }
});
</script>

<style lang="scss" scoped>
.payment-history-tab {
  padding: 20px;
  
  .info-section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 16px;
      font-weight: 500;
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }
    
    .summary-section {
      padding: 16px;
      
      .summary-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        
        .summary-card {
          flex: 1;
          min-width: 200px;
          padding: 16px;
          background-color: #f9f9f9;
          border-radius: 6px;
          border: 1px solid #f0f0f0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          
          .card-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }
          
          .card-value {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            
            &.total-amount {
              color: #333;
            }
            
            &.paid-amount {
              color: #67c23a;
            }
            
            &.overdue-amount {
              color: #f56c6c;
            }
            
            &.next-payment-amount {
              color: #409eff;
            }
          }
        }
      }
    }
    
    .filter-export-section {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .filters {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .filter-item {
          width: 150px;
          
          &.date-picker {
            width: 280px;
          }
        }
      }
    }
    
    .payment-table {
      padding: 0 16px 16px;
      
      :deep(.el-table) {
        margin-top: 16px;
        border-radius: 4px;
        overflow: hidden;
        background-color: #fff;
        
        .el-table__header th {
          background-color: #f5f7fa;
          font-weight: 500;
          color: #606266;
          padding: 8px 0;
          border-bottom: 1px solid #EBEEF5;
        }
        
        .el-table__row td {
          padding: 8px;
        }
        
        // 确保表格边界清晰
        &.el-table--border {
          border: 1px solid #EBEEF5;
          
          &::after, &::before {
            background-color: #EBEEF5;
          }
          
          .el-table__inner-wrapper::after,
          .el-table__inner-wrapper::before {
            background-color: #EBEEF5;
          }
        }
      }
      
      .pagination-container {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
      }
      
      .empty-payments {
        margin-top: 16px;
        text-align: center;
        padding: 30px;
        color: #909399;
        background-color: #f9f9f9;
        border: 1px dashed #e0e0e0;
        border-radius: 4px;
      }
    }
  }
}

.overdue-text {
  color: #f56c6c;
}

.payment-details-box {
  text-align: left;
  
  p {
    margin: 8px 0;
  }
}

.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 
import request from '@/utils/request';

/**
 * 获取企业合计数据
 * @returns {Promise<Object>} 包含企业合计数据的Promise
 */
export function getTotalStatistics() {
  return request.get('/crm-service/api/statistics/total');
}

/**
 * 获取统计数据
 * @param {Object} params 查询参数
 * @param {Date|String} [params.startTime] 开始时间
 * @param {Date|String} [params.endTime] 结束时间
 * @param {String} [params.periodType='monthly'] 周期类型，默认为monthly
 * @returns {Promise<Object>} 包含统计数据的Promise
 */
export function getStatistics(params = {}) {
  return request.get('/crm-service/api/statistics/dashboard', { params });
}

/**
 * 获取当前时间段统计数据
 * @param {Object} params 查询参数
 * @param {Date|String} [params.startTime] 开始时间
 * @param {Date|String} [params.endTime] 结束时间
 * @param {String} [params.periodType='monthly'] 周期类型，默认为monthly
 * @returns {Promise<Object>} 包含当前时间段统计数据的Promise
 */
export function getCurrentPeriodStatistics(params = {}) {
  return request.get('/crm-service/api/statistics/current-period', { params });
} 
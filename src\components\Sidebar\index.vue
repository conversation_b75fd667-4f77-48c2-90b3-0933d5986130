<template>
  <div class="sidebar-container">
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        class="el-menu-vertical"
        background-color="#fff"
        text-color="#333"
        active-text-color="#1890ff"
      >
        <template v-for="item in menuList" :key="item.path">
          <el-menu-item 
            v-if="!item.children" 
            :index="item.path"
            @click="handleMenuClick(item)"
          >
            <el-icon v-if="item.meta?.icon">
              <component :is="iconComponents[item.meta.icon]" />
            </el-icon>
            <template #title>
              <span>{{ t(item.meta.title) }}</span>
            </template>
          </el-menu-item>

          <el-sub-menu 
            v-else 
            :index="item.path"
            popper-class="sidebar-popper"
          >
            <template #title>
              <el-icon v-if="item.meta?.icon">
                <component :is="iconComponents[item.meta.icon]" />
              </el-icon>
              <span>{{ t(item.meta.title) }}</span>
            </template>
            <el-menu-item
              v-for="child in item.children"
              :key="child.path"
              :index="child.path.startsWith('/') ? child.path : `${item.path}/${child.path}`"
              @click="handleMenuClick({
                ...child, 
                path: child.path.startsWith('/') ? child.path : `${item.path}/${child.path}`
              })"
              style="padding-left: 40px !important;"
            >
              <el-icon v-if="child.meta?.icon">
                <component :is="iconComponents[child.meta.icon]" />
              </el-icon>
              <template #title>
                <span>{{ t(child.meta.title) }}</span>
              </template>
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { menuList } from '@/router/menu'
import { useI18n } from 'vue-i18n'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const isCollapse = ref(false)

// 图标组件映射
const iconComponents = ElementPlusIconsVue

// 当前激活的菜单
const activeMenu = computed(() => {
  const { path } = route
  const firstLevelPath = '/' + path.split('/')[1]
  return path === firstLevelPath && !menuList.find(item => item.path === path)?.children 
    ? path 
    : path
})

// 处理菜单点击
const handleMenuClick = (item) => {
  if (item.children) {
    return
  }
  router.push(item.path)
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background-color: #fff;
  position: relative;

  .el-menu-vertical {
    border-right: none;
    
    &:not(.el-menu--collapse) {
      width: 210px;
    }

    :deep(.el-sub-menu__title) {
      height: 50px;
      line-height: 50px;
      padding: 0 20px !important;
      position: relative;
      
      &:hover {
        background-color: #f6f6f6;
      }

      .el-icon {
        margin-right: 12px;
        width: 24px;
        text-align: center;
        font-size: 18px;
        vertical-align: middle;
      }
    }

    :deep(.el-menu-item) {
      height: 50px;
      line-height: 50px;
      padding: 0 20px !important;
      position: relative;
      
      &:hover {
        background-color: #f6f6f6;
      }

      &.is-active {
        background-color: #f0f7ff;
        color: #1890ff;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background-color: #1890ff;
        }
      }

      .el-icon {
        margin-right: 12px;
        width: 24px;
        text-align: center;
        font-size: 18px;
        vertical-align: middle;
      }
    }
  }

  .collapse-btn {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #fff;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #dcdfe6;
    
    &:hover {
      background-color: #f6f6f6;
    }
  }
}

:deep(.el-menu--popup) {
  .el-menu-item {
    height: 50px !important;
    line-height: 50px !important;
    padding: 0 20px !important;
    position: relative;
    
    &:hover {
      background-color: #f6f6f6;
    }

    &.is-active {
      background-color: #f0f7ff;
      color: #1890ff;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: #1890ff;
      }
    }

    .el-icon {
      margin-right: 12px;
      width: 24px;
      text-align: center;
      font-size: 18px;
      vertical-align: middle;
    }
  }
}
</style> 
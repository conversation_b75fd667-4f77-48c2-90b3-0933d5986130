import { createI18n } from 'vue-i18n';
import zhCn from './zh-cn';
import en from './en';

const messages = {
  'zh-CN': zhCn,
  'en': en
};

// 获取当前语言
export function getLanguage() {
  const storedLanguage = localStorage.getItem('language');
  if (storedLanguage) {
    return storedLanguage;
  }
  // 如果没有设置语言，则返回浏览器默认语言
  const language = navigator.language;
  const locales = Object.keys(messages);
  for (const locale of locales) {
    if (language.toLowerCase().includes(locale.toLowerCase())) {
      return locale;
    }
  }
  // 默认语言
  return 'zh-CN';
}

const i18n = createI18n({
  legacy: false,
  locale: getLanguage(),
  fallbackLocale: 'zh-CN',
  messages,
  globalInjection: true,
  silentTranslationWarn: true,
  missingWarn: false,
  silentFallbackWarn: true
});

export function setLanguage(lang) {
  i18n.global.locale.value = lang;
  localStorage.setItem('language', lang);
  document.querySelector('html').setAttribute('lang', lang);
}

export default i18n;
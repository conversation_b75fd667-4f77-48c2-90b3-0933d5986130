---
description: 
globs: 
alwaysApply: false
---
# Vue3 项目开发技术文档

## 技术栈概述
本项目基于 Vue 3 进行开发，使用以下技术栈来构建高效、现代化的 Web 应用：
- **Vue 3**：核心前端框架
- **Element Plus**：UI 组件库
- **Pinia**：状态管理
- **Vue Router**：路由管理
- **Tailwind CSS**：CSS 框架
- **JavaScript**：编程语言
- **Sass**：CSS 预处理器
- **Axios**：HTTP 请求库
- **Vite**：前端构建工具


---

## 各技术的使用场景

### 1. Vue 3
**使用场景**：
- 作为核心前端框架，提供响应式数据绑定、组件化开发。
- 使用 Vue Composition API (setup 语法) 进行逻辑封装。
- 结合 Vue 3 的 Teleport、Suspense 等新特性优化项目。

**示例代码：**
```vue
<script setup>
import { ref } from 'vue';
const count = ref(0);
</script>

<template>
  <button @click="count++">点击次数: {{ count }}</button>
</template>
```

---

### 2. Element Plus
**使用场景**：
- 作为 UI 组件库，提高开发效率。
- 使用表单组件、表格、弹窗等功能。
- 结合 Vue 3 进行按需引入，优化性能。

**示例代码：**
```vue
<template>
  <el-button type="primary">主要按钮</el-button>
</template>
```

---

### 3. Pinia
**使用场景**：
- 代替 Vuex 进行全局状态管理。
- 通过模块化设计，使状态管理更简洁高效。

**示例代码：**
```js
import { defineStore } from 'pinia';
export const useCounterStore = defineStore('counter', {
  state: () => ({ count: 0 }),
  actions: {
    increment() {
      this.count++;
    },
  },
});
```

---

### 4. Vue Router
**使用场景**：
- 负责管理应用的页面路由。
- 使用动态路由、导航守卫实现权限管理。

**示例代码：**
```js
import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/Home.vue';

const routes = [
  { path: '/', component: Home },
  { path: '/about', component: () => import('../views/About.vue') },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
```

---

### 5. Tailwind CSS
**使用场景**：
- 进行快速 UI 开发，减少编写自定义 CSS。
- 结合组件库，提升样式的一致性。

**示例代码：**
```vue
<template>
  <button class="bg-blue-500 text-white p-2 rounded-lg">点击我</button>
</template>
```

---

### 6. JavaScript
**使用场景**：
- 作为主要编程语言，实现业务逻辑。
- 结合 Vue 3 进行组件开发。

**示例代码：**
```js
function greet(name) {
  return `Hello, ${name}!`;
}
console.log(greet('Vue3'));
```

---

### 7. Sass
**使用场景**：
- 编写可复用的 CSS 变量、嵌套规则。
- 适用于全局样式管理，提升开发效率。

**示例代码：**
```scss
$primary-color: #42b983;

.button {
  background-color: $primary-color;
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}
```

---

### 8. Axios
**使用场景**：
- 处理 HTTP 请求，与后端进行数据交互。
- 结合 Vue 3 使用全局拦截器，提高接口管理。

**示例代码：**
```js
import axios from 'axios';
axios.get('/api/data').then(response => {
  console.log(response.data);
});
```

---

### 9. Vite
**使用场景**：
- 作为现代前端构建工具，提供极速的开发体验。
- 利用原生 ES 模块实现快速的热模块替换（HMR）。
- 进行按需编译，无需打包整个应用，优化开发环境性能。

**示例 vite 配置：**
```js
// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
  },
});
```

---

## 结论
本项目通过 Vue 3 作为核心框架，结合 Element Plus 提供 UI 组件，使用 Pinia 进行状态管理，Vue Router 负责路由控制。Tailwind CSS 提供灵活的样式方案，Sass 进一步优化 CSS 组织。Axios 用于 API 请求，Webpack 进行打包优化。所有技术的配合保证了项目的高效开发和良好的用户体验。



# Vue3 + Element Plus + JavaScript Layout 技术开发文档

## 1. 概述
封装一个  `Layout` 采用 `Vue3` + `Element Plus` + `JavaScript` 构建，提供后台管理系统的基础页面结构，包括侧边栏、顶部导航、主内容区域等，同时通过封装组件提高代码复用性。

## 2. 依赖组件
### 使用的 `Element Plus` 组件：
- `el-container`：布局容器
- `el-header`：头部区域
- `el-aside`：侧边栏
- `el-main`：主内容区
- `el-menu`：菜单组件
- `el-menu-item`：菜单项
- `el-sub-menu`：子菜单
- `el-icon`：图标
- `el-dropdown`：用户下拉菜单
- `el-dropdown-menu`：下拉菜单列表
- `el-dropdown-item`：下拉菜单项
- `el-button`：按钮
- `el-breadcrumb`：面包屑导航
- `el-breadcrumb-item`：面包屑项
- `el-tooltip`：提示工具

## 3. Layout 结构
### 3.1 组件封装
#### `Sidebar.vue`（侧边栏封装）
```vue
<template>
  <el-aside :width="width">
    <el-menu router :default-active="activeMenu">
      <el-menu-item v-for="item in menuItems" :key="item.index" :index="item.index">
        <el-icon><component :is="item.icon" /></el-icon>
        <span>{{ item.label }}</span>
      </el-menu-item>
    </el-menu>
  </el-aside>
</template>

<script>
export default {
  props: {
    menuItems: {
      type: Array,
      required: true
    },
    width: {
      type: String,
      default: "200px"
    },
    activeMenu: {
      type: String,
      default: "/home"
    }
  }
};
</script>
```

#### `Header.vue`（头部导航封装）
```vue
<template>
  <el-header>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.index" :to="item.to">
        {{ item.label }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    <el-dropdown>
      <el-button>用户</el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="logout">退出</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-header>
</template>

<script>
export default {
  props: {
    breadcrumbs: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    logout() {
      console.log("用户退出");
    }
  }
};
</script>
```

### 3.2 `Layout.vue`（整体布局）
```vue
<template>
  <el-container>
    <Sidebar :menuItems="menuItems" />
    <el-container>
      <Header :breadcrumbs="breadcrumbs" />
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import Sidebar from "@/components/Sidebar.vue";
import Header from "@/components/Header.vue";

export default {
  components: { Sidebar, Header },
  data() {
    return {
      menuItems: [
        { index: "/home", label: "首页", icon: "HomeFilled" },
        { index: "/settings", label: "设置", icon: "SettingFilled" }
      ],
      breadcrumbs: [
        { index: "1", label: "首页", to: "/home" },
        { index: "2", label: "当前页面" }
      ]
    };
  }
};
</script>
```

## 4. 说明
- `Sidebar.vue` 负责侧边栏菜单，可传入 `menuItems` 动态配置菜单。
- `Header.vue` 负责头部导航，包括面包屑导航和用户操作。
- `Layout.vue` 作为整体布局，组合 `Sidebar` 和 `Header` 并包含 `router-view` 负责路由渲染。

## 5. 结论
封装组件后，`vue-pure-admin` 的 `Layout` 结构更加模块化，提高了代码复用性和可维护性，适用于 Vue3 + Element Plus + JavaScript 的管理后台项目开发。






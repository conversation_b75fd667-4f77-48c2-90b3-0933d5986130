<template>
  <div class="penalty-calculation">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="逾期计算" class="penalty-form-item">
          <div class="penalty-input-group">
            <!-- 逾期计算方式 -->
            <el-select 
              v-model="penaltyData.penaltyCalculationType" 
              placeholder="每天"
              @change="handleChange"
              popper-class="penalty-select-dropdown"
              style="width: 120px"
            >
              <el-option label="每天" value="DAILY" />
              <el-option label="每周" value="WEEKLY" />
              <el-option label="每月" value="MONTHLY" />
            </el-select>

            <!-- 违约金数值 -->
            <el-input
              v-model="penaltyData.penaltyValue"
              placeholder="请输入金额"
              @input="handleValueInput"
              @change="handleValueChange"
              style="width: 120px"
            />

            <!-- 违约金类型 -->
            <el-select 
              v-model="penaltyData.penaltyAmountType" 
              placeholder="单位"
              @change="handleAmountTypeChange"
              popper-class="penalty-select-dropdown"
              style="width: 80px"
            >
              <el-option label="元" value="FIXED_AMOUNT" />
              <el-option label="%" value="PERCENTAGE_AMOUNT" />
            </el-select>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface PenaltyData {
  penaltyCalculationType: string;
  penaltyAmountType: string;
  penaltyValue: string;
}

const props = defineProps<{
  modelValue: PenaltyData;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: PenaltyData): void;
  (e: 'change', value: PenaltyData): void;
}>();

// 初始化默认值
const penaltyData = ref<PenaltyData>({
  penaltyCalculationType: 'DAILY',
  penaltyAmountType: 'FIXED_AMOUNT',
  penaltyValue: '0.00'
});

// 监听父组件传入的值
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    penaltyData.value = {
      penaltyCalculationType: newVal.penaltyCalculationType || 'DAILY',
      penaltyAmountType: newVal.penaltyAmountType || 'FIXED_AMOUNT',
      penaltyValue: newVal.penaltyValue || '0.00'
    };
  }
}, { immediate: true, deep: true });

// 监听本地数据变化
watch(penaltyData, (newVal) => {
  emit('update:modelValue', { ...newVal });
  emit('change', { ...newVal });
}, { deep: true });

const handleChange = () => {
  emit('update:modelValue', { ...penaltyData.value });
  emit('change', { ...penaltyData.value });
};

const handleAmountTypeChange = () => {
  penaltyData.value.penaltyValue = '0.00';
  handleChange();
};

const handleValueInput = (value: string) => {
  // 只允许输入数字和小数点
  const newValue = value.replace(/[^\d.]/g, '');
  
  // 处理小数点
  const parts = newValue.split('.');
  if (parts.length > 2) {
    // 保留第一个小数点
    penaltyData.value.penaltyValue = parts[0] + '.' + parts.slice(1).join('');
  } else if (parts.length === 2) {
    // 限制小数位数为2位
    penaltyData.value.penaltyValue = parts[0] + '.' + parts[1].slice(0, 2);
  } else {
    penaltyData.value.penaltyValue = newValue;
  }
};

const handleValueChange = () => {
  let value = parseFloat(penaltyData.value.penaltyValue);
  
  if (isNaN(value)) {
    value = 0;
  }
  
  if (penaltyData.value.penaltyAmountType === 'PERCENTAGE_AMOUNT') {
    // 百分比限制在0-100之间
    value = Math.min(Math.max(value, 0), 100);
  } else {
    // 固定金额限制在0-99999之间
    value = Math.min(Math.max(value, 0), 99999);
  }
  
  // 格式化为两位小数
  penaltyData.value.penaltyValue = value.toFixed(2);
  handleChange();
};
</script>

<style lang="scss" scoped>
.penalty-calculation {
  .penalty-form-item {
    margin-bottom: 18px;
    
    :deep(.el-form-item__content) {
      display: flex;
      justify-content: flex-start;
    }
  }

  .penalty-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    
    :deep(.el-input) {
      .el-input__inner {
        text-align: right;
        padding-right: 10px;
      }
    }
    
    :deep(.el-select) {
      .el-input__inner {
        text-align: left;
      }
    }
  }
}

// 修复下拉菜单定位问题
:deep(.penalty-select-dropdown) {
  position: absolute;
  margin-top: 4px;
  
  .el-select-dropdown__item {
    padding: 0 20px;
    height: 34px;
    line-height: 34px;
    text-align: left;
  }
}
</style> 
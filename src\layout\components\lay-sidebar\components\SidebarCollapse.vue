<script setup>
defineProps({
  isActive: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['toggleClick']);

function handleClick() {
  emit('toggleClick');
}
</script>

<template>
  <div @click="handleClick" class="hamburger-container">
    <svg
      :class="['hamburger', isActive ? 'is-active' : '']"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="64"
      height="64"
    >
      <path
        d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"
      />
    </svg>
  </div>
</template>

<style scoped>
.hamburger-container {
  position: absolute;
  bottom: 20px;
  right: 15px;
  padding: 0 15px;
  cursor: pointer;
  transition: background 0.3s;
  z-index: 999;
}

.hamburger {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.hamburger.is-active {
  transform: rotate(180deg);
}
</style>
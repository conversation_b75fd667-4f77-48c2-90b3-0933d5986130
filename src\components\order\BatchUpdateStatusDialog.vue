<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('order.batchUpdateStatus')"
    width="500px"
    destroy-on-close
    class="batch-update-status-dialog"
  >
    <div class="dialog-content">
      <!-- 状态选择区域 -->
      <div class="form-section">
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
          <!-- 变更状态为 -->
          <el-form-item :label="$t('order.changeStatusTo')" prop="status">
            <el-select v-model="form.status" class="status-select" clearable>
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <!-- 标记为坏账 -->
          <el-form-item v-if="showBadDebtOption" :label="$t('order.markAsBadDebt')" prop="badDebt">
            <el-select v-model="form.badDebt" class="status-select" clearable>
              <el-option :label="$t('common.noOperation')" value="" />
              <el-option :label="$t('common.yes')" value="true" />
              <el-option :label="$t('common.no')" value="false" />
            </el-select>
          </el-form-item>
          
          <!-- 标记为完成操作 -->
          <el-form-item :label="$t('order.markAsCompleted')" prop="completed">
            <el-select v-model="form.completed" class="status-select" clearable>
              <el-option :label="$t('common.noOperation')" value="" />
              <el-option :label="$t('common.yes')" value="true" />
              <el-option :label="$t('common.no')" value="false" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 提示信息 -->
      <div class="info-section">
        <el-alert
          type="warning"
          :closable="false"
          show-icon
        >
          <template #title>
            {{ $t('order.batchUpdateWarning') }}
          </template>
          <p>{{ $t('order.batchUpdateImpact') }}</p>
        </el-alert>
      </div>
      
      <!-- 选中的订单列表 -->
      <div class="selected-orders-section">
        <div class="section-header">
          <span>{{ $t('order.selectedOrders') }}:</span>
          <el-badge :value="selectedOrders.length" type="info" />
        </div>
        
        <div class="orders-list">
          <div v-for="order in selectedOrders" :key="order.id" class="order-item">
            <div class="order-info">
              <span class="order-id">{{ order.orderNo }}</span>
              <span class="order-type">{{ getOrderTypeName(order.orderType) }}</span>
            </div>
            <el-tag 
              size="small" 
              effect="light" 
              :type="getStatusTagType(order.status)"
            >
              {{ formatOrderStatus(order.status) }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- 确认选项 -->
      <div class="confirm-section">
        <el-checkbox v-model="confirmAction">
          {{ $t('order.confirmBatchUpdate') }}
        </el-checkbox>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ $t('common.cancel') }}</el-button>
        <el-button 
          type="primary" 
          :disabled="!isFormValid" 
          :loading="submitting"
          @click="handleConfirm"
        >
          {{ $t('common.confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { batchUpdateOrderStatus } from '@/api/order';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  selectedOrders: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'success']);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 表单引用
const formRef = ref(null);

// 表单数据
const form = ref({
  status: '',
  badDebt: '',
  completed: ''
});

// 确认操作选中状态
const confirmAction = ref(false);

// 提交状态
const submitting = ref(false);

// 表单验证规则
const rules = {
  status: [
    { 
      validator: (rule, value, callback) => {
        if (!value && !form.value.badDebt && !form.value.completed) {
          callback(new Error(t('order.selectAtLeastOneOption')));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ]
};

// 状态选项
const statusOptions = [
  { label: t('order.status.overdue'), value: 'OVERDUE' },
  { label: t('order.status.completed'), value: 'COMPLETED' },
  { label: t('order.status.cancelled'), value: 'CANCELLED' }
];

// 是否显示坏账选项（根据当前业务逻辑判断）
const showBadDebtOption = computed(() => {
  // 可以根据状态判断是否显示坏账选项
  return true;
});

// 表单是否有效
const isFormValid = computed(() => {
  return (
    (form.value.status || form.value.badDebt || form.value.completed) && 
    confirmAction.value
  );
});

// 获取订单类型名称
const getOrderTypeName = (type) => {
  const typeMap = {
    'RENT': t('order.type.rent'),
    'SALE': t('order.type.sale'),
    'INSTALLMENT': t('order.type.installment')
  };
  
  return typeMap[type] || type;
};

// 格式化订单状态
const formatOrderStatus = (status) => {
  if (!status) return t('common.unknown');
  
  const statusMap = {
    'NORMAL': t('order.status.normal'),
    'ACTIVE': t('order.status.active'),
    'OVERDUE': t('order.status.overdue'),
    'COMPLETED': t('order.status.completed'),
    'CANCELLED': t('order.status.cancelled')
  };
  
  return statusMap[status] || status;
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  if (!status) return 'info';
  
  const statusMap = {
    'NORMAL': 'success',
    'ACTIVE': 'success',
    'OVERDUE': 'warning',
    'COMPLETED': 'info',
    'CANCELLED': 'danger'
  };
  
  return statusMap[status] || 'info';
};

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  form.value = {
    status: '',
    badDebt: '',
    completed: ''
  };
  confirmAction.value = false;
  
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理确认
const handleConfirm = async () => {
  if (!isFormValid.value) return;
  
  try {
    submitting.value = true;
    
    // 构造更新数据
    const updateData = {
      orderIds: props.selectedOrders.map(order => order.id)
    };
    
    if (form.value.status) {
      updateData.status = form.value.status;
    }
    
    if (form.value.badDebt) {
      updateData.isBadDebt = form.value.badDebt === 'true';
    }
    
    if (form.value.completed) {
      updateData.isCompleted = form.value.completed === 'true';
    }
    
    // 二次确认
    await ElMessageBox.confirm(
      t('order.confirmBatchUpdateMessage', { count: props.selectedOrders.length }),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    );
    
    // 调用批量更新API
    const response = await batchUpdateOrderStatus('batch', updateData);
    
    if (response.code === 1000) {
      ElMessage.success(t('order.batchUpdateSuccess'));
      dialogVisible.value = false;
      resetForm();
      emit('success');
    } else {
      ElMessage.error(response.message || t('order.batchUpdateFailed'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新订单状态失败:', error);
      ElMessage.error(t('order.batchUpdateFailed'));
    }
  } finally {
    submitting.value = false;
  }
};

// 监听选中订单变化
watch(() => props.selectedOrders, (newVal) => {
  if (newVal.length === 0 && dialogVisible.value) {
    dialogVisible.value = false;
    resetForm();
  }
});
</script>

<style lang="scss" scoped>
.batch-update-status-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .dialog-content {
    .form-section {
      margin-bottom: 20px;
      
      .status-select {
        width: 100%;
      }
    }
    
    .info-section {
      margin-bottom: 20px;
      
      :deep(.el-alert__title) {
        font-weight: bold;
      }
      
      p {
        margin: 8px 0 0;
        font-size: 12px;
        color: #999;
      }
    }
    
    .selected-orders-section {
      margin-bottom: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
        border-radius: 4px 4px 0 0;
        font-weight: bold;
      }
      
      .orders-list {
        max-height: 200px;
        overflow-y: auto;
        padding: 10px;
        
        .order-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #ebeef5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .order-info {
            display: flex;
            align-items: center;
            
            .order-id {
              font-weight: bold;
              margin-right: 8px;
            }
            
            .order-type {
              font-size: 12px;
              color: #909399;
              background-color: #f0f2f5;
              padding: 2px 6px;
              border-radius: 2px;
            }
          }
        }
      }
    }
    
    .confirm-section {
      margin-top: 30px;
      padding: 15px;
      background-color: #fff9f9;
      border-radius: 4px;
      border-left: 3px solid #f56c6c;
      
      :deep(.el-checkbox__label) {
        font-weight: bold;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style> 
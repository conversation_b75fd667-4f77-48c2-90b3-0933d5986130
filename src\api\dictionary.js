import request from '@/utils/request'; // Assuming you have a request utility

const BASE_URL = '/crm-service/api/dict/fields';

// 1. 分页查询字段
export function getDictFields(query) {
  return request({
    url: BASE_URL,
    method: 'GET',
    params: query
  });
}

// 2. 新增字段
export function addDictField(data) {
  return request({
    url: BASE_URL,
    method: 'POST',
    data
  });
}

// 3. 更新字段
export function updateDictField(id, data) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data
  });
}

// 4. 删除字段
export function deleteDictField(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE'
  });
}

// 5. 获取模块字段列表
export function getModuleDictFields(module) {
  return request({
    url: `${BASE_URL}/module/${module}`,
    method: 'GET'
  });
}

// 6. 启用/禁用字段
export function updateDictFieldStatus(id, enabled) {
  return request({
    url: `${BASE_URL}/${id}/status`,
    method: 'PUT',
    params: { enabled }
  });
}

// 7. 更新字段排序
export function updateDictFieldSortOrder(id, sortOrder) {
  return request({
    url: `${BASE_URL}/${id}/sort`,
    method: 'PUT',
    params: { sortOrder }
  });
}

// 8. 批量更新字段排序
export function batchUpdateDictFieldSortOrder(data) {
  return request({
    url: `${BASE_URL}/batch-sort`,
    method: 'PUT',
    data
  });
}

// 9. 检查字段是否可删除
export function checkDictFieldCanDelete(id) {
  return request({
    url: `${BASE_URL}/${id}/can-delete`,
    method: 'GET'
  });
} 
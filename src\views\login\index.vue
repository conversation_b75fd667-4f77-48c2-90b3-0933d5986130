<template>
  <div class="login-container">
    <!-- 左上角 Logo -->
    <div class="corner-logo">
      <img src="@/assets/logo_white.png" alt="EasyControl" />
    </div>

    <!-- 登录表单 -->
    <div class="login-form-container">
      <div class="login-form-wrapper">
        <!-- Logo -->
        <div class="form-logo">
          <img src="@/assets/logo.png" alt="EasyControl" />
        </div>

        <!-- 登录标题 -->
        <div class="login-title">
          <h1>Login 登录</h1>
        </div>

        <!-- 表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="username">
            <div class="input-label">Username 用户名</div>
            <el-input
              v-model="loginForm.username"
              placeholder=""
            />
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-label">Password 密码</div>
            <el-input
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder=""
            >
              <template #suffix>
                <el-icon 
                  class="password-toggle-icon" 
                  @click="togglePasswordVisibility"
                >
                  <View v-if="showPassword" />
                  <Hide v-else />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="code">
            <div class="input-label">Captcha 验证码</div>
            <div class="captcha-wrapper">
              <el-input
                v-model="loginForm.code"
                style="width: calc(100% - 120px)"
              />
              <img
                :src="captchaImg"
                class="captcha-img"
                @click="refreshCaptcha"
                alt="验证码"
              />
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              <i class="el-icon-lock"></i>
              Login登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View, Hide } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
import { getCaptcha, login } from '@/api/user'
import { setToken, isSuccess } from '@/utils/auth'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loginFormRef = ref(null)
const loading = ref(false)
const captchaImg = ref('')
const uuid = ref('')
const showPassword = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  code: '',
  uuid: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 获取验证码
const refreshCaptcha = async () => {
  try {
    console.log('[Login] 开始获取验证码...')
    const response = await getCaptcha()
    console.log('[Login] 验证码API响应:', response)
    console.log('[Login] 响应headers:', response.headers)
    
    // 尝试多个可能的header字段名称
    const code = response.headers['code'] || 
                 response.headers['Code'] || 
                 response.headers['uuid'] || 
                 response.headers['captcha-id'] || 
                 response.headers['captcha-uuid'] ||
                 response.headers['x-captcha-id'] ||
                 response.headers['verification-code'];
    
    console.log('[Login] 从headers中获取的code:', code)
    
    if (code) {
      // 将响应头中的code值设置为uuid
      uuid.value = code
      loginForm.uuid = code
      console.log('[Login] 设置uuid成功:', code)
      
      // 将Blob对象转换为URL
      const blob = new Blob([response.data], { type: 'image/jpeg' })
      captchaImg.value = URL.createObjectURL(blob)
      console.log('[Login] 验证码图片设置成功')
    } else {
      console.error('[Login] 响应headers中没有找到验证码ID字段')
      console.log('[Login] 可用的headers:', Object.keys(response.headers || {}))
      ElMessage.error('获取验证码失败：响应中缺少验证码ID')
    }
  } catch (error) {
    console.error('[Login] 获取验证码失败:', error)
    ElMessage.error('获取验证码失败：' + (error.message || '网络错误'))
  }
}

// 登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true

    // 确保验证码和uuid已设置
    if (!loginForm.code || !loginForm.uuid) {
      ElMessage.error('请先获取验证码')
      refreshCaptcha()
      loading.value = false
      return
    }

    const res = await userStore.login(loginForm)
    setToken(res.token)
    ElMessage.success('登录成功')
    
    // 获取redirect参数，如果有就跳转到指定页面，否则跳转到首页
    const redirect = route.query.redirect
    if (redirect && typeof redirect === 'string') {
      router.push(decodeURIComponent(redirect))
    } else {
      router.push('/')
    }
  } catch (error) {
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取验证码
onMounted(() => {
  console.log('[Login] 页面加载，开始获取验证码...')
  refreshCaptcha()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/bg.png') no-repeat center center;
  background-size: cover;

  // 左上角 logo
  .corner-logo {
    position: fixed;
    top: -40px;
    left: 20px;
    z-index: 10;
    
    img {
      width: 200px;
      height: auto;
    }
  }

  // 登录表单容器
  .login-form-container {
    width: 400px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .login-form-wrapper {
    padding: 0 30px 30px;

    // 表单内 logo
    .form-logo {
      text-align: center;
      margin: 10px auto 10px;
      
      img {
        width: 180px;
        height: auto;
        display: inline-block;
      }
    }

    // 登录标题
    .login-title {
      text-align: center;
      margin-bottom: 20px;

      h1 {
        font-size: 24px;
        color: #333;
        font-weight: 500;
      }
    }

    // 登录表单
    .login-form {
      .input-label {
        margin-bottom: 8px;
        color: #333;
        font-size: 14px;
      }

      .captcha-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;

        .captcha-img {
          height: 40px;
          cursor: pointer;
        }
      }

      :deep(.el-input__inner) {
        height: 40px;
        line-height: 40px;
      }

      .password-toggle-icon {
        cursor: pointer;
        color: #c0c4cc;
        transition: color 0.3s;

        &:hover {
          color: #409eff;
        }
      }

      .login-button {
        width: 100%;
        height: 40px;
        font-size: 16px;
        margin-top: 10px;
        background-color: #2196f3;
        border-color: #2196f3;

        &:hover, &:focus {
          background-color: #1976d2;
          border-color: #1976d2;
        }
      }
    }
  }
}
</style> 
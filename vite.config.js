import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import alias from '@rollup/plugin-alias';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  console.log("VITE_APP_BASE_API:",env.VITE_APP_BASE_API);
  console.log("VITE_APP_CRM_SERVICE_BASE_API:",env.VITE_APP_CRM_SERVICE_BASE_API);
  console.log("VITE_APP_PUB_MDM_API:",env.VITE_APP_PUB_MDM_API);

  return {
    base: '/crm-web/',
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      alias({
        entries: [
          { find: 'dayjs', replacement: path.resolve(__dirname, 'node_modules/dayjs') },
          { find: /^dayjs\/(.*)/, replacement: path.resolve(__dirname, 'node_modules/dayjs/$1') }
        ]
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        'dayjs': path.resolve(__dirname, 'node_modules/dayjs'),
        'dayjs/locale': path.resolve(__dirname, 'node_modules/dayjs/locale'),
        'dayjs/plugin': path.resolve(__dirname, 'node_modules/dayjs/plugin')
      }
    },
    optimizeDeps: {
      include: ['dayjs', 'dayjs/locale/zh-cn', 'dayjs/plugin/updateLocale', 
                'dayjs/plugin/relativeTime', 'dayjs/plugin/isSameOrBefore', 
                'dayjs/plugin/isSameOrAfter', 'dayjs/plugin/customParseFormat', 
                'dayjs/plugin/isBetween', 'dayjs/plugin/localeData', 'dayjs/plugin/advancedFormat',
                'dayjs/plugin/duration', 'dayjs/plugin/weekOfYear', 'dayjs/plugin/weekYear',
                'dayjs/plugin/quarterOfYear', 'dayjs/plugin/arraySupport'],
      force: true
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
          silenceDeprecations: ["legacy-js-api"],
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: true,
      proxy: {
        '/cloud-service': {
          target: env.VITE_APP_BASE_API,
          changeOrigin: true,
          rewrite: (path) => path
        },
        '/crm-service': {
          target: env.VITE_APP_CRM_SERVICE_BASE_API,
          changeOrigin: true,
          rewrite: (path) => path
        },
        '/pub-mdm-platform': {
          target: env.VITE_APP_PUB_MDM_API,
          changeOrigin: true,
          rewrite: (path) => path
        },
        '/cos-proxy': {
          target: 'https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/cos-proxy/, '')
        }
      }
    }
  };
});
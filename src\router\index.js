import { createRouter, createWebHistory } from 'vue-router';
import Layout from '@/layout/index.vue';

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        component: () => import('@/views/home/<USER>'),
        name: 'Dashboard',
        meta: {
          title: 'menus.dashboard',
          icon: 'el-icon-s-home',
          affix: true,
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: 'menus.pureHome',
      requiresAuth: false
    }
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/list',
    name: 'Order',
    meta: { title: 'menus.order', icon: 'Document' },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/index.vue'),
        meta: { title: 'menus.orderList', icon: 'List' }
      }
    ]
  },
  {
    path: '/device',
    component: Layout,
    redirect: '/device/list',
    name: 'Devi<PERSON>',
    meta: { title: 'menus.device', icon: 'Cellphone' },
    children: [
      {
        path: 'list',
        name: '<PERSON>ceList',
        component: () => import('@/views/device/index.vue'),
        meta: { title: 'menus.deviceList', icon: 'Document' }
      }
    ]
  },
  {
    path: '/customer',
    component: Layout,
    redirect: '/customer/list',
    meta: {
      title: 'menus.customer',
      icon: 'UserFilled'
    },
    children: [
      {
        path: 'list',
        name: 'CustomerList',
        component: () => import('@/views/customer/index.vue'),
        meta: {
          title: 'menus.customerList',
          icon: 'List'
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/company-info',
    name: 'System',
    meta: { title: 'menus.system', icon: 'Setting' },
    children: [
      {
        path: 'company-info',
        name: 'CompanyInfo',
        component: () => import('@/views/system/CompanyInfo.vue'),
        meta: { title: 'system.companyInfo', icon: 'OfficeBuilding' }
      },
      {
        path: 'role',
        name: 'RoleManagement',
        component: () => import('@/views/system/RoleManagement.vue'),
        meta: { title: 'system.roleManagement', icon: 'UserFilled' }
      },
      {
        path: 'account',
        name: 'AccountManagement',
        component: () => import('@/views/system/AccountManagement.vue'),
        meta: { title: 'system.accountManagement', icon: 'User' }
      },
      {
        path: 'dictionary',
        name: 'DictionaryManagement',
        component: () => import('@/views/system/DictionaryManagement.vue'),
        meta: { title: 'dictionary.title', icon: 'List' }
      }
    ]
  }
];

/**
 * Create router instance with history mode
 */
const router = createRouter({
  history: createWebHistory('/crm-web/'),
  routes,
  scrollBehavior (to, from, savedPosition) {
    // Always scroll to top when navigating to a new route
    return savedPosition || { top: 0 };
  }
});

export default router;
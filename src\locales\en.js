export default {
    menus: {
      pureHome: 'Home',
      dashboard: 'Dashboard',
      device: 'Device',
      deviceList: 'Device List',
      order: 'Orders',
      orderList: 'Order List',
      customer: 'Customer',
      system: 'System',
      customerList: 'Customer List'
    },
    common: {
      home: 'Home',
      search: 'Search',
      reset: 'Reset',
      day: 'Day',
      confirm: 'Confirm',
      cancel: 'Cancel',
      create: 'Create',
      edit: 'Edit',
      delete: 'Delete',
      export: 'Export',
      deleteSuccess: 'Delete successfully',
      detail: 'Detail',
      details: 'Details',
      total: 'Total {count} items',
      loading: 'Loading...',
      save: 'Save',
      download: 'Download',
      view: 'View',
      createTime: 'Create Time',
      updateTime: 'Update Time',
      status: 'Status',
      operations: 'Operations',
      all: 'All',
      warning: 'Warning',
      downloading: 'Downloading...',
      close: 'Close',
      selectDateRange: 'Select Date Range',
      selectDate: 'Select Date',
      selectTime: 'Select Time',
      clear: 'Clear',
      to: 'to',
      expand: 'Expand',
      collapse: 'Collapse',
      moreOptions: 'More Options',
      dateRange: 'Date Range',
      prev: 'Previous',
      index: 'Index',
      next: 'Next',
      submit: 'Submit',
      refresh: 'Refresh',
      columnSetting: 'Column Settings',
      fetchFailed: 'Failed to fetch data',
      retry: 'Retry',
      uploading: 'Uploading...',
      exporting: 'Exporting...',
      exportSuccess: 'Export successful',
      uploadSuccess: 'Upload successful',
      uploadFailed: 'Upload failed',
      show: 'Show',
      hide: 'Hide',
      noData: 'No Data',
      noAttachments: 'No Attachments',
      formInvalid: 'Form is invalid, please check your input.',
      more: 'More',
      yes: 'Yes',
      no: 'No',
      timeInfo: 'Time Information',
      id: 'ID',
      imageError: 'Image failed to load',
      creator: 'Admin',
      batchOperations: 'Batch Operations',
      pleaseSelect: 'Please select',
      uploadExceedTip: 'Upload failed: Each file must not exceed 5MB',
      fileDownloadFailed: 'File download failed',
      uploadInvalidFormat: 'Upload failed: Only JPG, JPEG, PNG, PDF formats are supported',
      startDate: 'Start Date',
      endDate: 'End Date',
      remarks: 'Remarks'
    },
    device: {
      basicInfo: 'Basic Information',
      identificationInfo: 'Identification Information',
      additionalInfo: 'Additional Information',
      brand: {
        placeholder: 'Please enter device brand',
        label: 'Device Brand',
        Apple: 'Apple',
        Samsung: 'Samsung',
        Huawei: 'Huawei',
        Xiaomi: 'Xiaomi',
        OPPO: 'OPPO',
        vivo: 'vivo',
        other: 'Other'
      },
      delete:{
        confirmSingle: 'Are you sure to delete this device?',
        title: 'Delete Device'
      },
      timeInfo: 'Time Information',
      deviceInfoLabel: 'Device Information',
      autoGenerated: 'Auto Generated',
      deviceInfoHint: 'Auto generated based on brand+model+model number, e.g. Apple iPhone 13 Pro (A2639)',
      deviceInfoPlaceholder: 'Device information will be auto-generated, no need to input manually',
      sn:{
        label: 'Device Serial Number',
        placeholder: 'Please enter device serial number',
      },
      batchChangeStatus:{
        title: 'Batch Change Status',
        dialogTitle: 'Batch Change Status',
        confirmButton: 'Confirm',
        cancelButton: 'Cancel',
        confirmationCheckbox: 'Note: Devices with associated orders (in use) may not be able to have their status changed',
        infoMessage: 'You will batch change {count} devices',
        changeToLabel: 'Change to',
        targetStatusPlaceholder: 'Please select target status',
      },
      model: 'Model',
      modelPlaceholder: 'Please enter device model',
      modelNumber: 'Model Number',
      modelNumberPlaceholder: 'Please enter model number',
      color: {
        label: 'Device Color',
        black: 'Black',
        white: 'White',
        silver: 'Silver',
        gold: 'Gold',
        blue: 'Blue',
        red: 'Red',
        green: 'Green',
        purple: 'Purple',
        gray: 'Gray',
        brown: 'Brown',
        placeholder: 'Enter device color'
      },
      syncStatus: "Sync Status",
      status: {
        label: 'Device Status',
        IN_STOCK: 'In Stock',
        IN_USE: 'In Use',
        UNDER_REPAIR: 'Under Repair',
        IN_TRANSIT: 'In Transit',
        SCRAPPED: 'Scrapped',
        LOST: 'Lost',
        PREPARING: 'Preparing',
        SOLD: 'Sold'
      },
      serialNumber: 'Serial Number',
      serialNumberPlaceholder: 'Please enter device serial number',
      serialNumberTip: 'Serial number can be found on the back of the device or in settings',
      imei1: 'IMEI 1',
      imei2: 'IMEI 2',
      imeiPlaceholder: 'Enter a valid IMEI1 (15 digits)',
      imei2Placeholder: 'Enter a valid IMEI2 (15 digits)',
      remarks: 'Remarks',
      remarksPlaceholder: 'Please enter device-related remarks',
      costAmount: 'Cost Amount',
      attachments: 'Attachments',
      uploadText: 'Drag and drop files here to upload',
      uploadTip: 'Supports image (JPG/PNG/JPEG/PDF) formats, single file max 5MB',
      uploadLimitExceeded: 'Upload failed: You can upload up to 5 files only',
      fetchListFailed: 'Failed to fetch device list',
      fetchDetailFailed: 'Failed to fetch device details',
      fetchDetailFailedRetry: 'Failed to fetch device details, please try again',
      selectDeviceForBatchOperation: 'Please select devices to perform batch operation',
      rules: {
        brandRequired: 'Please select a brand',
        modelRequired: 'Please enter device model',
        modelFormat: 'Please enter 8-20 letters, numbers, or hyphens.',
        modelNumberRequired: 'Please enter model number',
        colorRequired: 'Please select device color',
        statusRequired: 'Please select device status',
        serialNumberRequired: 'Please enter device serial number',
        imei1Required: 'Please enter IMEI number',
        imei1Format: 'Please enter a valid IMEI1 (15 digits).',
        imei2Format: 'Please enter a valid IMEI2 (15 digits).',
        imei2Required: 'Please enter IMEI2 number',
        imei1DuplicateOnImei2: 'The same IMEI2 already exists in the enterprise. Please check and refill.',
        imei2DuplicateOnImei1: 'The same IMEI1 already exists in the enterprise. Please check and refill.',
        costAmountFormat: 'Cost amount must be a number',
        costAmountPlaceholder: 'Please enter a valid cost amount (an integer between 0 and 9,999,999).',
        costAmountRequired: 'Please enter a valid cost amount (an integer between 0 and 9,999,999).',
        costAmountInvalid: 'Please enter a valid cost amount (an integer between 0 and 9,999,999).',
        pendingSync: 'Pending Sync',
        snRequired: 'Please enter the device serial number',
        snInvalid: 'Please enter a valid Serial Number containing 8–20 characters, including letters and numbers.',
      },
      search: {
        placeholder: 'Device No./Name/SN/IMEI',
        status: 'Device Status',
        brand: 'Device Brand',
        color: 'Device Color',
        syncStatus: 'Sync Status',
        synced: 'Synced',
        pendingSync: 'Pending Sync'
      },
      list: {
        deviceNumber: 'Device Number',
        brandModel: 'Device Info',
        filenamePrefix: 'DeviceList',
        deviceInfo: 'Device Info',
        status: 'Status',
        serialNumber: 'Serial Number',
        imei1: 'IMEI1',
        imei2: 'IMEI2',
        syncStatus: 'Sync Status',
        syncTime: 'Sync Time',
        createTime: 'Create Time',
        orderNo: 'Order No.',
        operations: 'Operations',
        export: 'Export Devices',
        batchDelete: 'Batch Delete',
        reset: 'Reset'
      },
      buttons: {
        create: 'Add Device',
        import: 'Import Devices',
        export: 'Export Devices',
        batchDelete: 'Batch Delete',
        reset: 'Reset'
      },
      deviceName: 'Model Number',
      type: {
        label: 'Device Type',
        phone: 'Phone',
        tablet: 'Tablet',
        laptop: 'Laptop'
      },
      placeholder: {
        brand: 'Please select device brand',
        costAmount: 'Please enter cost amount',
        model: 'Please enter the device model, for example: 12 Pro.',
        modelNumber: 'Please enter the model number, for example: A2639.',
        color: 'Please select color',
        imei1: 'Please enter IMEI1',
        imei2: 'Please enter IMEI2'
      },
      export: {
        csv: 'Export as CSV',
        excel: 'Export as Excel',
        error: 'Export failed',
        csvSuccess: 'CSV exported successfully',
        excelSuccess: 'Excel exported successfully',
        exportSuccess: 'Device list exported successfully. File download completed!',
        emptyListError: 'Device list is empty and cannot be exported.',
        exportFailedError: 'Failed to export the device list. Please try again later.'
      },
      batchDelete: {
        title: 'Batch Delete',
        dialogTitle: 'Batch Delete Devices',
        confirm: 'Are you sure you want to delete the selected {count} devices?',
        confirmMessage: 'Are you sure you want to delete the selected {count} devices?',
        irreversibleWarning: 'This action cannot be undone!',
        selectedDevicesTitle: 'Selected Devices:',
        confirmationCheckbox: 'I have confirmed the above information and understand that some devices may not be able to change status due to business restrictions',
        confirmButton: 'Confirm Deletion',
        success: 'Devices deleted successfully.',
        error: 'Failed to delete devices. Please try again later.',
        deleteErrorPartial: 'Some devices cannot be deleted. Please check the device status or linked orders.'
      },
      syncTime: 'Sync Time',
      createTime: 'Create Time',
      orderNo: 'Order No.',
      operations: 'Operations',
      deviceInfo: 'Device Information',
      preview: 'File Preview',
      notFound: 'Device not found',
      information: 'Device Information',
      pleaseSelect: 'Please select a device',
      pleaseSearchOrCreate: 'Please search or create a device',
      searching: 'Searching for device...',
      noAttachments: 'No attachments',
      createSuccess: 'Device created successfully',
      searchFailed: 'Device search failed',
      colorInfo: 'Color',
      deviceDetails: 'Device Details',
      details: {
        title: 'Device Details',
        placeholder: 'Enter device details'
      },
      info: {
        title: 'Device Details'
      },
      action: {
        create: 'Create New Device'
      },
      table: {
        number: 'Device Info',
        info: 'Device Info',
        sn: 'Serial Number',
        status: 'Status'
      },
      deviceInfoTab: {
        uploadDevicePhoto: 'Upload Device Photo',
        attachmentName: 'File Name',
        attachmentActions: 'Actions',
        uploadHint: 'Please upload necessary files such as actual device photos or inspection reports.',
        imeiExplanationTitle: 'IMEI Explanation',
        imeiExplanationText: 'IMEI (International Mobile Equipment Identity) is a unique number to identify mobile phones.'
      },
      description: 'Device Description',
      accessories: 'Device Accessories',
      createTimeRange: 'Creation Time Range',
      error: {
        snExists: 'This serial number already exists, please enter another serial number.',
        imei1Exists: 'This IMEI1 already exists, please enter another IMEI1.',
        imei2Exists: 'This IMEI2 already exists, please enter another IMEI2.'
      }
    },
    dialog: {
      warning: 'Warning',
      confirm: 'Confirm',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      save: 'Save',
      createDevice: 'Create Device',
      editDevice: 'Edit Device',
      deleteConfirm: 'Are you sure to delete this device?',
      fileLimit: 'Maximum 5 files allowed',
      // warning: {
      //   fileLimit: 'Maximum 5 files allowed'
      // },
      success: {
        create: 'Created successfully',
        edit: 'Updated successfully',
        delete: 'Deleted successfully',
        logout: 'Logged out successfully',
        upload: 'Uploaded successfully'
      },
      error: {
        create: 'Failed to create',
        edit: 'Failed to update',
        delete: 'Failed to delete',
        export: 'Failed to export',
        logout: 'Logout failed, please try again later',
        upload: 'Upload failed',
        fileType: 'Unsupported file type',
        fileSize: 'File size cannot exceed 10MB'
      },
      createCustomer: 'Create New Customer',
      createOrder: 'Create Order'
    },
    header: {
      language: 'Language',
      logout: 'Logout',
      logoutConfirm: 'Are you sure to logout?'
    },
    buttons: {
      pureLoginOut: 'Logout',
      pureAccountSettings: 'Account Settings',
      pureOpenSystemSet: 'Open System Settings',
      pureBackTop: 'Back to Top',
      fullScreen: 'Full Screen',
      cancelFullScreen: 'Exit Full Screen',
      language: 'Language'
    },
    settings: {
      title: 'System Settings',
      themeMode: 'Theme Mode',
      themeColor: 'Theme Color',
      interfaceSettings: 'Interface Settings',
      fixedHeader: 'Fixed Header',
      showLogo: 'Show Logo',
      showTags: 'Show Tags',
      showFooter: 'Show Footer'
    },
    search: {
      title: 'Menu Search',
      placeholder: 'Enter keywords to search',
      noResults: 'No matching menu found',
      tip: 'Use up/down arrows and enter to select'
    },
    notice: {
      title: 'Notifications',
      notification: 'Notification',
      message: 'Message',
      todo: 'Todo',
      total: 'Total {count} items',
      markAllAsRead: 'Mark all as read',
      empty: 'No messages',
      fetchFailed: 'Failed to fetch messages'
    },
    status: {
      pureLoad: 'Loading...'
    },
    customer: {
      customerNo: 'Customer No.',
      name: 'Name',
      email: 'Email',
      idInfo: 'ID Information',
      addressInfo: 'Address Information',
      phone: 'Phone',
      idType: 'ID Type',
      idNumber: 'ID Number',
      residenceAddress: 'Residence Address',
      mailingAddress: 'Mailing Address',
      delete:{
        success: 'Delete successfully',
        failed: 'Delete failed',
        confirm: 'Are you sure to delete this customer?'
      },
      details: {
        title: 'Customer Details',
        basicInfo: 'Basic Information',
        idInfo: 'ID Information',
        orderInfo: 'Order Information',
        attachments: 'Attachments',
        timeInfo: 'Time Information',
        noOrders: 'No associated orders',
        noAttachments: 'No attachments'
      },
      placeholder: {
        firstName: 'Please enter first name',
        lastName: 'Please enter last name',
        email: 'Please enter email address',
        phone1: 'Please enter primary phone number',
        phone2: 'Please enter alternate phone number',
        idNumber: 'Please enter ID number',
        licenseAddress: 'Please enter ID address',
        address: 'Please enter contact address',
        search: 'Search by email or phone number'
      },
      rules: {
        emailRequired: 'Please enter customer email',
        emailFormat: 'Please enter a valid email address',
        emailLength: 'Please enter a valid 5-50 email address',
        firstNameRequired: 'Please enter a valid 1-30 first name',
        lastNameRequired: 'Please enter a valid 1-30 last name',
        firstNameLength: 'Please enter a valid 1-30 first name',
        lastNameLength: 'Please enter a valid 1-30 last name',
        lastNameInvalidChars: 'Last name cannot contain special characters',
        idTypeRequired: 'Please select an ID type',
        idNumberRequired: 'Please enter a valid ID number',
        idNumberLength: 'ID number must be between 6 and 20 characters',
        idNumberInvalid: 'ID number can only contain letters and numbers',
        licenseAddressRequired: 'Please enter a valid 1-100 license address',
        contactAddressRequired: 'Please enter a valid 1-100 contact address',
        phoneRequired: 'Please enter phone number',
        phone1Required: 'Please enter phone number',
        phone1FormatInvalid: 'Please enter a 10-15 valid phone number',
        phone2FormatInvalid: 'Please enter a 10-15 valid phone number',
        emailExists: 'This email is already registered. Please use another email.',
        phoneExists: 'This phone number is already registered. Please use another number.'
      },
      idTypes: {
        idCard: 'ID Card',
        passport: 'Passport',
        driverLicense: 'Driver License'
      },
      status: {
        linked: 'Linked',
        unlinked: 'Unlinked'
      },
      filter: {
        linked: 'Linked Orders',
        unlinked: 'Unlinked Orders',
        normal: 'Normal',
        overdue: 'Has Overdue',
        overdueRange: 'Overdue Count Range',
        minValue: 'Min Value',
        maxValue: 'Max Value',
        error: {
          minMaxOverdue: 'Min overdue times cannot be greater than max overdue times.'
        }
      },
      dialog: {
        createCustomer: 'Create Customer',
        editCustomer: 'Edit Customer'
      },
      uploadText: 'Click or drag file to upload',
      uploadTip: 'Support jpg, jpeg、png, pdf format, single file not exceed 10MB',
      search: {
        placeholder: 'Name/ID No./Phone No.',
        idType: 'ID Type',
        orderRelation: 'Order Relation',
        status: 'Customer Status'
      },
      searchPlaceholder: 'Enter customer no./name/phone',
      searchKeywordRequired: 'Please enter customer search keyword',
      searchSuccess: 'Customer found successfully',
      notSelected: 'No customer selected',
      table: {
        code: 'Customer Code',
        name: 'Name',
        orderId: 'Order ID',
        idInfo: 'ID Info',
        phone: 'Phone',
        email: 'Email',
        orderStatus: 'Order Status',
        overdueCount: 'Overdue Count',
        createTime: 'Create Time',
        updateTime: 'Update Time',
        operation: 'Operation'
      },
      button: {
        add: 'Add Customer',
        export: 'Export',
        batchOperation: 'Batch Operation',
        batchDelete: 'Batch Delete',
        search: 'Search',
        reset: 'Reset',
        save: 'Save',
        cancel: 'Cancel'
      },
      export: {
        success: 'Export successful',
        error: 'Export failed, please try again',
        selected: 'Export selected data',
        all: 'Export all data',
        csv: 'Export CSV',
        excel: 'Export Excel',
        selectTip: 'Please select customers to export'
      },
      form: {
        firstName: 'First Name',
        lastName: 'Last Name',
        idType: 'ID Type',
        idNumber: 'ID Number',
        licenseAddress: 'Residence Address',
        contactAddress: 'Mailing Address',
        sameAsLicense: 'Same as Residence Address',
        phone1: 'Phone',
        phone2: 'Alternative Phone',
        attachments: 'Attachments',
        uploadTip: 'Click or drag file to upload',
        uploadDesc: 'Support jpg, png, pdf format, single file should not exceed 10MB'
      },
      pagination: {
        total: 'Total {total} items',
        goto: 'Go to',
        pageClassifier: 'page',
        pagesize: ' items/page'
      },
      information: 'Customer Information',
      newCustomer: 'New Customer',
      notFound: 'Customer not Found',
      searching: 'Searching for customer...',
      selectTip: 'Please search and select a customer, or create a new one',
      createSuccess: 'Customer created successfully',
      createFailed: 'Customer creation failed',
      searchFailed: 'Customer search failed',
      action: {
        create: 'Create Customer'
      },
      basicInfo: 'Basic Information',
      email: 'Email',
      form: {
        firstName: 'First Name',
        lastName: 'Last Name',
        phone1: 'Phone',
        phone2: 'Alternative Phone',
        idType: 'ID Type',
        idNumber: 'ID Number',
        licenseAddress: 'License Address',
        contactAddress: 'Contact Address',
        sameAsLicense: 'Same as license address'
      },
      batch:{
        noSelection: 'Please select customers to operate',
        deleteConfirm: 'Are you sure you want to delete {count} selected customers? This action cannot be undone!',
        deleteSuccess: 'Batch deletion successful',
        deleteFailed: 'Batch deletion failed'
      },
      placeholder: {
        email: 'Please Enter Email',
        firstName: 'Please enter First Name',
        lastName: 'Please enter Last Name',
        phone: 'Please enter phone number',
        phone2: 'Please enter alternative phone number',
        idType: 'Please select ID type',
        idNumber: 'Please enter ID Number',
        licenseAddress: 'Please enter license address',
        contactAddress: 'Please enter contact address',
        costAmount: 'Please enter cost amount'
      },
      idTypes: {
        idCard: 'ID Card',
        passport: 'Passport',
        driverLicense: 'Driver License'
      }
    },
    order: {
      orderNo: 'Order No.',
      statusAndProgress: 'Order Status and Progress',
      financialOverview: 'Financial Overview',
      periodLease: 'Period Lease',
      periodInfo: 'Period Information',
      information: 'Order Information',
      yuan: 'yuan',
      management: 'Order Management',
      rentPeriod: 'Rent Period',
      financialStatusInfo: 'Financial Status Information',
      endDate: 'End Date',
      months: 'months',
      deviceInfo: 'Device Info',
      penaltyFine: 'Late payment penalty',
      penaltyCalculationMethod: 'Penalty calculation method',
      paymentDetails: 'Payment Details',
      penaltyCalculationMethods: {
        daily: 'Daily interest',
        monthly: 'Monthly interest',
        weekly: 'Weekly interest'
      },
      status: {
        active: 'Active',
        completed: 'Completed',
        overdue: 'Overdue',
        cancelled: 'Cancelled'
      },
      createDate: 'Create Date',
      orderType: 'Order Type',
      orderStatus: 'Order Status',
      paymentStatus: 'Payment Status',
      searchPlaceholder: 'Please enter order no./customer name/phone/serial number/IMEI',
      steps: {
        selectDevice: 'Select Device',
        customerInfo: 'Customer Info',
        orderInfo: 'Order Info',
        paymentPlan: 'Payment Plan',
        preview: 'Preview'
      },
      types: {
        rental: 'Rental',
        sale: 'Sale',
        installment: 'Installment'
      },
      statusInfo: 'Order Status',
      basicInfo: 'Order Details',
      attachments: 'Order Attachments',
      noAttachments: 'No Attachments',
      orderDate: 'Order Date',
      financialStatus: {
        NORMAL: 'Normal',
        OVERDUE: 'Overdue',
        SETTLED: 'Settled',
        UNKNOWN: 'Unknown',
        COMPLETED: 'Completed',
        NOT_STARTED: 'Not Started',
        IN_PROGRESS: 'In Progress',
        DEFAULTED: 'Defaulted'
      },
      overdueCount: 'Overdue Count',
      overdueAmount: 'Overdue Amount',
      firstPaymentDate: 'First Payment Date',
      currentInstallment: 'Current Installment',
      depositStatus: 'Deposit Status',
      nextPaymentDate: 'Next Payment Date',
      penaltyAmount: 'Penalty Amount',
      penalty: 'Penalty',
      statusOverview: 'Order Status Overview',
      progressInfo: 'Order Progress',
      periodProgress: 'Payment Period Progress',
      completed: 'Completed',
      currentPeriodLabel: 'Current Period',
      paidAmount: 'Paid Amount',
      remainingAmount: 'Remaining Amount',
      orderHistory: 'Order History',
      activity: {
        created: 'Order Created',
        paymentReceived: 'Payment Received',
        statusChanged: 'Status Changed',
        completed: 'Order Completed',
        cancelled: 'Order Cancelled',
        paymentOverdue: 'Payment Overdue',
        paymentReminder: 'Payment Reminder'
      },
      statusUpdate: 'Status update',
      statusUpdates: {
        title: 'Status update',
        markAsBadDebt: 'Mark as Bad Debt',
        status: 'Order Status',
        badDebtTitle: "Confirm Mark as Bad Debt",
        batchTitle: 'Batch update order status',
        badDebtConfirm: 'After marking as bad debt, penalty calculation will stop and the order status will be changed to Completed. Are you sure you want to proceed?',
        recalculatePenaltyQuestion: 'Would you like to recalculate overdue penalties during the bad debt period?',
        onlyOverdueCanMark: 'Only overdue orders can be marked as bad debt',
        badDebtCheckboxDescription: 'Check to mark the order as bad debt, status will change to "Bad Debt"',
        success: 'Status updated successfully',
        failed: 'Status update failed',
        confirm: 'Confirm',
        cancel: 'Cancel',
        noAction: 'No action',
        confirmUpdate:  'Confirm update',
        selectedOrdersCount: 'You will batch update the status of {count} orders',
        changeStatusTo: 'Change status to',
        yes: 'Yes',
        no: 'No',
        warning: 'Warning: Status changes, marking as bad debt, or completion may affect billing, payment, and other related business of the order',
        cancelBadDebtTitle: 'Cancel Bad Debt Marking'
      },
      markAsBadDebt: 'Mark as Bad Debt',
      badDebtDescription: 'When checked, the order will be marked as bad debt and status will change to "Bad Debt"',
      statusUpdateSuccess: 'Order status updated successfully',
      statusUpdateFailed: 'Failed to update order status',
      startDate: 'Start Date',
      duration: 'Duration',
      remarks: 'Remarks',
      selectStartDate: 'Select Start Date',
      remarksPlaceholder: 'Please enter remarks for the order',
      orderBasicInfo: 'Order Basic Info',
      paymentInfo: 'Payment Info',
      initialPayment: 'Initial Payment',
      periodicPayment: 'Periodic Payment',
      numberOfInstallments: 'Number of Installments',
      enterNumberOfInstallments: 'Enter number of installments',
      periodicLength: 'Periodic Length',
      enterPeriodicLength: 'Enter periodic length',
      day: 'Day',
      week: 'Week',
      month: 'Month',
      year: 'Year',
      totalAmount: 'Total Amount',
      otherFees: 'Other Fees Information',
      backToList: 'Back to Order List',
      penaltyType: 'Overdue Calculation',
      penaltyTypes: {
        daily: 'Daily Calculation',
        fixed: 'Fixed Amount',
        percentage: 'Percentage',
        none: 'No Penalty'
      },
      selectPenaltyType: 'Please select penalty type',
      deposit: 'Deposit',
      totalServiceFee: 'Total Service Fee',
      selectFirstPaymentDate: 'Please select first payment date',
      rules: {
        typeRequired: 'Please select order type',
        startDateRequired: 'Please select start date',
        durationRequired: 'Please enter duration'
      },
      details: {
        title: 'Order Details'
      },
      confirmCancel: 'Are you sure to cancel creating the order? All information entered will be lost.',
      createSuccess: 'Order created successfully',
      createError: 'Failed to create order',
      totalAmount: 'Total Amount',
      paymentSummary: 'Payment Summary',
      buttons: {
        create: 'Create Order',
        export: 'Export',
        batchDelete: 'Batch Delete'
      },
      batchDelete: {
        confirm: 'Are you sure to delete {count} selected orders?',
        success: 'Batch deletion successful',
        error: 'Batch deletion failed'
      },
      payment: {
        methods: {
          ONLINE: 'Online Payment',
          OFFLINE_TRANSFER: 'Offline Transfer',
          BANK_TRANSFER: 'Bank Transfer',
          CASH: 'Cash',
          POS: 'POS',
          WECHAT: 'WeChat Pay',
          ALIPAY: 'Alipay',
          OTHER: 'Other'
        },
        title: 'Order Payment',
        total: 'Total',
        status: {
          pending: 'Pending',
          paid: 'Paid',
          overdue: 'Overdue',
          latePaid: 'Late Paid'
        },
        method: 'Payment Method',
        submitPayment: 'Confirm Payment',
        cancel: 'Cancel',
        failed: 'Payment failed',
        selectTransaction: 'Please select at least one transaction to pay',
        selectMethod: 'Please select payment method',
        amount: 'Amount Due',
        paidAmount: 'Paid Amount',
        dueDate: 'Due Date',
        type: 'Payment Type',
        remarks: 'Remarks',
        selectOneTransaction: 'Please select at least one transaction',
        unpaidTransactions: 'Unpaid Transactions',
        totalPayable: 'Total Payable',
        totalPaid: 'Total Paid',
        overdueAmount: 'Overdue Amount',
        selectTransactionRequired: 'Please select at least one transaction to pay',
        selectMethodRequired: 'Please select payment method',
        remarksPlaceholder: 'Please enter remarks, up to 256 characters',
        remarksMaxLength: 'Remarks cannot exceed 256 characters',
        confirmPayment: 'Confirm Payment',
        successMessage: 'Payment successful',
        failedMessage: 'Payment failed, please try again later',
        systemError: 'Payment failed, system error',
        fetchFailed: 'Failed to fetch transaction records',
        noUnpaidTransactions: 'No unpaid transactions',
        selectAtLeastOne: 'Please select at least one transaction to pay'
      },
      delete: {
        title: 'Delete Order',
        confirmMessage: 'Are you sure to delete this order? This action cannot be undone!',
        confirm: 'Are you sure to delete this order? This action cannot be undone!',
        cannotDeleteWithTransactions: 'Cannot delete order with transactions',
        success: 'Delete successfully',
        error: 'Delete failed'
      },
      validation: {
        startDateRequired: 'Please select start date',
        typeRequired: 'Please select order type',
        initialPaymentRequired: 'Please enter initial payment',
        firstPaymentDateRequired: 'Please select first payment date',
        firstPaymentDateInvalid: 'First payment date cannot be earlier than the order date, please reselect',
        periodicPaymentRequired: 'Please enter periodic payment',
        installmentsRequired: 'Please enter number of installments',
        periodicLengthRequired: 'Please enter periodic length',
        penaltyTypeRequired: 'Please select penalty type',
        validAmount: 'Please enter a valid amount (max 8 digits, 2 decimals)',
        validInteger: 'Please enter a valid positive integer',
        remarksLength: 'Remarks cannot exceed 256 characters',
        checkFormFields: 'Please check form errors',
        selectDevice: 'Please select a device',
        selectCustomer: 'Please select or create a customer',
        positiveInteger: 'Please enter a valid positive integer',
        positiveAmount: 'Amount must be greater than 0',
        depositAmountRequired: 'Please enter the deposit amount',
        serviceFeeRequired: 'Please enter the total service fee',
      },
      dialog: {
        createTitle: 'Create Order',
        monthlyAmount: 'Monthly Amount',
        paymentMethod: 'Payment Method',
        paymentFrequency: 'Payment Frequency',
        monthly: 'Monthly',
        quarterly: 'Quarterly',
        semiannual: 'Semi-annual',
        annual: 'Annual',
        needDeposit: 'Need Deposit',
        depositAmount: 'Deposit Amount',
        selectedDevices: 'Selected Devices',
        items: 'items',
        penaltyValue: 'Penalty Value',
        confirmSaveTitle: 'Confirm Save',
        confirmSaveContent: 'Are you sure to save and create the order?',
        confirmSave: 'Confirm Save',
        cancelSave: 'Cancel Save'
      },
      paymentPlanTitle: 'Payment Plan',
      initialPaymentType: 'Initial Payment',
      depositType: 'Deposit',
      installmentType: 'Installment',
      installmentPeriod: 'Period {n}',
      paymentPending: 'Pending',
      paymentPaid: 'Paid',
      paymentOverdue: 'Overdue',
      totalPaymentAmount: 'Total Amount Due',
      amountLabel: 'Amount Due',
      serviceFeeLabel: 'Service Fee',
      dueDateLabel: 'Due Date',
      statusLabel: 'Status',
      paymentTypeLabel: 'Payment Type',
      serialNumber: 'No.',
      installmentNumber: 'Period',
      totalLabel: 'Total',
      paymentPlanTip: 'You can adjust the installment amount and service fee for each period. The system will automatically recalculate the total order amount. Note: The total service fee remains unchanged.',
      deviceInfoTab: {
        uploadDevicePhoto: 'Upload Device Photo',
        attachmentName: 'File Name',
        attachmentActions: 'Actions',
        uploadHint: 'Please upload necessary files such as actual device photos or inspection reports.',
        imeiExplanationTitle: 'IMEI Explanation',
        imeiExplanationText: 'IMEI (International Mobile Equipment Identity) is a unique number to identify mobile phones.'
      },
      button: {
        search: 'Search',
        reset: 'Reset',
        add: 'Add Order',
        export: 'Export',
        batchOperation: 'Batch Operation'
      },
      export: {
        excel: 'Export Excel',
        csv: 'Export CSV',
        success: 'File export successful, downloading...',
        error: 'Export failed'
      },
      batch: {
        onlyOverdueForBadDebt: 'Batch operation failed: Only orders with "Overdue" status can be marked as bad debt. Please filter the orders again.',
        updateStatus: 'Batch Status Update',
        delete: 'Batch Delete',
        noSelection: 'Please select orders to operate',
        deleteConfirm: 'Are you sure to delete {count} selected orders?',
        deleteSuccess: 'Batch deletion successful',
        deleteError: 'Batch deletion failed',
        statusUpdateAllSuccess: 'Batch status update successful',
        statusUpdateAllFailed: 'Batch status update failed',
        statusUpdateSuccess: 'Status update successful',
        statusUpdateError: 'Status update failed',
        statusUpdateAllConfirm: 'Are you sure to update the status of these {count} orders?',
        statusUpdateConfirm: 'Are you sure to update the status of this order?',
      },
      pagination: {
        total: 'Total {total} records',
        goto: 'Go to',
        pageClassifier: 'page',
        pagesize: 'items/page'
      },
      filter: {
        searchPlaceholder: 'Order No./Customer Name/Phone/Device Info',
        search: 'Search',
        reset: 'Reset',
        queryTimeType: 'Please select time type',
        queryTimeOperator: 'Please select time operator',
        enterDays: 'Please enter days',
        expand: 'Expand',
        collapse: 'Collapse',
        advancedOptions: 'Advanced Options',
        orderID: 'Order ID',
        customerName: 'Customer Name',
        selectDate: 'Select Date',
        orderType: 'Order Type',
        all: 'All',
        installment: 'Installment',
        rental: 'Rental',
        orderStatus: 'Order Status',
        normal: 'Normal',
        overdue: 'Overdue',
        completed: 'Completed',
        financialStatus: 'Financial Status',
        dueDate: 'Due Date',
        paymentTime: 'Payment Time',
        overdueTime: 'Overdue Time',
        badDebtTime: 'Bad Debt Time',
        firstPaymentDate: 'First Payment Date',
        last1Month: 'Last 1 Month',
        last3Months: 'Last 3 Months',
        last6Months: 'Last 6 Months',
        last1Year: 'Last 1 Year',
        customDate: 'Custom Date Range',
        greaterThan: 'Greater Than',
        lessThan: 'Less Than',
        equalTo: 'Equal To',
        days: 'Days',
        dueInWeek: 'Due in Week',
        dueInMonth: 'Due in Month',
        overdue: 'Overdue'
      },
      list: {
        title: 'Order List',
        orderNo: 'Order No.',
        orderType: 'Order Type',
        customerName: 'Customer Name',
        customerPhone: 'Customer Phone',
        deviceInfo: 'Device Info',
        serialNumber: 'Serial Number',
        imei: 'IMEI',
        createDate: 'Create Date',
        updateDate: 'Update Date',
        orderStatus: 'Order Status',
        financialStatus: 'Financial Status',
        firstPaymentDate: 'First Payment Date',
        currentInstallment: 'Current Installment',
        badDebt: 'Bad Debt',
        yes: 'Yes',
        no: 'No',
        days: 'Days',
        installment: 'Installment',
        rental: 'Rental',
        installmentWithTag: 'Installment',
        rentalWithTag: 'Rental',
        of: '/',
        operations: 'Operations',
        actions: {
          view: 'View',
          delete: 'Delete',
          edit: 'Edit',
          payment: 'Payment',
          attachment: 'Attachments',
          statusUpdate: 'Status Update'
        }
      },
      actions: {
        create: 'Create',
        createOrder: 'Create Order',
        export: 'Export',
        exportCSV: 'Export CSV',
        exportExcel: 'Export Excel',
        batchOperation: 'Batch Operation',
        batchStatusUpdate: 'Batch Status Update',
        customColumns: 'Custom Columns',
        deleteConfirm: 'Are you sure to delete this order? This action cannot be undone!',
        deleteSuccess: 'Delete successful',
        deleteFailed: 'Delete failed',
        exportSuccess: 'Export successful',
        exportFailed: 'Export failed',
        noDataToExport: 'No data to export',
        pleaseSelectOrders: 'Please select orders to operate',
        batchUpdateStatus: 'Batch Update Status'
      },
      batchUpdate: {
        title: 'Batch Update Order Status',
        changeStatusTo: 'Change Status To',
        markAsBadDebt: 'Mark as Bad Debt',
        markAsCompleted: 'Mark as Completed',
        selectedOrders: 'Selected Orders',
        confirmBatchUpdate: 'I have confirmed the above information and understand the business impact of status changes, bad debt marking and completion operations',
        batchUpdateWarning: 'Note: Status changes, bad debt marking or completion operations may affect order billing, payment and other related business',
        batchUpdateImpact: 'Status changes may cause billing rule changes, please operate with caution',
        selectAtLeastOneOption: 'Please select at least one option',
        confirmBatchUpdateMessage: 'Are you sure to update the status of these {count} orders?',
        batchUpdateSuccess: 'Batch status update successful',
        batchUpdateFailed: 'Batch status update failed'
      },
      financialStatus: {
        normal: 'Normal',
        overdue: 'Overdue',
        settled: 'Settled',
        unknown: 'Unknown',
        NORMAL: 'Normal',
        OVERDUE: 'Overdue',
        SETTLED: 'Settled',
        UNKNOWN: 'Unknown',
        COMPLETED: 'Completed',
        NOT_STARTED: 'Not Started',
        IN_PROGRESS: 'In Progress',
        DEFAULTED: 'Defaulted'
      },
      status: {
       active: 'Active',
        completed: 'Completed',
        overdue: 'Overdue',
        cancelled: 'Cancelled',
        unknown: 'Unknown Status',
        normal: 'Normal',
        notStarted: 'Not Started',
        defaulted: 'Defaulted',
        badDebt: 'Bad Debt',
        ACTIVE: 'Active',
        COMPLETED: 'Completed',
        OVERDUE: 'Overdue',
        CANCELLED: 'Cancelled',
        UNKNOWN: 'Unknown Status',
        NORMAL: 'Normal',
        NOT_STARTED: 'Not Started',
        DEFAULTED: 'Defaulted',
        BAD_DEBT: 'Bad Debt'
      },
      penaltyCalculationType: 'Penalty Calculation Type',
      selectPenaltyCalculationType: 'Please select penalty calculation type',
      penaltyCalculationTypes: {
        daily: 'Per Day',
        weekly: 'Per Week',
        monthly: 'Per Month'
      },
      penaltyAmountType: 'Penalty Amount Type',
      selectPenaltyAmountType: 'Please select penalty amount type',
      penaltyAmountTypes: {
        fixed: 'Fixed Amount',
        percentage: 'Percentage Amount'
      },
      penaltyValue: 'Penalty Value',
      enterFixedAmount: 'Please enter fixed amount',
      enterPercentage: 'Please enter percentage value',
      penaltyCalculationTypeRequired: 'Please select penalty calculation type',
      penaltyAmountTypeRequired: 'Please select penalty amount type',
      penaltyValueRequired: 'Please enter penalty value',
      penaltyValueTooLarge: 'Fixed amount cannot exceed 99999',
      percentageTooLarge: 'Percentage cannot exceed 100%',
      orderIdPlaceholder: 'Please enter order ID',
      customerNamePlaceholder: 'Please enter customer name',
      orderId: 'Order ID',
      nextPaymentAmount: 'Next Payment Amount',
      includeOverdue: 'Including Overdue Penalty',
      contractAmount: 'Contract Amount',
      search: {
        placeholder: 'Enter Order No./Customer Name/Phone/IMEI'
      },
      attachment: {
        allAttachments: 'All Attachments',
        title: 'Attachment Management',
        dragHint: 'Drag & drop files here or click to upload',
        formatLimit: 'Supports JPG, PNG, JPEG, PDF formats',
        sizeLimit: 'Single file size cannot exceed 5MB',
        invalidFormat: 'Upload failed: Only JPG, PNG, JPEG, PDF formats are supported.',
        fileName: 'File Name',
        fileSize: 'File Size',
        fileType: 'File Type',
        uploadDate: 'Upload Date',
        operations: 'Operations',
        preview: 'Preview',
        download: 'Download',
        delete: 'Delete',
        deleteConfirm: 'Are you sure you want to delete this file? This action cannot be undone.',
        fetchFailed: 'Failed to fetch attachment list',
        uploadSuccess: 'File {fileName} uploaded successfully',
        uploadFailed: 'File {fileName} failed to upload',
        uploadFailedInvalidResponse: 'Upload failed: Invalid server response ({fileName})',
        deleteSuccess: 'Attachment deleted successfully',
        deleteFailed: 'Failed to delete attachment',
        downloadFailed: 'Invalid download link, cannot download',
        previewFailed: 'Preview is not supported for this file format',
        noOrderId: 'Order ID is missing, cannot upload attachments',
        uploadLimit: 'Upload failed: You can upload up to 5 files only.',
        noFilesToSave: 'No attachments to save',
        saveSuccess: 'Attachments saved successfully',
        filesToBeSaved: 'Files to be Uploaded',
        fileRemoved: 'File {fileName} has been removed',
        uploadedAttachments: 'Uploaded Attachments',
        invalidLink: 'Invalid link. Please try again.',
        noPreviewAvailable: 'This file type is not supported for preview.',
        downloadUrlMissing: 'Download link missing or invalid.'
      }
    },
    dateRange: {
      lastWeek: 'Last Week',
      lastMonth: 'Last Month',
      lastQuarter: 'Last 3 Months',
      lastSixMonths: 'Last 6 Months',
      lastYear: 'Last Year',
      startDate: 'Start Date',
      endDate: 'End Date'
    },
    date: {
      months: [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ],
      weekDays: [
        'Sunday', 'Monday', 'Tuesday', 'Wednesday',
        'Thursday', 'Friday', 'Saturday'
      ]
    },
    dashboard: {
      welcome: 'Welcome back, {name}',
      welcomeDesc: 'Welcome to use this system, enjoy!',
      recentActivity: 'Recent Activity',
      performance: 'Performance Metrics',
      selectCompany: 'Select Company',
      currentStatistics: 'Current Statistics',
      enterpriseTotalStatistics: 'Enterprise Total Statistics',
      mainCompany: 'Headquarters',
      branchA: 'Branch A',
      branchB: 'Branch B',
      branchC: 'Branch C',
      newOrdersCount: 'New Orders',
      completedOrdersCount: 'Completed Orders',
      badDebtOrdersCount: 'Bad Debt Orders',
      totalOrders: 'Total Orders',
      overdueOrders: 'Overdue Orders',
      actualReceived: 'Actual Received',
      accountsReceivable: 'Accounts Receivable',
      expenditure: 'Expenditure',
      initialPaymentIncome: 'Initial Payment',
      installmentIncome: 'Installment Income',
      penaltyIncome: 'Penalty Income',
      depositReceived: 'Deposit Received',
      initialPaymentReceivable: 'Initial Payment Due',
      installmentReceivable: 'Installment Due',
      penaltyReceivable: 'Penalty Due',
      overdueIncome: 'Overdue Income',
      depositReceivable: 'Deposit Due',
      deviceCost: 'Device Cost',
      badDebtAmount: 'Bad Debt Amount',
      fetchDataFailed: 'Failed to fetch data, please try again later',
      pleaseSelectDateRange: 'Please select a date range before searching',
      resetToDefaultDateRange: 'Date range has been reset to the last month'
    },
    paymentHistory: {
      summary: {
        totalPayable: 'Total Payable',
        totalPaid: 'Total Paid',
        overdueAmount: 'Overdue Amount',
        nextPaymentAmount: 'Next Payment Amount',
        unpaidPenalty: 'Unpaid Penalty'
      },
      filters: {
        typePlaceholder: 'Transaction Type',
        statusPlaceholder: 'Transaction Status',
        paymentDateStart: 'Payment Date Start',
        paymentDateEnd: 'Payment Date End'
      },
      types: {
        INITIAL: 'Initial Payment',
        DEPOSIT: 'Deposit',
        INSTALLMENT: 'Installment',
        REFUND: 'Refund'
      },
      statuses: {
        PAID: 'Paid',
        PENDING: 'Pending',
        LATE_PAID: 'Late Paid',
        OVERDUE: 'Overdue',
        REFUNDED: 'Refunded'
      },
      table: {
        index: 'No.',
        transactionDate: 'Transaction Date',
        type: 'Type',
        installmentNo: 'Period',
        payableAmount: 'Payable',
        paidAmount: 'Paid Amount',
        serviceFee: 'Service Fee',
        status: 'Status',
        dueDate: 'Due Date',
        paidTime: 'Paid Time',
        paymentMethod: 'Payment Method',
        overdueAmount: 'Overdue Amount',
        createdTime: 'Created Time'
      }
    },
    system: {
      socialAccountDeleteSuccess: 'Social media account deleted successfully',
      socialAccountDeleteFailed: 'Social media account deletion failed',
      companyInfo: 'Company Info',
      addAccount: 'Add Account',
      allStatus: 'All Status',
      days: 'Days',
      editAccount: 'Edit Account',
      disableAccount: 'Disable Account',
      enableAccount: 'Enable Account',
      passwordPolicy: 'Password must contain letters and numbers, at least 8 characters',
      allTypes: 'All Types',
      accountSettings: 'Account Settings',
      logoDeleteSuccess: 'Company logo deleted successfully',
      logoDeleteFailed: 'Company logo deletion failed',
      newPassword: 'New Password',
      inputNewPassword: 'Please enter new password',
      active: 'Active',
      disabled: 'Disabled',
      passwordResetSuccess: 'Password reset email sent',
      passwordResetFailed: 'Password reset email failed',
      resetPassword: 'Reset Password',
      deleteAccount: 'Delete Account',
      deleteAccountConfirm: 'Are you sure you want to delete this account?',
      resetPasswordConfirm: 'Are you sure you want to reset the password for this account?',
      accountCreatedSuccess: 'Account created successfully',
      accountCreatedFailed: 'Account creation failed',
      accountUpdatedSuccess: 'Account updated successfully',
      accountUpdatedFailed: 'Account update failed',
      accountDeletedSuccess: 'Account deleted successfully',
      usernameRequired: 'Username is required',
      trial: 'Trial',
      formal: 'Formal',
      phoneRequired: 'Phone is required',
      phoneFormat: 'Phone number format is incorrect',
      roleRequired: 'Role is required',
      accountTypeRequired: 'Account type is required',
      statusRequired: 'Status is required',
      emailRequired: 'Email is required',
      emailFormat: 'Email format is incorrect',
      role: 'Role',
      roleName: 'Role Name',
      searchPlaceholder: 'Search by username/phone number/email',
      username: 'Username',
      password: 'Password',
      passwordRequired: 'Password is required',
      confirmPassword: 'Confirm Password',
      confirmPasswordRequired: 'Confirm password is required',
      passwordNotMatch: 'Passwords do not match',
      passwordValidity: 'Password Validity',
      phoneNumber: 'Phone Number',
      accountType: 'Account Type',
      status: 'Status',
      basicInfo: 'Basic Info',
      lastUpdated: 'Last Updated',
      assignPermissions: 'Assign Permissions',
      enableAccountSuccess: 'Account enabled successfully',
      disableAccountSuccess: 'Account disabled successfully',
      logoSizeRequirement: 'Logo Size Recommendation',
      logoFormatSupport: 'Supported Formats',
      logoSizeLimit: 'Size Limit',
      uploadLogo: 'Upload Logo',
      changeLogo: 'Change Logo',
      deleteLogo: 'Delete Logo',
      viewLargerLogo: 'View Larger Logo',
      deleteLogoConfirm: 'Are you sure you want to delete the company logo?',
      uploadSuccess: 'Upload Successful',
      deleteSuccess: 'Delete Successful',
      operationFailed: 'Operation Failed',
      companyName: 'Company Name',
      address: 'Company Address',
      phone: 'Contact Phone',
      email: 'Email',
      enabledStatus: 'Enabled',
      disabledStatus: 'Disabled',
      settlementCurrency: 'Settlement Currency',
      socialMedia: 'Social Media Accounts',
      addSocialAccount: 'Add Social Media Account',
      noSocialAccounts: 'No Social Media Accounts',
      editDialogTitle: 'Edit {field}',
      pleaseEnter: 'Please Enter',
      platformType: 'Platform Type',
      platformName: 'Platform Name',
      accountUrl: 'Account URL',
      wechat: 'WeChat',
      linkedin: 'LinkedIn',
      facebook: 'Facebook',
      twitter: 'Twitter',
      other: 'Other',
      accountManagement: 'Account Management',
      pleaseSelectPlatform: 'Please Select Platform',
      pleaseEnterAccountName: 'Please Enter Account Name',
      pleaseEnterAccountUrl: 'Please Enter Account URL',
      invalidUrl: 'Invalid URL Format',
      platformRequired: 'Platform Type is Required',
      urlRequired: 'Account URL is Required',
      deleteConfirm: 'Confirm to Delete This Account?',
      roleManagement: 'Role Management',
      roleList: 'Role List',
      roleName: 'Role Name',
      pleaseEnterRoleName: 'Please Enter Role Name',
      remark: 'Remark',
      pleaseEnterRemark: 'Please Enter Remark',
      addRole: 'Add Role',
      editRole: 'Edit Role',
      deleteRole: 'Delete Role',
      deleteRoleConfirm: 'Are you sure you want to delete this role?',
      roleNameRequired: 'Role name is required',
      roleNameLength: 'Role name should be between 2-50 characters',
      remarkLength: 'Remark cannot exceed 100 characters',
      createSuccess: 'Created successfully',
      updateSuccess: 'Updated successfully',
      deleteSuccess: 'Deleted successfully',
      createFailed: 'Failed to create',
      updateFailed: 'Failed to update',
      deleteFailed: 'Failed to delete',
      fetchFailed: 'Failed to fetch data',
      noPermission: 'No permission'
    },
    dictionary: {
      noField: 'No custom fields yet',
      title: 'Dictionary Management',
      addField: 'Add Field',
      editField: 'Edit Field',
      moduleOrder: 'Order Management',
      moduleCustomer: 'Customer Management',
      moduleDevice: 'Device Management',
      moduleCompany: 'Company Management',
      modulePayment: 'Payment Management',
      noField: 'No custom fields yet',
      addOption: 'Add Option',
      optionPlaceholder: 'Please enter option',
      options: 'Options',
      fieldName: 'Field Name',
      fieldNamePlaceholder: 'Please enter field name',
      fieldNameRequired: 'Field name is required',
      addOption: 'Add Option',
      optionPlaceholder: 'Please enter option',
      fieldDefaultValueNotInOptions: 'Default value not in options.',
      fieldDefaultValueAutoSet: 'Default value not set, automatically set to the first option.'
    },
    orderDialog: {
      preview: {
        statusPendingConfirmation: 'Pending Confirmation',
        remarksPlaceholder: 'Please enter remarks',
        imei1Label: 'IMEI'
      }
    },
    batchDelete: {
      title: 'Batch Delete',
      dialogTitle: 'Batch Delete Devices',
      confirm: 'Are you sure you want to delete the selected {count} devices?',
      confirmMessage: 'Are you sure you want to delete the selected {count} devices?',
      irreversibleWarning: 'This action cannot be undone!',
      selectedDevicesTitle: 'Selected Devices:',
      confirmationCheckbox: 'I have confirmed the above information and understand that this operation is irreversible',
      confirmButton: 'Confirm Delete',
      success: 'Batch delete successful',
      error: 'Batch delete failed'
    },
    batchChangeStatus: {
      title: 'Batch Change Status',
      pleaseSelect: 'Please select target status',
      dialogTitle: 'Batch Change Device Status',
      changeToLabel: 'Change to',
      targetStatusPlaceholder: 'Please select target status',
      infoMessage: 'You are about to batch change the status of {count} devices',
      selectedDevicesTitle: 'Selected Devices:',
      rentedWarning: 'Note: Devices associated with orders (rented) may not be able to change status',
      confirmationCheckbox: 'I have confirmed the above information and understand that some devices may not be changed due to business restrictions',
      confirmButton: 'Confirm Change',
      success: 'Batch change status successful',
      error: 'Batch change status failed'
    },
    export: {
      csv: 'Export as CSV',
      excel: 'Export as Excel',
      csvSuccess: 'CSV export successful',
      excelSuccess: 'Excel export successful',
      success: 'Export successful',
      error: 'Export failed, please try again'
    }
  };
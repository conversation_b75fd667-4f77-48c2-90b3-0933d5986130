<script setup>
import { ref, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 面包屑列表
const breadcrumbs = ref([]);

// 监听路由变化
watch(
  () => route.path,
  () => {
    getBreadcrumb();
  },
  {
    immediate: true
  }
);

// 获取面包屑数据
function getBreadcrumb() {
  // 重置面包屑
  breadcrumbs.value = [];
  
  // 首页始终放第一个
  breadcrumbs.value.push({
    path: '/',
    meta: { title: t('menus.pureHome') }
  });
  
  // 获取匹配的路由记录
  const matched = route.matched.filter(
    item => item.meta && item.meta.title && !item.meta.hideBreadcrumb
  );
  
  // 添加匹配的路由到面包屑
  breadcrumbs.value = [...breadcrumbs.value, ...matched];
}

// 处理跳转
function handleLink(item) {
  const { path } = item;
  router.push(path);
}
</script>

<template>
  <el-breadcrumb separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumbs" 
        :key="item.path"
      >
        <span
          v-if="index === breadcrumbs.length - 1 || !item.path"
          class="no-redirect"
        >{{ item.meta.title }}</span>
        <a 
          v-else 
          class="redirect" 
          @click.prevent="handleLink(item)"
        >{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<style lang="scss" scoped>
.el-breadcrumb {
  display: inline-block;
  line-height: 48px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
  
  .redirect {
    color: #666;
    font-weight: 600;
    cursor: pointer;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
</style>
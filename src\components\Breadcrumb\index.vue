<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
    <el-breadcrumb-item 
      v-for="(item, index) in breadcrumbList" 
      :key="index"
      :to="index === breadcrumbList.length - 1 ? null : { path: item.path }"
    >
      {{ item.meta.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const breadcrumbList = ref([])

// 获取面包屑数据
const getBreadcrumb = () => {
  const matched = route.matched.filter(
    item => item.meta && item.meta.title && item.path !== '/'
  )
  breadcrumbList.value = matched
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    getBreadcrumb()
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.el-breadcrumb {
  margin-bottom: 20px;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
</style> 
<template>
  <div class="upload-container" ref="uploadComponentRef">
    <el-upload
      v-model:file-list="fileList"
      :auto-upload="false"
      :multiple="true"
      :limit="5"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-remove="handleRemove"
      accept=".jpg,.jpeg,.png,.pdf,.docx,.xlsx"
      class="upload-component"
    >
      <template #trigger>
        <el-button type="primary">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <span>{{ t('device.attachments') }}</span>
        </el-button>
      </template>
      
      <template #tip>
        <div class="el-upload__tip">{{ t('device.uploadTip') }}</div>
      </template>

      <template #file="{ file }">
        <div class="upload-file">
          <el-icon class="file-type-icon">
            <component :is="file.type.startsWith('image/') ? Picture : Document" />
          </el-icon>
          <div class="file-info">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <el-progress 
              v-if="file.status === 'uploading'"
              :percentage="file.percentage || 0"
            />
            <div v-else-if="file.status === 'success'" class="success-text">
              <el-icon><circle-check /></el-icon>
              {{ t('dialog.success.upload') }}
            </div>
            <div v-else-if="file.status === 'error'" class="error-text">
              <el-icon><circle-close /></el-icon>
              {{ t('dialog.error.upload') }}
            </div>
          </div>
          <div class="file-actions">
            <el-button 
              type="danger" 
              link 
              @click.stop="handleRemove(file)"
            >
              <el-icon><delete /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
    </el-upload>

    <div class="upload-actions">
      <el-button type="primary" @click="submitUpload">{{ t('common.confirm') }}</el-button>
      <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
    </div>

    <!-- 预览弹窗 -->
    <el-dialog v-model="previewVisible" :title="t('device.preview')">
      <div class="preview-container">
        <img v-if="isImage(previewFile)" :src="previewUrl" class="preview-image" />
        <iframe v-else :src="previewUrl" class="preview-document"></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { uploadFile } from '@/api/upload'
import { 
  UploadFilled, 
  Document, 
  Picture, 
  CircleCheck, 
  CircleClose,
  View,
  Delete
} from '@element-plus/icons-vue'

const { t } = useI18n()
const uploadComponentRef = ref(null)
const fileList = ref([])
const previewVisible = ref(false)
const previewFile = ref(null)
const previewUrl = ref('')

const emit = defineEmits(['update:fileUrls', 'cancel'])

// 上传前校验
const beforeUpload = (file) => {
  const allowTypes = ['image/jpeg', 'image/png', 'application/pdf', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
  
  if (!allowTypes.includes(file.type)) {
    ElMessage.error(t('dialog.error.fileType'))
    return false
  }
  
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error(t('dialog.error.fileSize'))
    return false
  }
  
  return true
}

// 超出数量限制
const handleExceed = () => {
  ElMessage.warning(t('dialog.fileLimit'))
}

// 移除文件
const handleRemove = (file) => {
  const index = fileList.value.indexOf(file)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 预览文件
const handlePreview = (file) => {
  previewFile.value = file
  previewUrl.value = file.url
  previewVisible.value = true
}

// 判断是否为图片
const isImage = (file) => {
  return file?.raw?.type?.startsWith('image/')
}

// 判断是否可预览
const isPreviewable = (file) => {
  return isImage(file) || file?.raw?.type === 'application/pdf'
}

// 提交上传
const submitUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }
  uploadComponentRef.value.submit()
}

// 取消上传
const handleCancel = () => {
  fileList.value = []
  emit('cancel')
}

// 上传文件的方法
const uploadFiles = async () => {
  console.log('uploadFiles 方法被调用，当前组件:', uploadComponentRef.value)
  console.log('当前文件列表:', fileList.value)
  
  if (fileList.value.length === 0) {
    console.log('没有文件需要上传')
    return []
  }

  try {
    const uploadPromises = fileList.value.map(async file => {
      console.log('准备上传文件:', file.name)
      file.status = 'uploading'
      file.percentage = 0
      
      try {
        const response = await uploadFile(file.raw)
        console.log('文件上传成功:', file.name, response)
        file.status = 'success'
        file.percentage = 100
        file.url = response.data.url
        return response.data.url
      } catch (error) {
        console.error('文件上传失败:', file.name, error)
        file.status = 'error'
        file.percentage = 0
        throw error
      }
    })

    console.log('等待所有文件上传完成')
    const urls = await Promise.all(uploadPromises)
    console.log('所有文件上传完成，URLs:', urls)
    return urls
  } catch (error) {
    console.error('上传过程出错:', error)
    throw error
  }
}

// 暴露方法和数据
defineExpose({
  uploadFiles,
  fileList
})
</script>

<style lang="scss" scoped>
.upload-container {
  .upload-component {
    :deep(.el-upload-list) {
      width: 100%;
    }
  }

  .upload-actions {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    gap: 12px;
  }

  .upload-file {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 8px;

    .file-type-icon {
      font-size: 24px;
      margin-right: 12px;
      color: #909399;
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        font-size: 14px;
        color: #606266;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .success-text {
        color: #67c23a;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
      }

      .error-text {
        color: #f56c6c;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
      }
    }

    .file-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.preview-container {
  width: 100%;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f7fa;

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .preview-document {
    width: 100%;
    height: 100%;
    border: none;
  }
}

:deep(.el-upload__tip) {
  line-height: 1.5;
  margin-top: 8px;
  color: #909399;
}
</style> 
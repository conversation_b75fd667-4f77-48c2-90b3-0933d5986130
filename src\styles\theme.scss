// 主题变量
:root {
    --el-color-primary: #409EFF;
    --el-color-success: #67C23A;
    --el-color-warning: #E6A23C;
    --el-color-danger: #F56C6C;
    --el-color-info: #909399;
    
    // 布局变量
    --pure-transition-duration: 0.3s;
    --sidebar-width: 210px;
    --sidebar-collapse-width: 64px;
    --header-height: 48px;
  }
  
  // 暗黑模式
  html.dark {
    --el-bg-color: #141414;
    --el-bg-color-overlay: #1d1e1f;
    --el-text-color-primary: #E5EAF3;
    --el-text-color-regular: #CFD3DC;
    --el-border-color-light: #414243;
    --el-fill-color-light: #262727;
    
    body {
      background-color: var(--el-bg-color);
      color: var(--el-text-color-primary);
    }
    
    .sidebar-container {
      background-color: var(--el-bg-color-overlay);
      border-right: 1px solid var(--el-border-color-light);
    }
    
    .app-main {
      background-color: var(--el-bg-color);
    }
    
    .navbar {
      background-color: var(--el-bg-color-overlay) !important;
      box-shadow: 0 1px 4px #0d0d0d !important;
    }
  }
  
  // 主题过渡效果
  .pure-classes-transition {
    transition: all var(--pure-transition-duration);
  }
  
  // 菜单主题
  .el-menu {
    --el-menu-bg-color: var(--el-bg-color-overlay);
    --el-menu-hover-bg-color: var(--el-fill-color-light);
    --el-menu-text-color: var(--el-text-color-regular);
    --el-menu-active-color: var(--el-color-primary);
    
    border-right: none !important;
  }
  
  // 侧边栏过渡
  .sidebar-container {
    transition: width var(--pure-transition-duration);
    width: var(--sidebar-width);
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    
    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 48px);
      }
    }
    
    &.no-logo {
      .el-scrollbar {
        height: 100%;
      }
    }
  }
  
  // 主内容区域过渡
  .main-container {
    min-height: 100%;
    transition: margin-left var(--pure-transition-duration);
    margin-left: var(--sidebar-width);
    position: relative;
  }
  
  // 侧边栏折叠状态
  .hideSidebar {
    .sidebar-container {
      width: var(--sidebar-collapse-width) !important;
    }
    
    .main-container {
      margin-left: var(--sidebar-collapse-width);
    }
    
    .svg-icon {
      margin-right: 0 !important;
    }
    
    .sub-el-icon {
      margin-right: 0 !important;
    }
    
    // 隐藏侧边栏时，子菜单应显示在弹出式菜单中
    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }
  
  // 移动端适配
  .mobile {
    .main-container {
      margin-left: 0;
    }
    
    .sidebar-container {
      transition: transform var(--pure-transition-duration);
      width: var(--sidebar-width) !important;
    }
    
    &.hideSidebar {
      .sidebar-container {
        transform: translateX(-210px);
      }
    }
  }
  
  // 隐藏侧边栏
  .main-hidden {
    margin-left: 0 !important;
  }
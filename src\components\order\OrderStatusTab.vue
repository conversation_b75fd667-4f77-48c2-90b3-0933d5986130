<!-- 订单状态概览部分 -->
<template>
  <div class="order-status-tab">
    <el-row :gutter="20">
      <!-- 订单基本信息 -->
      <el-col :span="8">
        <div class="status-card">
          <div class="card-header">{{ $t('order.orderStatus') }}</div>
          <div class="card-content">
            <div class="status-tag" :class="orderStatusClass">
              {{ orderInfo.orderStatusName }}
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 财务状态 -->
      <el-col :span="8">
        <div class="status-card">
          <div class="card-header">{{ $t('order.financialStatusInfo') }}</div>
          <div class="card-content">
            <div class="status-tag" :class="financialStatusClass">
              {{ getFinancialStatusText(orderInfo.financialStatus) }}
            </div>
          </div>
        </div>
      </el-col>

      <!-- 违约金信息 -->
      <el-col :span="8">
        <div class="status-card">
          <div class="card-header">{{ $t('order.penaltyInfo') }}</div>
          <div class="card-content">
            <div class="penalty-info">
              <div class="info-item">
                <span class="label">{{ $t('order.totalPenalty') }}：</span>
                <span class="value">¥{{ formatAmount(orderInfo.totalPenaltyIncurred) }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ $t('order.paidPenalty') }}：</span>
                <span class="value success">¥{{ formatAmount(orderInfo.totalPenaltyPaid) }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ $t('order.unpaidPenalty') }}：</span>
                <span class="value danger">¥{{ formatAmount(orderInfo.remainingPenalty) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 待付款信息展示 -->
    <div class="payment-due-section">
      <div class="section-header">{{ $t('order.nextPayment') }}</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">{{ $t('order.dueDate') }}：</span>
            <span class="value">{{ orderInfo.nextPaymentDate }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">{{ $t('order.dueAmount') }}：</span>
            <span class="value emphasis">¥{{ formatAmount(orderInfo.nextPaymentAmount) }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 逾期警示框 -->
      <div v-if="orderInfo.hasOverdue" class="overdue-alert">
        <el-alert
          type="warning"
          :closable="false"
          show-icon
        >
          <template #title>
            <span class="overdue-title">{{ $t('order.overdueWarning') }}</span>
          </template>
          <div class="overdue-details">
            <div class="detail-item">
              <span class="label">{{ $t('order.overdueInstallments') }}：</span>
              <span class="value">{{ orderInfo.overdueInstallments }}{{ $t('order.installmentUnit') }}</span>
            </div>
            <div class="detail-item">
              <span class="label">{{ $t('order.maxOverdueDays') }}：</span>
              <span class="value">{{ orderInfo.maxOverdueDays }}{{ $t('order.dayUnit') }}</span>
            </div>
            <div class="detail-item">
              <span class="label">{{ $t('order.overdueAmount') }}：</span>
              <span class="value danger">¥{{ formatAmount(orderInfo.totalOverdueAmount) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">{{ $t('order.penaltyAmount') }}：</span>
              <span class="value danger">¥{{ formatAmount(orderInfo.remainingPenalty) }}</span>
            </div>
          </div>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({})
  }
})

// 格式化金额
const formatAmount = (amount) => {
  return Number(amount || 0).toFixed(2)
}

// 获取财务状态文本
const getFinancialStatusText = (status) => {
  const statusKey = status ? status.toUpperCase() : 'NOT_STARTED';
  const statusMap = {
    'OVERDUE': t('order.financialStatus.OVERDUE'),
    'NOT_STARTED': t('order.financialStatus.NOT_STARTED'),
    'IN_PROGRESS': t('order.financialStatus.IN_PROGRESS'),
    'SETTLED': t('order.financialStatus.SETTLED')
  };
  return statusMap[statusKey] || status;
}

// 订单状态样式
const orderStatusClass = computed(() => {
  return {
    'normal': props.orderInfo.orderStatus === 'NORMAL',
    'overdue': props.orderInfo.hasOverdue,
    'completed': props.orderInfo.orderStatus === 'COMPLETED',
    'cancelled': props.orderInfo.orderStatus === 'CANCELLED'
  }
})

// 财务状态样式
const financialStatusClass = computed(() => {
  const status = props.orderInfo.financialStatus || '';
  return {
    'overdue': status === 'OVERDUE',
    'in-progress': status === 'IN_PROGRESS',
    'settled': status === 'SETTLED',
    'not-started': status === 'NOT_STARTED'
  };
})
</script>

<style lang="scss" scoped>
.order-status-tab {
  padding: 20px;
}

.status-card {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  height: 100%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.card-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #303133;
}

.status-tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 16px;
}

.status-tag.normal {
  background: #e1f3d8;
  color: #67c23a;
}

.status-tag.overdue {
  background: #fef0f0;
  color: #f56c6c;
}

.status-tag.completed {
  background: #67c23a1a;
  color: #67c23a;
}

.status-tag.pending,
.status-tag.not-started {
  background: #fdf6ec;
  color: #e6a23c;
}

.status-tag.in-progress {
  background: #ecf5ff;
  color: #409eff;
}

.status-tag.settled {
  background: #f0f9eb;
  color: #67c23a;
}

.payment-due-section {
  margin-top: 20px;
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.label {
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  font-weight: 500;
  color: #303133;
}

.value.emphasis {
  font-size: 18px;
  color: #409EFF;
  font-weight: bold;
}

.value.danger {
  color: #f56c6c;
}

.value.success {
  color: #67c23a;
}

.penalty-info {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.overdue-alert {
  margin-top: 16px;
}

.overdue-alert :deep(.el-alert__title) {
  font-size: 14px;
  font-weight: 500;
}

.overdue-alert :deep(.el-alert__content) {
  padding: 8px 0;
}

.overdue-details {
  margin-top: 8px;
}

.detail-item {
  margin-bottom: 4px;
  font-size: 13px;
  display: flex;
  align-items: center;
}
</style> 
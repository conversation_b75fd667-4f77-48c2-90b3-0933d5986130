/**
 * 测试设备附件功能的示例数据和验证
 * 这个文件用于验证设备详情页面使用后端返回的附件信息
 */

// 模拟后端返回的设备附件数据（包含缩略图、文件大小、文件类型）
const mockDeviceAttachments = [
  {
    id: "att_001",
    fileName: "device_manual.pdf",
    fileUrl: "https://example.com/files/device_manual.pdf",
    thumbnail: "https://example.com/thumbnails/device_manual_thumb.jpg", // 后端生成的PDF缩略图
    fileSize: 2048576, // 2MB
    fileType: "PDF",
    uploadTime: "2024-01-15T10:30:00Z",
    createTime: "2024-01-15T10:30:00Z"
  },
  {
    id: "att_002", 
    fileName: "device_photo.jpg",
    fileUrl: "https://example.com/files/device_photo.jpg",
    thumbnail: null, // 图片文件不需要单独的缩略图，直接使用原图
    fileSize: 1536000, // 1.5MB
    fileType: "JPG",
    uploadTime: "2024-01-15T11:00:00Z",
    createTime: "2024-01-15T11:00:00Z"
  },
  {
    id: "att_003",
    fileName: "warranty_info.docx", 
    fileUrl: "https://example.com/files/warranty_info.docx",
    thumbnail: "https://example.com/thumbnails/warranty_info_thumb.jpg", // 后端生成的文档缩略图
    fileSize: 512000, // 500KB
    fileType: "DOCX",
    uploadTime: "2024-01-15T12:00:00Z",
    createTime: "2024-01-15T12:00:00Z"
  }
];

// 模拟旧的前端处理方式（需要下载文件获取大小，生成缩略图）
const mockOldAttachments = [
  {
    id: "att_001",
    fileName: "device_manual.pdf",
    fileUrl: "https://example.com/files/device_manual.pdf"
    // 缺少 thumbnail, fileSize, fileType - 需要前端处理
  },
  {
    id: "att_002",
    fileName: "device_photo.jpg", 
    fileUrl: "https://example.com/files/device_photo.jpg"
    // 缺少 fileSize, fileType - 需要前端处理
  }
];

// 验证新的处理方式的优势
console.log("=== 新的后端返回附件信息方式 ===");
console.log("优势：");
console.log("1. 无需前端下载文件获取大小");
console.log("2. 无需前端生成PDF缩略图");
console.log("3. 减少网络请求和处理时间");
console.log("4. 提升用户体验，减少加载时间");
console.log("5. 减少前端计算资源消耗");

console.log("\n=== 示例数据对比 ===");
console.log("新方式 - 后端返回完整信息：");
console.log(JSON.stringify(mockDeviceAttachments[0], null, 2));

console.log("\n旧方式 - 前端需要额外处理：");
console.log(JSON.stringify(mockOldAttachments[0], null, 2));

// 模拟格式化函数测试
function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function formatUploadTime(timeString) {
  if (!timeString) return '';
  try {
    const date = new Date(timeString);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return '';
  }
}

console.log("\n=== 格式化测试 ===");
mockDeviceAttachments.forEach(att => {
  console.log(`文件: ${att.fileName}`);
  console.log(`大小: ${formatFileSize(att.fileSize)}`);
  console.log(`类型: ${att.fileType}`);
  console.log(`上传时间: ${formatUploadTime(att.uploadTime)}`);
  console.log(`缩略图: ${att.thumbnail ? '有' : '无'}`);
  console.log("---");
});

// 验证修改的关键点
console.log("\n=== 关键修改点验证 ===");
console.log("✓ 创建了 src/api/types/device.js 定义附件类型");
console.log("✓ 修改了 src/views/device/index.vue 的 processAllAttachments 函数");
console.log("✓ 移除了前端 PDF 缩略图生成逻辑");
console.log("✓ 移除了前端文件大小获取逻辑");
console.log("✓ 更新了附件显示模板，移除骨架屏");
console.log("✓ 修改了 src/components/order/tabs/DeviceInfoTab.vue");
console.log("✓ 修改了 src/components/common/AttachmentList.vue");
console.log("✓ 添加了文件类型识别和格式化函数");

export { mockDeviceAttachments, mockOldAttachments, formatFileSize, formatUploadTime };

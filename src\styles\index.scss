@use './theme.scss';
@use './transition.scss';
@use './message-box.scss';
@use './order-tabs.scss';

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  overflow: hidden;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a, a:focus, a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

// 工具类
.full-height {
  height: 100%;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.grow {
  flex-grow: 1;
}

// 添加全局日期选择器样式
.date-picker-popper {
  z-index: 3000 !important; // 提高层级以确保不被其他元素遮挡
  max-width: none !important;
  width: auto !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  background-color: #fff !important;
  
  // 修复弹出层下方可能出现的额外框
  &::after {
    content: none !important;
  }
  
  .el-picker-panel__content {
    margin: 0 !important;
    
    .el-date-table {
      width: 100% !important;
    }
  }
  
  // 确保日历面板正确显示
  .el-date-range-picker__content {
    width: auto !important;
    
    .el-date-range-picker__header {
      margin-bottom: 8px !important;
    }
  }
  
  // 确保月份日期单元格宽度一致
  .el-date-table td {
    padding: 2px 0 !important;
    width: 32px !important;
    height: 30px !important;
  }
  
  .el-picker-panel__footer {
    padding: 8px !important;
  }
  
  // 优化快捷选项区域
  .el-picker-panel__sidebar {
    width: 110px !important;
    
    .el-picker-panel__shortcut {
      padding: 8px 12px !important;
      font-size: 13px !important;
    }
  }
  
  .el-picker-panel__body-wrapper {
    display: flex !important;
  }
  
  // 修复跨月显示问题
  .el-date-range-picker {
    width: auto !important;
    min-width: 520px !important; // 设置最小宽度确保两个月份面板并排显示
    
    .el-date-range-picker__content {
      &.is-left {
        border-right: 1px solid #e4e7ed !important;
      }
    }
  }
}

// 修复日期选择器在移动设备上的显示问题
@media (max-width: 768px) {
  .date-picker-popper {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: 90vw !important;
    
    .el-date-range-picker {
      min-width: unset !important; // 移动设备上移除最小宽度
      display: flex !important;
      flex-direction: column !important;
      
      .el-date-range-picker__content {
        border-right: none !important;
        margin-bottom: 10px !important;
      }
      
      .el-picker-panel__icon-btn {
        margin: 0 4px !important;
      }
    }
    
    .el-picker-panel__content {
      padding: 10px !important;
    }
    
    // 调整快捷选项在移动设备上的布局
    .el-picker-panel__sidebar {
      width: 100% !important;
      display: flex !important;
      flex-wrap: wrap !important;
      
      .el-picker-panel__shortcut {
        flex: 1 0 auto !important;
        text-align: center !important;
      }
    }
    
    .el-picker-panel__body-wrapper {
      flex-direction: column !important;
    }
  }
}

// 修复日期选择器弹出层显示问题
.el-picker-panel[role="dialog"] {
  margin: 5px 0;
}

// 确保日期范围选择器内部文本有足够空间
.el-range-editor {
  .el-range__content {
    margin: 0 30px 0 10px;
  }
  
  .el-range-input {
    width: 42%;
  }
}

// 日期选择器弹出层样式
.date-popper {
  width: auto !important;
  
  .el-picker-panel__sidebar {
    min-width: 110px;
  }
  
  .el-date-range-picker__content {
    margin: 0 !important;
    width: auto !important;
  }
  
  // 修复日期范围选择器中的清空按钮位置
  .el-range__close-icon {
    position: absolute;
    top: 0;
    right: 5px;
    display: flex;
    align-items: center;
    height: 100%;
  }
  
  .el-picker-panel__body {
    min-width: 550px;
  }
  
  .el-date-table {
    td {
      padding: 2px;
    }
  }
}

@media (max-width: 768px) {
  .date-popper {
    .el-picker-panel__body {
      min-width: auto;
      width: 100%;
    }
    
    .el-picker-panel__sidebar {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      border-right: none;
      border-bottom: 1px solid #ebeef5;
      
      .el-picker-panel__shortcut {
        padding: 6px 10px;
        flex: 1;
        text-align: center;
      }
    }
    
    .el-picker-panel__body-wrapper {
      display: flex;
      flex-direction: column;
    }
    
    .el-date-range-picker__content {
      &.is-left {
        border-right: none;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }
}
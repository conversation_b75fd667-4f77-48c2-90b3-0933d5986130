import {
  HomeFilled,
  Document,
  Setting,
  UserFilled,
  List,
  Mobile
} from '@element-plus/icons-vue'

// 图标映射表
const iconMap = {
  HomeFilled,
  Document,
  Setting,
  UserFilled,
  List,
  Mobile
} 

<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    background-color="var(--el-menu-bg-color)"
    text-color="var(--el-menu-text-color)"
    active-text-color="var(--el-menu-active-text-color)"
    :unique-opened="true"
    router
  >
    <template v-for="menu in menus" :key="menu.path">
      <!-- 有子菜单的情况 -->
      <el-sub-menu v-if="menu.children && menu.children.length > 0" :index="menu.path">
        <template #title>
          <el-icon v-if="menu.meta?.icon">
            <component :is="iconMap[menu.meta.icon]" />
          </el-icon>
          <span>{{ t(menu.meta?.title) }}</span>
        </template>
        <el-menu-item 
          v-for="child in menu.children" 
          :key="child.path"
          :index="menu.path + '/' + child.path"
        >
          <el-icon v-if="child.meta?.icon">
            <component :is="iconMap[child.meta.icon]" />
          </el-icon>
          <span>{{ t(child.meta?.title) }}</span>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 没有子菜单的情况 -->
      <el-menu-item v-else :index="menu.path">
        <el-icon v-if="menu.meta?.icon">
          <component :is="iconMap[menu.meta.icon]" />
        </el-icon>
        <span>{{ t(menu.meta?.title) }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const route = useRoute()

const isCollapse = ref(false)

// 菜单数据
const menus = [
  {
    path: '/home',
    meta: {
      title: 'menus.dashboard',
      icon: 'HomeFilled'
    }
  },
  {
    path: '/device',
    meta: {
      title: 'menus.device',
      icon: 'Mobile'
    },
    children: [
      {
        path: 'list',
        meta: {
          title: 'menus.deviceList',
          icon: 'Document'
        }
      }
    ]
  },
  {
    path: '/customer',
    meta: {
      title: 'menus.customer',
      icon: 'UserFilled'
    },
    children: [
      {
        path: 'list',
        meta: {
          title: 'menus.customerList',
          icon: 'List'
        }
      }
    ]
  }
]

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})
</script>

<style lang="scss" scoped>
.el-menu {
  border-right: none;
  
  :deep(.el-sub-menu__title) {
    &:hover {
      background-color: var(--el-menu-hover-bg-color);
    }
  }
  
  :deep(.el-menu-item) {
    &:hover {
      background-color: var(--el-menu-hover-bg-color);
    }
    
    &.is-active {
      background-color: var(--el-menu-active-bg-color);
    }
  }
  
  .el-icon {
    margin-right: 12px;
    width: 24px;
    text-align: center;
  }
}
</style> 
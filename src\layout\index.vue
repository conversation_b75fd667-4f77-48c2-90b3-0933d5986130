<script setup>
import Sidebar from '@/components/Sidebar/index.vue'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import "animate.css";
console.log(123123)
// 引入图标
// import "@/components/ReIcon/src/offlineIcon";
import { useLayout } from "./hooks/useLayout";
import { useAppStore } from "@/stores/modules/app";
import { useSettingStore } from "@/stores/modules/settings";
// import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import {
  ref,
  reactive,
  computed,
  onMounted,
  onBeforeMount
} from "vue";
import {
  useGlobal,
  deviceDetection,
  useResizeObserver
} from "@pureadmin/utils";
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { logout } from '@/api/user';
import { removeToken } from '@/utils/auth';
import { useI18n } from 'vue-i18n'
import LangSwitch from '@/components/LangSwitch.vue'
import { ArrowDown, User, SwitchButton } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'


const appWrapperRef = ref();
const isMobile = deviceDetection();
const appStore = useAppStore();
const settingStore = useSettingStore();
const { $storage } = useGlobal();
const router = useRouter();
const { t } = useI18n()
const userStore = useUserStore()
const username = ref('管理员')
debugger

const set = reactive({
  sidebar: computed(() => {
    return appStore.sidebar;
  }),

  device: computed(() => {
    return appStore.device;
  }),

  fixedHeader: computed(() => {
    return settingStore.fixedHeader;
  }),

  classes: computed(() => {
    return {
      hideSidebar: !set.sidebar.opened,
      openSidebar: set.sidebar.opened,
      withoutAnimation: set.sidebar.withoutAnimation,
      mobile: set.device === "mobile"
    };
  }),

  hideTabs: computed(() => {
    return $storage?.configure.hideTabs;
  })
});

function toggle(device, bool) {
  appStore.toggleDevice(device);
  appStore.toggleSideBar(bool, "resize");
}

// 判断是否可自动关闭菜单栏
let isAutoCloseSidebar = true;

useResizeObserver(appWrapperRef, entries => {
  if (isMobile) return;
  const entry = entries[0];
  const [{ inlineSize: width, blockSize: height }] = entry.borderBoxSize;
  appStore.setViewportSize({ width, height });
  
  /** width app-wrapper类容器宽度
   * 0 < width <= 760 隐藏侧边栏
   * 760 < width <= 990 折叠侧边栏
   * width > 990 展开侧边栏
   */
  if (width > 0 && width <= 760) {
    toggle("mobile", false);
    isAutoCloseSidebar = true;
  } else if (width > 760 && width <= 990) {
    if (isAutoCloseSidebar) {
      toggle("desktop", false);
      isAutoCloseSidebar = false;
    }
  } else if (width > 990 && !set.sidebar.isClickCollapse) {
    toggle("desktop", true);
    isAutoCloseSidebar = true;
  } else {
    toggle("desktop", false);
    isAutoCloseSidebar = false;
  }
});

onMounted(() => {
  if (isMobile) {
    toggle("mobile", false);
  }
});

onBeforeMount(() => {
  // useDataThemeChange().dataThemeChange($storage.layout?.overallStyle);
});

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm(
    t('header.logoutConfirm'),
    t('dialog.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await userStore.logout()
      ElMessage.success(t('dialog.success.logout'))
      router.push('/login')
    } catch (error) {
      console.error('退出失败：', error)
      ElMessage.error(t('dialog.error.logout'))
    }
  }).catch(() => {
    // 取消退出，不做任何操作
  })
}
</script>

<template>
  <div class="app-wrapper">
    <!-- 顶部导航栏 -->
    <div class="navbar">
      <div class="logo">
        <img src="@/assets/logo_white_opt.png" alt="logo" />
      </div>
      <div class="right-menu">
        <lang-switch class="right-menu-item" />
        <el-dropdown trigger="click">
          <span class="user-info">
            <el-avatar size="small" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
            <span>{{ username }}</span>
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-icon><user /></el-icon>
                <span>{{ t('buttons.pureAccountSettings') }}</span>
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><switch-button /></el-icon>
                <span>{{ t('buttons.pureLoginOut') }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="sidebar-wrapper">
      <Sidebar />
    </div>

    <!-- 主要内容区 -->
    <div class="main-container">
      <!-- 内容区 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;

  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 64px;
    background-color: #1890ff;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1002;
    color: #fff;

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0px;
      
      img {
        max-height: 60%;
        width: auto;
        object-fit: contain;
      }
    }

    .right-menu {
      display: flex;
      align-items: center;
      height: 100%;

      .right-menu-item {
        margin-right: 16px;
      }

      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #fff;
        font-size: 14px;
        
        .el-avatar {
          margin-right: 8px;
        }

        .el-icon {
          margin-left: 4px;
          font-size: 12px;
        }
      }

      :deep(.el-dropdown-menu__item) {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        
        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  .sidebar-wrapper {
    transition: width 0.28s;
    width: 210px;
    height: calc(100% - 64px);
    position: fixed;
    top: 64px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.05);
  }

  .main-container {
    height: 100%;
    transition: margin-left 0.28s;
    margin-left: 210px;
    margin-top: 64px;
    position: relative;
    width: calc(100% - 210px);
    overflow: hidden;
    
    .app-main {
      padding: 20px;
      background-color: #f0f2f5;
      height: calc(100vh - 64px);
      overflow: auto;
    }
  }
}

// 过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
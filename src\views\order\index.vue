<template>
  <div class="order-list-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{ t('common.home') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('order.management') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ t('order.list.title') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 新的筛选区域 -->
    <div class="new-filter-container">
      <!-- 上方搜索区域 -->
      <div class="search-row">
        <el-input
          v-model="searchKeyword"
          :placeholder="t('order.search.placeholder')"
          prefix-icon="Search"
          class="search-input"
          clearable
          @input="handleSearch"
          @keyup.enter="handleSearch"
        />
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> {{ t('common.search') }}
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon> {{ t('common.reset') }}
          </el-button>
          <el-button @click="toggleFilterRow">
            <el-icon><component :is="isFilterRowCollapsed ? ArrowDown : ArrowUp" /></el-icon>
            {{ isFilterRowCollapsed ? t('common.expand') : t('common.collapse') }}
          </el-button>
        </div>
      </div>

      <!-- 筛选器行 -->
      <div class="filter-row" :class="{ 'collapsed-filters': isFilterRowCollapsed }">
        <el-select v-model="filters.orderType" :placeholder="t('order.filter.orderType')" clearable @change="handleSearch">
          <el-option :label="t('order.filter.all')" value="" />
          <el-option v-for="item in orderTypeOptions" :key="item.value" :label="t(`order.types.${item.value.toLowerCase()}`)" :value="item.value" />
        </el-select>
        
        <el-select v-model="filters.orderStatus" :placeholder="t('order.filter.orderStatus')" clearable @change="handleSearch">
          <el-option :label="t('order.filter.all')" value="" />
          <el-option v-for="item in orderStatusOptions" :key="item.value" :label="t(`order.status.${item.value}`)" :value="item.value" />
        </el-select>
        
        <el-select v-model="filters.financialStatus" :placeholder="t('order.filter.financialStatus')" clearable @change="handleSearch">
          <el-option :label="t('order.filter.all')" value="" />
          <el-option v-for="item in financialStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        
        <el-select v-model="filters.badDebtFlag" :placeholder="t('order.list.badDebt')" clearable @change="handleSearch">
          <el-option :label="t('order.filter.all')" value="" />
          <el-option :label="t('common.yes')" value="true" />
          <el-option :label="t('common.no')" value="false" />
        </el-select>
        
        <!-- 首付日期筛选 -->
        <div class="date-filter-wrapper">
          <el-date-picker
            v-model="filters.firstPaymentDateRange"
            type="daterange"
            unlink-panels
            :range-separator="t('common.to')"
            :start-placeholder="t('order.filter.firstPaymentDate')"
            :end-placeholder="t('dateRange.endDate')"
            :shortcuts="firstPaymentDateShortcuts"
            @change="handleFirstPaymentDateRangeChange"
            style="width: 100%"
          />
        </div>
        
        <!-- 到期时间筛选 -->
        <!-- <div class="date-filter-wrapper">
          <el-date-picker
            v-model="filters.dueDateRange"
            type="daterange"
            unlink-panels
            :range-separator="t('common.to')"
            :start-placeholder="t('order.filter.dueDate')"
            :end-placeholder="t('dateRange.endDate')"
            :shortcuts="dueDateShortcuts"
            @change="handleDueDateRangeChange"
            style="width: 100%"
          />
        </div> -->

        <!-- 自定义时间查询筛选 -->
        <div class="time-query-filter-unit">
          <el-select v-model="filters.queryTimeType" :placeholder="$t('order.filter.queryTimeType')" clearable class="query-time-select">
            <el-option :label="$t('order.filter.paymentTime')" value="paymentTime" />
            <el-option :label="$t('order.filter.overdueTime')" value="overdueTime" />
            <el-option :label="$t('order.filter.badDebtTime')" value="badDebtTime" />
          </el-select>
          <el-select v-model="filters.queryTimeOperator" :placeholder="$t('order.filter.queryTimeOperator')" clearable class="query-time-operator-select">
            <el-option label=">" value="greaterThan" />
            <el-option label="=" value="equalTo" />
            <el-option label="<" value="lessThan" />
          </el-select>
          <el-input 
            v-model.number="filters.queryTimeValue"
            :placeholder="$t('order.filter.enterDays')"
            class="query-time-value-input"
            clearable
            @change="handleFilterChange"
          >
            <template #append>{{ $t('common.day') }}</template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 操作区域 -->
    <el-card class="content-card">
      <div class="operation-container">
        <div class="left-operations">
          <el-button type="primary" class="operation-btn" @click="handleCreateOrder">
            <el-icon><Plus /></el-icon>{{ t('order.actions.create') }}
          </el-button>
        </div>
        
        <div class="right-operations">
          <el-dropdown @command="handleExport" trigger="click" :disabled="total === 0">
            <el-button type="primary" class="operation-btn" :disabled="total === 0">
              <el-icon><Download /></el-icon>{{ t('order.actions.export', '导出')
              }}<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="excel" :icon="Download">{{
                  t('order.actions.exportExcel')
                }}</el-dropdown-item>
                <el-dropdown-item command="csv" :icon="Document">{{
                  t('order.actions.exportCSV')
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="warning" @click="handleBatchOperation('status')">
            <el-icon><RefreshRight /></el-icon>{{ t('order.actions.batchUpdateStatus') }}
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <OrderTable 
          :loading="loading"
          :data="orderList"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @selection-change="handleSelectionChange"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @update:page-size="pageSize = $event"
          @update:current-page="currentPage = $event"
          @view="handleView"
          @edit="handleEdit"
          @delete="handleDelete"
          @payment="handleOrderPayment"
          @attachment="handleOrderAttachment"
          @status-update="handleOrderStatusUpdate"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <!-- <OrderDetail v-model="detailVisible" :order-id="currentOrderId" /> -->
    <OrderStatusUpdateDialog 
      v-model="statusVisible" 
      :order-id="isBatchModeActive ? '' : currentOrder.dbId"
      :order-status="isBatchModeActive ? '' : currentOrder.status"
      :order-financial-status="isBatchModeActive ? '' : currentOrder.financeStatus"
      :is-original-bad-debt="isBatchModeActive ? false : currentOrder.badDebt"
      :is-batch="isBatchModeActive"
      :order-ids="isBatchModeActive ? selectedOrders.map(o => o.id) : []"
      :selected-orders-info="isBatchModeActive ? selectedOrders : []"
      :initial-bad-debt-states="isBatchModeActive ? selectedOrders.map(o => o.badDebt) : []"
      :financial-status-options="financialStatusOptions"
      @success="handleStatusSuccess" 
    />
    <AttachmentManager v-model="attachmentVisible" :order-id="currentOrderId"/>
    <PaymentDialog v-model="paymentVisible" :order-id="currentOrderId" @success="handlePaymentSuccess" />
    
    <!-- 创建订单对话框 -->
    <CreateOrderDialog v-model="createOrderVisible" @created="handleOrderCreated" />

    <!-- 新增订单详情对话框 -->
    <OrderDetailDialog 
      v-model="orderDetailDialogVisible" 
      :order-id="currentOrderId" 
      :order-number="currentOrderNumber"
      @payment="handleOrderPayment"
      @attachment="handleOrderAttachment"
      @status-update="handleOrderStatusUpdate"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onActivated } from 'vue'
import { Plus, Download, Setting, RefreshRight, Document, Search, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OrderTable from '@/components/order/OrderTable.vue'
import OrderStatusUpdateDialog from '@/components/order/OrderStatusUpdateDialog.vue'
import AttachmentManager from '@/components/order/AttachmentManager.vue'
import PaymentDialog from '@/components/order/PaymentDialog.vue'
import CreateOrderDialog from '@/components/order/CreateOrderDialog.vue'
import OrderDetailDialog from '@/components/order/OrderDetailDialog.vue'
import { getOrderList, deleteOrder, exportOrders } from '@/api/order'
import { useI18n } from 'vue-i18n'
import { getDictFields } from '@/api/dictionary';

const { t } = useI18n()

// 数据加载状态
const loading = ref(false)

// 订单列表数据
const orderList = ref([])
const total = ref(0)
const pageSize = ref(20)
const currentPage = ref(1)

const financialStatusOptions = ref([]);

// 选中的订单
const selectedOrders = ref([])

// 各弹窗的显示状态
const detailVisible = ref(false)
const statusVisible = ref(false)
const attachmentVisible = ref(false)
const paymentVisible = ref(false)
const createOrderVisible = ref(false)
const orderDetailDialogVisible = ref(false)

// 当前操作的订单ID
const currentOrderId = ref('')
const currentOrderNumber = ref('')
const currentOrder = ref({
  dbId: '',
  orderId: '',
  status: 'normal',
  financeStatus: 'normal',
  badDebt: false
})

const isBatchModeActive = ref(false)

const brandOptions = ref([]);

const getBrandName = (brandValue) => {
  if (!brandValue) return '';
  const option = brandOptions.value.find(opt => opt.value === brandValue);
  return option ? option.label : brandValue;
};

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  handleFilterChange()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  handleFilterChange()
}

// 首付日期快捷选项
const firstPaymentDateShortcuts = [
  {
    text: t('common.all'),
    value: null
  },
  {
    text: t('dateRange.lastWeek'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    }
  },
  {
    text: t('dateRange.lastMonth'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    }
  },
  {
    text: t('dateRange.lastQuarter'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    }
  }
]

// 到期时间快捷选项
const dueDateShortcuts = [
  {
    text: t('common.all'),
    value: null
  },
  {
    text: t('order.filter.dueInWeek'),
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setDate(end.getDate() + 7)
      return [start, end]
    }
  },
  {
    text: t('order.filter.dueInMonth'),
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setMonth(end.getMonth() + 1)
      return [start, end]
    }
  },
  {
    text: t('order.filter.overdue'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6) // 假设最多查询半年前的逾期数据
      return [start, end]
    }
  }
]

// 筛选条件
const filters = ref({
  orderType: '',
  orderStatus: '',
  financialStatus: '',
  badDebtFlag: '',
  firstPaymentDateRange: null,
  dueDateRange: null,
  // 新增时间查询筛选条件
  queryTimeType: '',
  queryTimeOperator: '',
  queryTimeValue: null
})

// 搜索关键词
const searchKeyword = ref('')

// 选项数据
const orderTypeOptions = [
  { value: 'RENTAL', label: t('order.types.rental') },
  { value: 'INSTALLMENT', label: t('order.types.installment') }
]

const orderStatusOptions = [
  { value: 'NORMAL', label: 'order.status.NORMAL' },
  { value: 'COMPLETED', label: 'order.status.COMPLETED' },
  { value: 'OVERDUE', label: 'order.status.OVERDUE' },
]

const financialStatusOptions_hardcoded = [
  { value: 'OVERDUE', label: 'order.financialStatus.OVERDUE' },
  { value: 'NOT_STARTED', label: 'order.financialStatus.NOT_STARTED' },
  { value: 'IN_PROGRESS', label: 'order.financialStatus.IN_PROGRESS' },
  { value: 'SETTLED', label: 'order.financialStatus.SETTLED' }
]

// 筛选器行折叠状态
const isFilterRowCollapsed = ref(false); // 默认收起

// 切换筛选器行的折叠状态
const toggleFilterRow = () => {
  isFilterRowCollapsed.value = !isFilterRowCollapsed.value;
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    orderType: '',
    orderStatus: '',
    financialStatus: '',
    badDebtFlag: '',
    firstPaymentDateRange: null,
    dueDateRange: null,
    queryTimeType: '',
    queryTimeOperator: '',
    queryTimeValue: null
  }
  searchKeyword.value = ''
  handleFilterChange()
}

// 处理筛选条件变化 - 修改为直接使用filters
const handleFilterChange = () => {
  console.log('筛选条件变化:', filters.value)
  
  // 构建查询对象
  const query = {}
  
  // 处理搜索关键词
  if (searchKeyword.value) {
    // 同时搜索多个字段
    query.keyword = searchKeyword.value
  }
  
  // 处理筛选条件
  if (filters.value.orderType) {
    query.orderType = filters.value.orderType
  }
  
  if (filters.value.orderStatus) {
    query.orderStatus = filters.value.orderStatus
  }
  
  if (filters.value.financialStatus) {
    query.financialStatus = filters.value.financialStatus
  }
  
  if (filters.value.badDebtFlag) {
    query.badDebtFlag = filters.value.badDebtFlag === 'true'
  }
  
  // 处理首付日期筛选
  if (filters.value.firstPaymentDateRange && filters.value.firstPaymentDateRange.length === 2) {
    query.firstPaymentDateStart = dayjs(filters.value.firstPaymentDateRange[0]).startOf('day').format();
    query.firstPaymentDateEnd = dayjs(filters.value.firstPaymentDateRange[1]).endOf('day').format();
}

// 处理到期日筛选
if (filters.value.dueDateRange && filters.value.dueDateRange.length === 2) {
    query.dueDateStart = dayjs(filters.value.dueDateRange[0]).startOf('day').format();
    query.dueDateEnd = dayjs(filters.value.dueDateRange[1]).endOf('day').format();
}

// 处理自定义时间查询
if (filters.value.queryTimeType && filters.value.queryTimeOperator && typeof filters.value.queryTimeValue === 'number' && !isNaN(filters.value.queryTimeValue)) {
  query.queryTimeType = filters.value.queryTimeType;
  query.queryTimeOperator = filters.value.queryTimeOperator;
  query.queryTimeValue = filters.value.queryTimeValue; // 直接使用数字值发送
} else {
  // 如果时间类型、操作符或天数不完整/无效，则清空所有相关筛选条件
  delete query.queryTimeType;
  delete query.queryTimeOperator;
  delete query.queryTimeValue;
}

  // 调用API
  fetchOrderList(query)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  handleFilterChange()
}

// 获取订单列表数据 - 修改为接收query参数
const fetchOrderList = async (customQuery) => {
  loading.value = true
  try {
    console.log('正在获取订单列表数据...')
    
    // 构建新的API参数格式
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      ...customQuery
    }
    
    const res = await getOrderList(params)
    
    // console.log('订单列表API响应:', res)
    
    if (res.code === 1000) {
      // 处理API返回的数据，使用快照数据
      orderList.value = res.data.records.map(item => ({
        id: item.id,
        orderId: item.orderId,
        type: item.orderType?.toLowerCase(),
        typeName: item.orderType === 'RENTAL' ? t('order.types.rental') : item.orderType === 'INSTALLMENT' ? t('order.types.installment') : t('order.types.sale'),
        startDate: item.firstPaymentDate?.split('T')[0] || '',
        installment: item.numberOfInstallments > 0 ? `${item.currentInstallment || 1}/${item.numberOfInstallments}` : '-',
        status: item.orderStatus?.toUpperCase() || '',
        statusText: item.orderStatus === 'NORMAL' ? `${t('order.status.NORMAL')} ${item.daysCount || 0}${t('order.list.days')}` : 
                  item.orderStatus === 'OVERDUE' ? `${t('order.status.OVERDUE')} ${item.daysCount || 0}${t('order.list.days')}` :
                  item.orderStatus === 'COMPLETED' ? t('order.status.COMPLETED') :
                  item.orderStatus === 'CANCELLED' ? t('order.status.CANCELLED') :
                  item.orderStatus === 'ACTIVE' ? `${t('order.status.ACTIVE')} ${item.daysCount || 0}${t('order.list.days')}` :
                  t('order.status.' + (item.orderStatus || 'UNKNOWN').toUpperCase()),
        // 使用customerSnapshot数据
        customerName: item.customerSnapshot?.name.replace('_', '') || `${t('customer.id')}: ${item.customerId}`,
        customerId: item.customerId,
        phone: item.customerSnapshot?.phone || '',
        // 使用deviceSnapshot数据
        deviceInfo: item.deviceSnapshot ? 
          `${getBrandName(item.deviceSnapshot.brand) || ''} ${item.deviceSnapshot.model || ''} ${item.deviceSnapshot.color || ''}`.trim() : 
          `${t('device.id')}: ${item.deviceId}`,
        deviceId: item.deviceId,
        deviceBrand: getBrandName(item.deviceSnapshot?.brand) || '',
        deviceName: item.deviceSnapshot?.deviceName || '',
        deviceModel: item.deviceSnapshot?.model || '',
        deviceColor: item.deviceSnapshot?.color || '',
        serialNumber: item.deviceSnapshot?.sn || '',
        imei: item.deviceSnapshot?.imei1 || '',
        imei1: item.deviceSnapshot?.imei1 || '',
        imei2: item.deviceSnapshot?.imei2 || '',
        financeStatus: item.financialStatus?.toUpperCase() || 'NORMAL',
        financeStatusText: financialStatusOptions.value.find(opt => opt.value === item.financialStatus)?.label || item.financialStatus,
        createDate: item.createTime?.replace('T', ' ').substr(0, 10) || '',
        updateDate: item.updateTime?.replace('T', ' ').substr(0, 10) || '',
        initialPayment: item.initialPayment,
        periodicPayment: item.periodicPayment,
        totalAmount: item.totalAmount,
        badDebt: item.badDebt || false,
        badDebtFlag: item.badDebtFlag,
        // 直接使用后端返回的 canDelete 字段作为唯一判断依据，不再进行前端计算
        canDelete: item.canDelete
      }));
      
      total.value = res.data.total;
    } else {
      ElMessage.error(res.message);
    }
  } catch (error) {
    ElMessage.error(`${error.message}`);
    orderList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理新建订单
const handleCreateOrder = () => {
  createOrderVisible.value = true
}

// 处理订单创建成功
const handleOrderCreated = () => {
  ElMessage.success('订单创建成功')
  fetchOrderList()
}

// 处理导出
const handleExport = async (command) => {
  console.log('导出格式:', command);
  const queryParams = {
    keyword: searchKeyword.value || null,
    orderType: filters.value.orderType || null,
    orderStatus: filters.value.orderStatus || null,
    financialStatus: filters.value.financialStatus || null,
  };

  queryParams.format = command;

  if (filters.value.firstPaymentDateRange && filters.value.firstPaymentDateRange.length === 2) {
    queryParams.firstPaymentDateStart = filters.value.firstPaymentDateRange[0];
    queryParams.firstPaymentDateEnd = filters.value.firstPaymentDateRange[1];
  }

  if (filters.value.dueDateRange && filters.value.dueDateRange.length === 2) {
    queryParams.dueDateStart = filters.value.dueDateRange[0];
    queryParams.dueDateEnd = filters.value.dueDateRange[1];
  }

  Object.keys(queryParams).forEach(key => {
    if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
      delete queryParams[key];
    }
  });

  loading.value = true;

  try {
    const response = await exportOrders(queryParams);

    let blobData;
    let mimeType;
    let fileExt = command;

    if (command === 'excel') {
      mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileExt = 'xlsx';
    } else if (command === 'csv') {
      mimeType = 'text/csv;charset=utf-8';
    } else {
      mimeType = 'application/octet-stream';
    }

    if (response && response.data && response.data instanceof Blob) {
      blobData = response.data;
      if (response.data.type && response.data.type !== 'application/octet-stream') {
         mimeType = response.data.type;
      }
    } else {
      console.error('导出响应不是 Blob:', response);
      let errorJson = {};
      try {
        errorJson = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
      } catch (e) { /* ignore parsing error */ }
      const apiMessage = errorJson?.message || '服务器未返回有效文件数据';
      ElMessage.error(`${t('order.export.error')}: ${apiMessage}`);
      loading.value = false;
      return;
    }

    const link = document.createElement('a');
    link.href = URL.createObjectURL(new Blob([blobData], { type: mimeType }));
    
    const contentDisposition = response.headers ? response.headers['content-disposition'] : '';
    let downloadFileName = `${t('order.list.title', '订单列表')}_${command}_${new Date().getTime()}.${fileExt}`;
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;"\n]*?)['";\n]?$/i);
      if (fileNameMatch && fileNameMatch[1]) {
        try {
            downloadFileName = decodeURIComponent(fileNameMatch[1]);
        } catch (e) {
            downloadFileName = fileNameMatch[1];
            console.warn('Could not decode filename from Content-Disposition, using raw value:', downloadFileName);
        }
      } else {
         const simpleMatch = contentDisposition.match(/filename="?(.+)"?/);
         if (simpleMatch && simpleMatch[1]) downloadFileName = simpleMatch[1];
      }
    }
    
    link.download = downloadFileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    
    ElMessage.success(t('order.export.success', { format: command.toUpperCase() }));
  } catch (error) {
    console.error('导出订单失败:', error);
    let errorMessage = error.message || t('order.export.error');
    if (error.response && error.response.data && error.response.data instanceof Blob) {
        try {
            const errorText = await error.response.data.text();
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || errorMessage;
        } catch (e) {
            // Blob is not JSON or text, stick with original error
        }
    } else if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
    }

    ElMessage.error(`${t('order.export.error')}: ${errorMessage}`);
  } finally {
    loading.value = false;
  }
};

// 处理批量操作
const handleBatchOperation = (command) => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning(t('order.batch.noSelection'))
    return
  }
  if (command === 'status') {
    console.log('批量更新状态 for IDs:', selectedOrders.value.map(o => o.id));
    currentOrder.value = {};
    isBatchModeActive.value = true;
    statusVisible.value = true;
  } else {
    console.log('批量操作:', command)
    // TODO: Implement other batch operations if any
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 处理查看详情
const handleView = (row) => {
  currentOrderId.value = row.id
  currentOrderNumber.value = row.orderId
  orderDetailDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  // TODO: 跳转到编辑页面
}

// 处理删除
const handleDelete = (row) => {
  // 以后端返回的 canDelete 作为唯一判断依据
  if (!row.canDelete) {
    ElMessage.warning(t('order.delete.cannotDeleteWithTransactions'));
    return;
  }

  ElMessageBox.confirm(
    t('order.delete.confirmMessage'),
    t('order.delete.title'),
    {
      confirmButtonText: '确定删除',
      cancelButtonText: t('common.cancel'),
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
      cancelButtonClass: 'el-button--default',
      showIcon: true,
      customClass: 'order-delete-confirm-dialog',
      roundButton: false,
      distinguishCancelAndClose: true,
      dangerouslyUseHTMLString: false
    }
  )
    .then(async () => {
      try {
        // 调用删除API
        const res = await deleteOrder(row.id);
        if (res.code === 1000) {
          ElMessage.success(t('order.delete.success'));
          fetchOrderList();
        } else {
          ElMessage.error(res.message || t('order.delete.error'));
        }
      } catch (error) {
        console.error('删除订单失败:', error);
        ElMessage.error(t('order.delete.error'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
}

// 处理状态更新成功
const handleStatusSuccess = () => {
  // ElMessage.success('状态更新成功')
  fetchOrderList()
}

// 处理付款信息更新成功
const handlePaymentSuccess = () => {
  fetchOrderList()
}

// 处理付款
const handleOrderPayment = (data) => {
  currentOrderId.value = data.orderId;
  paymentVisible.value = true;
}

// 处理附件
const handleOrderAttachment = (data) => {
  currentOrderId.value = data.orderId;
  attachmentVisible.value = true;
}

// 处理状态更新
const handleOrderStatusUpdate = (data) => {
  currentOrder.value = {
    dbId: data.id,
    orderId: data.orderId,
    status: data.status || 'NORMAL',
    financeStatus: data.financeStatus || 'NORMAL',
    badDebt: data.badDebtFlag
  };
  isBatchModeActive.value = false;
  statusVisible.value = true;
}

// 加载字段选项的函数
const loadFieldOptions = async () => {
  console.log('开始加载字段选项...')
  
  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'brand', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      brandOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch brand options:", error);
  }

  try {
    const response = await getDictFields({ module: 'ORDER', fieldCode: 'financial_status', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      financialStatusOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch financial status options:", error);
  }
}

// 页面加载时获取数据
onMounted(async () => {
  console.log('订单列表页面加载完成，开始获取数据...')
  await loadFieldOptions()
  fetchOrderList()
})

onActivated(async () => {
  console.log('订单列表页面激活，重新加载字段选项...')
  await loadFieldOptions()
  resetFilters()
})

// 监视筛选条件变化，自动触发搜索
watch(
  () => [filters.value.orderType, filters.value.orderStatus, filters.value.financialStatus, filters.value.badDebt],
  () => {
    console.log('筛选条件下拉框变化，自动触发搜索')
    handleFilterChange()
  }
)

// 处理首付日期范围变化
const handleFirstPaymentDateRangeChange = (val) => {
  if (val) {
    // 当选择了自定义日期范围时，清除预设的筛选条件
    handleFilterChange()
  }
}

// 处理到期日期范围变化
const handleDueDateRangeChange = (val) => {
  if (val) {
    // 当选择了自定义日期范围时，清除预设的筛选条件
    handleFilterChange()
  }
}
</script>

<style lang="scss" scoped>
.order-list-container {
  // Added styles for non-scrollable page
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;

  padding: 20px;
  background-color: #f5f7fa;

  .breadcrumb-container {
    margin-bottom: 16px;
    padding: 8px 0;
  }

  // 新的筛选区域样式
  .new-filter-container {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .search-row {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      align-items: center;
      
      .search-input {
        flex: 1;
        max-width: 400px;
      }
      
      .search-actions {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-shrink: 0;
      }
    }
    
    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 16px;
      align-items: center;
      transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
      max-height: 500px; /* 展开时足够的高度 */
      overflow: hidden;
      opacity: 1;
      
      &.collapsed-filters {
        max-height: 0;
        opacity: 0;
        margin-top: 0; /* 移除上边距，防止空隙 */
        padding-top: 0; /* 移除上内边距 */
        padding-bottom: 0; /* 移除下内边距 */
      }

      .el-select {
        width: 100%;
      }
      
      .date-filter-wrapper {
        min-width: 280px;
        
        .el-date-editor {
          width: 100%;
        }
      }
      
      // 在大屏幕上使用固定列数
      @media (min-width: 1400px) {
        grid-template-columns: repeat(6, 1fr);
      }
      
      @media (min-width: 1200px) and (max-width: 1399px) {
        grid-template-columns: repeat(4, 1fr);
        
        .date-filter-wrapper {
          grid-column: span 2;
        }
      }
      
      @media (min-width: 992px) and (max-width: 1199px) {
        grid-template-columns: repeat(3, 1fr);
        
        .date-filter-wrapper {
          grid-column: span 1;
          min-width: 240px;
        }
      }
      
      @media (max-width: 991px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        
        .date-filter-wrapper {
          grid-column: span 2;
          min-width: 200px;
        }
      }
      
      @media (max-width: 576px) {
        grid-template-columns: 1fr;
        
        .date-filter-wrapper {
          grid-column: span 1;
        }
      }

      .time-query-filter-unit {
        display: flex;
        gap: 12px; /* 保持组件间距 */
        align-items: center;
        width: 100%;

        .query-time-select {
          flex: 4; /* 弹性填充，最宽 */
          min-width: 180px; /* 进一步增大最小宽度以显示完整文字 */
        }
        .query-time-operator-select {
          flex: none; /* 固定宽度，不弹性伸缩 */
          width: 160px; /* 进一步增大宽度以显示完整文字 */
        }
        .query-time-value-input {
          flex: 2; /* 弹性填充，中等宽度 */
          width: 100%;
          min-width: 170px; /* 确保最小宽度，防止文字溢出 */
          
          .el-input__wrapper {
            width: 100%;
          }
        }
      }
    }
  }

  .content-card {
    border-radius: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 确保内容不溢出
    
    :deep(.el-card__body) {
      padding: 20px;
      flex: 1;
      overflow: hidden; // 确保内容不溢出
      display: flex;
      flex-direction: column;
    }
  }

  .operation-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-shrink: 0; // 防止操作区域被压缩
    
    .left-operations {
      display: flex;
      gap: 8px;
    }
    
    .right-operations {
      display: flex;
      gap: 12px;
    }

    .operation-btn {
      .el-icon {
        margin-right: 4px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: auto; // 允许表格区域滚动

    // 隐藏Webkit浏览器的滚动条
    &::-webkit-scrollbar {
      display: none; // 或 width: 0; height: 0;
    }
    // 隐藏Firefox的滚动条
    scrollbar-width: none;
    // 隐藏IE/Edge的滚动条
    -ms-overflow-style: none;
  }

  :deep(.el-button) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    border-radius: 6px;
    font-weight: 500;
    
    .el-icon {
      margin-right: 0;
    }
  }

  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }

  :deep(.el-select) {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }
}

// 日期选择器样式调整
:deep(.el-date-editor--daterange) {
  --el-date-editor-width: 100%;
  width: 100%;
}

:deep(.el-picker-panel__shortcut) {
  padding: 0 10px;
  line-height: 26px;
  font-size: 13px;
}
</style> 
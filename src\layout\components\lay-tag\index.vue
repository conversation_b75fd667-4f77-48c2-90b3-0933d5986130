<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMultiTagsStore } from '@/stores/modules/multiTags';

const route = useRoute();
const router = useRouter();
const tagsStore = useMultiTagsStore();

// 标签列表
const tagList = computed(() => tagsStore.tagList);
// 当前活动标签
const activeTag = computed(() => route.fullPath);
// 滚动容器
const scrollContainer = ref(null);

// 标签可滚动区域
const scrollable = ref(false);
// 滚动到的位置
const scrollLeft = ref(0);

// 监听路由变化更新标签
watch(
  () => route.fullPath,
  () => {
    addVisitedTag();
    moveToCurrentTag();
  },
  {
    immediate: true
  }
);

// 添加访问过的标签
function addVisitedTag() {
  const { name, fullPath, meta } = route;
  if (!name) return;
  
  tagsStore.addTag({
    name,
    path: fullPath,
    meta
  });
}

// 移动到当前标签位置
async function moveToCurrentTag() {
  await nextTick();
  
  if (!scrollContainer.value) return;
  
  // 获取当前标签和容器
  const tags = document.querySelectorAll('.tags-item');
  const container = scrollContainer.value;
  
  // 找到当前标签
  let currentTag = null;
  for (const tag of tags) {
    if (tag.dataset.path === activeTag.value) {
      currentTag = tag;
      break;
    }
  }
  
  if (!currentTag) return;
  
  // 计算标签位置
  const tagOffsetLeft = currentTag.offsetLeft;
  const tagWidth = currentTag.offsetWidth;
  
  // 容器宽度
  const containerWidth = container.offsetWidth;
  // 所有标签总宽度
  const scrollWidth = container.scrollWidth;
  
  if (tagOffsetLeft < scrollLeft.value) {
    // 标签在可视区域左侧
    scrollLeft.value = tagOffsetLeft;
  } else if (tagOffsetLeft + tagWidth > scrollLeft.value + containerWidth) {
    // 标签在可视区域右侧
    scrollLeft.value = tagOffsetLeft + tagWidth - containerWidth;
  }
  
  // 判断是否可滚动
  scrollable.value = scrollWidth > containerWidth;
}

// 关闭标签
function closeTag(tag) {
  tagsStore.delTag(tag);
  
  // 如果关闭的是当前标签，则跳转到最后一个标签
  if (tag.path === activeTag.value) {
    const latestTag = tagList.value[tagList.value.length - 1];
    router.push(latestTag.path);
  }
}

// 处理标签点击
function handleTagClick(tag) {
  if (tag.path === activeTag.value) return;
  router.push(tag.path);
}
</script>

<template>
  <div class="tags-view-container">
    <div
      ref="scrollContainer"
      class="tags-view-wrapper"
      @wheel.prevent="handleScroll"
    >
      <div
        class="tags-view-item"
        :style="{ transform: `translateX(-${scrollLeft}px)` }"
      >
        <span
          v-for="(tag, index) in tagList"
          :key="index"
          :data-path="tag.path"
          :class="['tags-item', { active: tag.path === activeTag }]"
          @click="handleTagClick(tag)"
        >
          {{ tag.meta.title }}
          <el-icon 
            v-if="index !== 0"
            class="close-icon" 
            @click.stop="closeTag(tag)"
          >
            <Close />
          </el-icon>
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  
  .tags-view-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    white-space: nowrap;
  }
  
  .tags-view-item {
    position: absolute;
    display: flex;
    transition: transform 0.3s ease;
  }
  
  .tags-item {
    display: inline-flex;
    align-items: center;
    margin: 2px 4px 2px 0;
    padding: 0 8px;
    height: 26px;
    font-size: 12px;
    background: #fff;
    color: #495060;
    border: 1px solid #d8dce5;
    border-radius: 2px;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    &:first-of-type {
      margin-left: 5px;
    }
    
    &.active {
      background-color: var(--el-color-primary);
      color: #fff;
      border-color: var(--el-color-primary);
      
      .close-icon {
        color: #fff;
      }
    }
    
    .close-icon {
      margin-left: 6px;
      width: 16px;
      height: 16px;
      vertical-align: -0.3em;
      color: #495060;
      
      &:hover {
        color: #ff4d4f;
      }
    }
  }
}
</style>
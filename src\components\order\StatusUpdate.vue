<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="visible = $event"
    title="更新订单状态"
    width="500px"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="订单状态" prop="status">
        <el-select 
          :model-value="form.status" 
          @update:model-value="form.status = $event" 
          placeholder="请选择订单状态"
        >
          <el-option label="正常" value="normal" />
          <el-option label="逾期" value="overdue" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input
          :model-value="form.remarks"
          @update:model-value="form.remarks = $event"
          type="textarea"
          :rows="3"
          placeholder="请输入状态变更原因"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 弹窗显示状态
const visible = ref(false)

// 表单ref
const formRef = ref(null)

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  status: '',
  remarks: ''
})

// 表单校验规则
const rules = {
  status: [
    { required: true, message: '请选择订单状态', trigger: 'change' }
  ],
  remarks: [
    { required: true, message: '请输入状态变更原因', trigger: 'blur' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 重置表单
    form.value = {
      status: '',
      remarks: ''
    }
  }
})

// 监听内部显示状态
watch(() => visible.value, (val) => {
  emit('update:modelValue', val)
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // TODO: 调用API更新订单状态
    // const res = await updateOrderStatus(props.orderId, form.value)
    
    ElMessage.success('状态更新成功')
    visible.value = false
    emit('success')
  } catch (error) {
    console.error('更新订单状态失败:', error)
    ElMessage.error('更新订单状态失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style> 
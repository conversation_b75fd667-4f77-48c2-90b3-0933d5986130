 /**
 * Format a date using the Intl.DateTimeFormat API
 * @param {string|Date} date - The date to format
 * @param {string} format - Format type: 'date', 'time', 'datetime', or 'relative'
 * @param {string} locale - The locale to use for formatting (defaults to browser locale)
 * @returns {string} Formatted date string
 */
export function formatDate(date, format = 'date', locale = navigator.language) {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Handle invalid dates
    if (isNaN(dateObj.getTime())) return 'Invalid date';
    
    // Define format options
    const options = {
      date: { year: 'numeric', month: 'short', day: 'numeric' },
      time: { hour: 'numeric', minute: 'numeric' },
      datetime: { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric' },
    };
    
    // Handle relative time format
    if (format === 'relative') {
      const now = new Date();
      const diffInSeconds = Math.floor((now - dateObj) / 1000);
      
      // Less than a minute
      if (diffInSeconds < 60) {
        return 'Just now';
      }
      
      // Less than an hour
      if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
      }
      
      // Less than a day
      if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
      }
      
      // Less than a week
      if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
      }
      
      // Default to normal date format for older dates
      return new Intl.DateTimeFormat(locale, options.date).format(dateObj);
    }
    
    // Use Intl.DateTimeFormat for standard formats
    return new Intl.DateTimeFormat(locale, options[format] || options.date).format(dateObj);
  }
  
  /**
   * Format a number as currency
   * @param {number} amount - The amount to format
   * @param {string} currency - Currency code (e.g., 'USD', 'EUR')
   * @param {string} locale - The locale to use (defaults to browser locale)
   * @returns {string} Formatted currency string
   */
  export function formatCurrency(amount, currency = 'USD', locale = navigator.language) {
    if (amount === null || amount === undefined) return '';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }
  
  /**
   * Generate a random string (useful for IDs)
   * @param {number} length - Length of the random string
   * @returns {string} Random string
   */
  export function generateRandomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  /**
   * Debounce a function to limit how often it can be called
   * @param {Function} func - The function to debounce
   * @param {number} wait - Time in milliseconds to wait
   * @returns {Function} Debounced function
   */
  export function debounce(func, wait = 300) {
    let timeout;
    
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  /**
   * Truncate a string to a specified length and add ellipsis
   * @param {string} str - The string to truncate
   * @param {number} length - Maximum length before truncation
   * @returns {string} Truncated string
   */
  export function truncateString(str, length = 50) {
    if (!str || str.length <= length) return str;
    return str.slice(0, length) + '...';
  }
  
  /**
   * Deep clone an object or array
   * @param {any} obj - The object to clone
   * @returns {any} A deep clone of the object
   */
  export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    return JSON.parse(JSON.stringify(obj));
  }
  
  /**
   * Filter an array of objects by a search term across all properties
   * @param {Array} array - Array of objects to search
   * @param {string} searchTerm - Term to search for
   * @returns {Array} Filtered array of objects
   */
  export function searchObjects(array, searchTerm) {
    if (!searchTerm || searchTerm === '') return array;
    
    const term = searchTerm.toLowerCase();
    
    return array.filter(item => {
      return Object.values(item).some(value => {
        if (value === null || value === undefined) return false;
        return value.toString().toLowerCase().includes(term);
      });
    });
  }
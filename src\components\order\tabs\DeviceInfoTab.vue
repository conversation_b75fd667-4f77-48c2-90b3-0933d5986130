<template>
  <div class="device-info-tab">
    <!-- Device Information Card -->
    <el-card shadow="never" class="info-card device-details-card">
      <template #header>
        <div class="card-header">
          <span>{{ $t('device.deviceInfo') }}</span>
        </div>
      </template>

      <div v-if="props.isLoading" class="loading-container">
        <el-progress 
          type="circle" 
          :width="24" 
          :stroke-width="3" 
          :percentage="100" 
          status="success" 
          :indeterminate="true" 
          :duration="1"
          class="custom-loader"
        />
        <span style="margin-left: 8px;">{{ $t('common.loading') }}</span>
      </div>

      <div v-else-if="props.loadingError" class="error-container">
        <span>{{ $t('common.fetchFailed') }}</span>
        <el-button type="danger" :icon="Refresh" @click="handleRetryFetch" plain size="small" class="retry-button">
          {{ $t('common.retry') }}
        </el-button>
      </div>
      
      <el-descriptions v-else-if="deviceDetails && Object.keys(deviceDetails).length > 0 && !isEmptyDevice(deviceDetails)" :column="2" border class="device-descriptions">
        <el-descriptions-item>
          <template #label>{{ $t('device.brand.label') }}</template>
          {{ brandLabel }}
        </el-descriptions-item>
        
        <el-descriptions-item>
          <template #label>{{ $t('device.color.label') }}</template>
          <span class="color-block-container">
            {{ colorLabel }}
          </span>
        </el-descriptions-item>

        <el-descriptions-item :label="$t('device.details.title')" >
            {{ deviceFullDetailsComputed }}
        </el-descriptions-item>

        <el-descriptions-item :label="$t('device.sn.label')" >
            {{ deviceDetails.serialNumber }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <span>{{ $t('device.imei1') }}</span>
            <el-popover
              placement="top-start"
              :title="$t('order.deviceInfoTab.imeiExplanationTitle')"
              :width="200"
              trigger="click"
              :content="$t('order.deviceInfoTab.imeiExplanationText')"
            >
              <template #reference>
                <el-icon class="info-icon"><QuestionFilled /></el-icon>
              </template>
            </el-popover>
          </template>
          {{ deviceDetails.imei1 || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item>
          <template #label>
            <span>{{ $t('device.imei2') }}</span>
             <el-popover
              placement="top-start"
              :title="$t('order.deviceInfoTab.imeiExplanationTitle')"
              :width="200"
              trigger="click"
              :content="$t('order.deviceInfoTab.imeiExplanationText')"
            >
              <template #reference>
                <el-icon class="info-icon"><QuestionFilled /></el-icon>
              </template>
            </el-popover>
          </template>
          {{ deviceDetails.imei2 || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-else-if="!props.isLoading && !props.loadingError" class="empty-details">
        <p>{{ $t('device.pleaseSelect') }}</p>
      </div>
      <el-divider v-if="!props.isLoading && !props.loadingError && deviceDetails && Object.keys(deviceDetails).length > 0" class="details-divider" />
    </el-card>

    <!-- Attachments Card -->
    <el-card shadow="never" class="info-card attachments-card">
      <template #header>
        <div class="card-header">
          <span>{{ $t('device.attachments') }}</span>
        </div>
      </template>
      <div v-if="processedAttachments && processedAttachments.length > 0" class="attachment-list">
        <div v-for="attachment in processedAttachments" :key="attachment.id" class="attachment-item">
          <div class="attachment-main-content">
            <div class="attachment-thumbnail">
              <template v-if="isImageFile(attachment.fileName)">
                <el-image
                  style="width: 50px; height: 50px; border-radius: 4px"
                  :src="attachment.fileUrl"
                  :preview-src-list="[attachment.fileUrl]"
                  fit="cover"
                  lazy
                >
                  <template #placeholder>
                    <div class="image-slot"><el-icon><Picture /></el-icon></div>
                  </template>
                  <template #error>
                    <div class="image-slot"><el-icon><Picture /></el-icon></div>
                  </template>
                </el-image>
              </template>
              <template v-else-if="isPdfFile(attachment.fileName)">
                <el-image
                  v-if="attachment.thumbnail"
                  style="width: 50px; height: 50px; border-radius: 4px"
                  :src="attachment.thumbnail"
                  fit="cover"
                  lazy
                  @click="handlePreview(attachment)"
                >
                  <template #error>
                    <div class="thumbnail-placeholder">
                      <el-icon size="24"><Document /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div v-else class="thumbnail-placeholder" @click="handlePreview(attachment)">
                  <el-icon size="24"><Document /></el-icon>
                </div>
              </template>
              <template v-else>
                <div class="thumbnail-placeholder" @click="handlePreview(attachment)">
                  <el-icon size="24"><Document /></el-icon>
                </div>
              </template>
            </div>
            <div class="attachment-details">
              <a class="el-upload-list__item-name" @click="handlePreview(attachment)">
                <span>{{ attachment.fileName }}</span>
              </a>
              <div class="attachment-meta">
                <span v-if="attachment.formattedSize">{{ attachment.formattedSize }}</span>
                <span
                  v-if="attachment.formattedSize && attachment.formattedUploadTime"
                  class="meta-divider"
                  >|</span
                >
                <span v-if="attachment.formattedUploadTime">{{ attachment.formattedUploadTime }}</span>
              </div>
            </div>
          </div>
          <div class="attachment-actions">
            <el-button type="primary" link size="small" @click="handlePreview(attachment)">{{
              $t('order.attachment.preview')
            }}</el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="handleDownload(attachment)"
              :loading="attachment.isDownloading"
              :disabled="attachment.isDownloading"
            >
              {{ attachment.isDownloading ? $t('common.downloading') : $t('common.download') }}
            </el-button>
          </div>
        </div>
      </div>

      <div v-else class="empty-attachments">
        {{ $t('order.noAttachments') }}
      </div>
    </el-card>

    <!-- 图片预览弹窗 -->
    <el-dialog v-model="previewVisible" :title="currentAttachment?.fileName || ''" class="preview-dialog" width="70%">
      <div class="image-preview-container">
        <img v-if="currentAttachment" :src="currentAttachment.url" alt="Preview" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { QuestionFilled, Refresh, Picture, Document } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { updateDevice } from '@/api/device';
import { downloadOssFile } from '@/api/upload';
import * as pdfjsLib from 'pdfjs-dist/build/pdf.mjs';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.mjs?url';
import axios from 'axios';
import { getDictFields } from '@/api/dictionary';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const { t } = useI18n();

const defaultEmptyDevice = () => ({
  brand: '-',
  color: '-',
  model: '-',
  modelNumber: '-',
  serialNumber: '-',
  imei1: '-',
  imei2: '-',
  productionDate: '-',
  storageDate: '-'
});

const props = defineProps({
  orderId: {
    type: String,
    required: true
  },
  deviceInfo: {
    type: Object,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry-fetch']);

const deviceDetails = ref(null);
const processedAttachments = ref([]);
const colorOptions = ref([]);
const brandOptions = ref([]);

// 附件预览控制
const previewVisible = ref(false);
const currentAttachment = ref(null);

watch(
  () => props.isLoading,
  (newVal) => {
    if (newVal) {
      deviceDetails.value = null;
    }
  },
  { immediate: true }
);

watch(
  () => props.loadingError,
  (newVal) => {
    if (newVal) {
      deviceDetails.value = null;
    }
  },
  { immediate: true }
);

watch(
  () => props.deviceInfo,
  (newVal) => {
    if (props.isLoading || props.loadingError) {
      processedAttachments.value = [];
      return;
    }
    if (newVal && Object.keys(newVal).length > 0) {
      deviceDetails.value = { ...newVal };
      processAllAttachments(newVal.attachments || []);
    } else {
      deviceDetails.value = defaultEmptyDevice();
      processedAttachments.value = [];
    }
  },
  { immediate: true, deep: true }
);

const colorLabel = computed(() => {
  if (deviceDetails.value && deviceDetails.value.color) {
    const option = colorOptions.value.find(opt => opt.value === deviceDetails.value.color);
    return option ? option.label : deviceDetails.value.color;
  }
  return '-';
});

const brandLabel = computed(() => {
  if (deviceDetails.value && deviceDetails.value.brand) {
    const option = brandOptions.value.find(opt => opt.value === deviceDetails.value.brand);
    return option ? option.label : deviceDetails.value.brand;
  }
  return '-';
});

const isEmptyDevice = (details) => {
  if (!details) return true;
  return Object.values(details).every((value) => value === '-' || value === '' || value === null);
};

const deviceFullDetailsComputed = computed(() => {
  if (!deviceDetails.value || isEmptyDevice(deviceDetails.value)) return '-';
  if (props.isLoading || props.loadingError) return ''; 
  const model = deviceDetails.value.model || '';
  const modelNumber = deviceDetails.value.modelNumber; // Keep original value
  
  let modelNumberPart = '';
  // Only add parentheses if modelNumber exists and is not an empty string or '-' or null/undefined
  if (modelNumber && modelNumber !== '-' && String(modelNumber).trim() !== '') {
    modelNumberPart = `(${modelNumber})`;
  }
  
  // If all parts are empty, return '-'
  if (!brandLabel.value && !model && !modelNumberPart) {
    return '-';
  }
  
  // Construct the string, filter out empty parts before joining
  const parts = [brandLabel.value, model, modelNumberPart].filter((part) => part !== '' && part !== '-');
  return parts.join(' ').trim();
});

function getProxiedUrl(url) {
  if (!url) return '';
  const targetDomain = 'https://mdm-hk-1254102626.cos.ap-shanghai.myqcloud.com';
  if (url.startsWith(targetDomain)) {
    // In development, proxy through Vite. In production, this might need a different strategy (e.g., Nginx proxy)
    if (import.meta.env.DEV) {
      return url.replace(targetDomain, '/cos-proxy');
    }
  }
  return url;
}

function handleRetryFetch() {
  emit('retry-fetch');
}

async function processAllAttachments(attachments) {
  if (!attachments || attachments.length === 0) {
    processedAttachments.value = [];
    return;
  }

  const attachmentsData = attachments.map((att) => ({
    ...att,
    id: att.id || `temp_${Date.now()}_${Math.random()}`,
    // 使用后端返回的信息 - 字段名是 thumbnailUrl
    thumbnail: att.thumbnailUrl || null,
    fileSize: att.fileSize || 0,
    formattedSize: att.fileSize ? formatFileSize(att.fileSize) : '',
    uploadTime: att.uploadTime,
    formattedUploadTime: formatUploadTime(att.uploadTime),
    isDownloading: false,
    isProcessing: false // 后端已经提供了所有信息，不需要处理
  }));

  // 直接使用后端返回的数据，不需要异步处理
  processedAttachments.value = attachmentsData;
}

// fetchFileSize函数已移除，现在使用后端返回的文件大小信息

onMounted(async () => {
  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'color', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      colorOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch color options:", error);
  }

  try {
    const response = await getDictFields({ module: 'DEVICE', fieldCode: 'brand', enabled: true });
    if (response.data && response.data.records && response.data.records.length > 0) {
      brandOptions.value = response.data.records[0].optionList || [];
    }
  } catch (error) {
    console.error("Failed to fetch brand options:", error);
  }
});

// generatePdfThumbnail函数已移除，现在使用后端返回的缩略图

function formatUploadTime(dateString) {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return ''; // Invalid date
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return '';
  }
}

const colorMap = {
  '黑色': '#000000',
  'Black': '#000000',
  '白色': '#FFFFFF',
  'White': '#FFFFFF',
  '银色': '#C0C0C0',
  'Silver': '#C0C0C0',
  '金色': '#FFD700',
  'Gold': '#FFD700',
  '蓝色': '#0000FF',
  'Blue': '#0000FF',
  '红色': '#FF0000',
  'Red': '#FF0000',
  '绿色': '#008000',
  'Green': '#008000',
  '紫色': '#800080',
  'Purple': '#800080',
  '灰色': '#808080',
  'Gray': '#808080',
  '深空灰': '#5F6368',
  'Space Gray': '#5F6368',
  '棕色': '#A52A2A',
  'Brown': '#A52A2A'
};

function getColorHex(colorName) {
  return colorMap[colorName] || 'transparent';
}

// 判断是否为图片文件
function isImageFile(fileName) {
  if (!fileName) return false;
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = fileName.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
}

// 判断是否为PDF文件
function isPdfFile(fileName) {
  if (!fileName) return false;
  const pdfExtensions = ['pdf'];
  const extension = fileName.split('.').pop().toLowerCase();
  return pdfExtensions.includes(extension);
}

// 格式化文件大小
function formatFileSize(size) {
  if (size === 0) return '0 B';
  if (!size || isNaN(size)) return '';

  const units = ['B', 'KB', 'MB', 'GB'];
  let i = 0;
  let formattedSize = size;

  while (formattedSize >= 1024 && i < units.length - 1) {
    formattedSize /= 1024;
    i++;
  }

  return `${formattedSize.toFixed(2)} ${units[i]}`;
}

// 预览附件
function handlePreview(attachment) {
  if (!attachment || !attachment.fileUrl) {
    ElMessage.warning(t('order.attachment.invalidLink'));
    return;
  }

  if (isImageFile(attachment.fileName)) {
    currentAttachment.value = {
      fileName: attachment.fileName,
      url: attachment.fileUrl
    };
    previewVisible.value = true;
  } else if (isPdfFile(attachment.fileName)) {
    window.open(attachment.fileUrl, '_blank');
  } else {
    ElMessage.info(t('order.attachment.noPreviewAvailable'));
  }
}

// 下载附件
async function handleDownload(attachment) {
  const attachmentToDownload = processedAttachments.value.find((a) => a.id === attachment.id);
  if (!attachmentToDownload || !attachmentToDownload.fileUrl) {
    ElMessage.warning(t('order.attachment.downloadUrlMissing'));
    return;
  }

  attachmentToDownload.isDownloading = true;

  try {
    const { blob, filename } = await downloadOssFile(attachmentToDownload.fileUrl);

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    ElMessage.success(t('common.download') + ' ' + t('common.success'));
  } catch (error) {
    console.error('文件下载失败:', error);
    ElMessage.error(
      t('common.download') + ' ' + t('common.error') + ': ' + (error.message || t('common.unknown'))
    );
  } finally {
    if (attachmentToDownload) {
      attachmentToDownload.isDownloading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.device-info-tab {
  width: 100%;
  padding: 24px;
  background-color: #f9fafc;
  
  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .error-container {
    flex-direction: column;
    
    .retry-button {
      margin-top: 16px;
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    background-color: #fff;
    
    :deep(.el-card__header) {
      background-color: #f7f9fc;
      padding: 14px 16px;
      border-bottom: 1px solid #ebeef5;
      
      .card-header {
        font-size: 15px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    :deep(.el-card__body) {
      padding: 16px;
    }
    
    &.device-details-card {
      .empty-details {
        padding: 20px;
        text-align: center;
        color: #909399;
        font-size: 13px;
      }
      
      .details-divider {
        margin: 0 0 16px;
      }
      
      :deep(.el-descriptions__table) {
        table-layout: fixed;
        width: 100%;
      }

      .color-block-container {
        display: flex;
        align-items: center;
        
        .color-block {
          display: inline-block;
          width: 14px;
          height: 14px;
          border-radius: 3px;
          margin-right: 6px;
          border: 1px solid rgba(0, 0, 0, 0.1);
        }
      }
      
      .info-icon {
        margin-left: 4px;
        color: #c0c4cc;
        cursor: pointer;
        font-size: 14px;
      }
    }
  }
}

.attachments-container {
  padding: 16px;
}

.attachment-list {
  padding: 8px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  .attachment-main-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-grow: 1;
  }

  .attachment-thumbnail {
    flex-shrink: 0;
  }

  .attachment-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .el-upload-list__item-name {
    display: flex;
    align-items: center;
    color: #606266;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      color: var(--el-color-primary);
    }
  }

  .attachment-meta {
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    gap: 5px;

    .meta-divider {
      color: #e0e0e0;
    }
  }

  .attachment-actions {
    flex-shrink: 0;
    display: flex;
    gap: 10px;
  }
}

.thumbnail-placeholder {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #c0c4cc;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: var(--el-text-color-secondary);
  font-size: 24px;
}

.empty-attachments {
  color: #909399;
  text-align: center;
  padding: 20px;
  border: 1px dashed #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
}

:deep(.preview-dialog) {
  .el-dialog__header {
    background-color: #f7f9fc;
    margin-right: 0;
    padding: 14px 16px;
  }
  
  .el-dialog__body {
    padding: 0;
  }
  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 80vh;
    overflow: auto;
    .preview-image {
      max-width: 100%;
      max-height: 100%;
      display: block;
    }
  }
}
</style> 
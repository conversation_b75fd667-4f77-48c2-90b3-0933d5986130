<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import screenfull from 'screenfull';

const { t } = useI18n();
const isFullscreen = ref(false);

// 切换全屏
function toggleFullScreen() {
  if (screenfull.isEnabled) {
    screenfull.toggle();
    isFullscreen.value = !isFullscreen.value;
  }
}

// 监听全屏变化
if (screenfull.isEnabled) {
  screenfull.on('change', () => {
    isFullscreen.value = screenfull.isFullscreen;
  });
}
</script>

<template>
  <div 
    class="full-screen navbar-bg-hover" 
    :title="isFullscreen ? t('buttons.cancelFullScreen') : t('buttons.fullScreen')"
    @click="toggleFullScreen"
  >
    <i :class="[isFullscreen ? 'el-icon-close' : 'el-icon-full-screen']"></i>
  </div>
</template>

<style lang="scss" scoped>
.full-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 48px;
  cursor: pointer;
  
  i {
    font-size: 16px;
  }
}

.navbar-bg-hover {
  &:hover {
    background-color: var(--el-fill-color-light);
  }
}
</style>
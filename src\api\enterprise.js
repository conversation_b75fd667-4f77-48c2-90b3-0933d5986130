import request from '@/utils/request'

/**
 * 获取当前企业信息
 * @returns {Promise<object>} 企业信息对象
 */
export function getEnterpriseInfo() {
  return request({
    url: '/crm-service/api/enterprise-info',
    method: 'get'
  })
}

/**
 * 更新企业基本信息
 * @param {object} data - 企业信息对象
 * @returns {Promise<object>} 更新结果
 */
export function updateEnterpriseInfo(data) {
  return request({
    url: '/crm-service/api/enterprise-info',
    method: 'put',
    data
  })
}

/**
 * 上传企业Logo
 * @param {File} file - Logo文件
 * @returns {Promise<string>} 文件URL
 */
export function uploadLogo(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/crm-service/api/enterprise-info/logo',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

/**
 * 获取企业社交媒体列表
 * @param {number} enterpriseId - 企业ID
 * @returns {Promise<Array>} 社交媒体列表
 */
export function getEnterpriseSocialMediaList(enterpriseId) {
  return request({
    url: `/crm-service/api/enterprise-social-media/list/${enterpriseId}`,
    method: 'get'
  })
}

/**
 * 保存企业社交媒体
 * @param {object} data - 社交媒体信息
 * @returns {Promise<object>} 保存结果
 */
export function saveEnterpriseSocialMedia(data) {
  return request({
    url: '/crm-service/api/enterprise-social-media',
    method: 'post',
    data
  })
}

/**
 * 更新企业社交媒体
 * @param {object} data - 社交媒体信息
 * @returns {Promise<object>} 更新结果
 */
export function updateEnterpriseSocialMedia(data) {
  return request({
    url: '/crm-service/api/enterprise-social-media',
    method: 'put',
    data
  })
}

/**
 * 删除企业社交媒体
 * @param {number} id - 社交媒体ID
 * @returns {Promise<object>} 删除结果
 */
export function deleteEnterpriseSocialMedia(id) {
  return request({
    url: `/crm-service/api/enterprise-social-media/${id}`,
    method: 'delete'
  })
}

/**
 * 批量更新社交媒体账号
 * @param {Array} data - 社交媒体账号列表
 * @returns {Promise<object>} 更新结果
 */
export function batchUpdateSocialMedia(data) {
  return request({
    url: '/crm-service/api/enterprise-info/social-media/batch',
    method: 'post',
    data
  })
}

/**
 * 删除企业Logo
 * @returns {Promise<object>} 删除结果
 */
export function deleteLogo() {
  return request({
    url: '/crm-service/api/enterprise-info/logo',
    method: 'delete'
  })
} 
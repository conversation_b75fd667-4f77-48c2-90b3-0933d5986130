/**
 * @typedef {Object} CustomerDTO
 * @property {number} id - 客户ID
 * @property {number} enterpriseId - 企业ID
 * @property {string} firstName - 名字
 * @property {string} lastName - 姓氏
 * @property {string} email - 邮箱
 * @property {string} phone1 - 电话1
 * @property {string} phone2 - 电话2
 * @property {string} idType - 证件类型 ID_CARD-身份证, PASSPORT-护照, DRIVER_LICENSE-驾照
 * @property {string} idNumber - 证件号码
 * @property {string} licenseAddress - 证件地址
 * @property {string} contactAddress - 联系地址
 * @property {Array<{fileName: string, fileUrl: string}>} attachments - 附件列表
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * @typedef {Object} CustomerQuery
 * @property {number} current - 当前页码
 * @property {number} size - 每页数量
 * @property {string} [name] - 客户姓名
 * @property {string} [phone] - 电话
 * @property {string} [email] - 邮箱
 * @property {string} [idNumber] - 证件号码
 */

/**
 * @typedef {Object} CustomerCreateDTO
 * @property {string} firstName - 名字
 * @property {string} lastName - 姓氏
 * @property {string} email - 邮箱
 * @property {string} phone1 - 电话1
 * @property {string} [phone2] - 电话2
 * @property {string} idType - 证件类型
 * @property {string} idNumber - 证件号码
 * @property {string} licenseAddress - 证件地址
 * @property {string} contactAddress - 联系地址
 * @property {Array<{fileName: string, fileUrl: string}>} [attachments] - 附件列表
 */

/**
 * @typedef {Object} CustomerUpdateDTO
 * @property {number} id - 客户ID
 * @property {string} firstName - 名字
 * @property {string} lastName - 姓氏
 * @property {string} email - 邮箱
 * @property {string} phone1 - 电话1
 * @property {string} [phone2] - 电话2
 * @property {string} idType - 证件类型
 * @property {string} idNumber - 证件号码
 * @property {string} licenseAddress - 证件地址
 * @property {string} contactAddress - 联系地址
 * @property {Array<{fileName: string, fileUrl: string}>} [attachments] - 附件列表
 */

export const ID_TYPES = {
  ID_CARD: '身份证',
  PASSPORT: '护照',
  DRIVER_LICENSE: '驾照'
}

export const ID_TYPE_OPTIONS = [
  { label: '身份证', value: 'ID_CARD' },
  { label: '护照', value: 'PASSPORT' },
  { label: '驾照', value: 'DRIVER_LICENSE' }
] 
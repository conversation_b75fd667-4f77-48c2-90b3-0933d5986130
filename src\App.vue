<script setup>
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import AppLayout from './components/AppLayout.vue';

// 导入全局配置的 dayjs
import dayjs from 'dayjs';

const { locale } = useI18n()
const elementLocale = ref(locale.value.includes('zh') ? zhCn : en)

// 监听语言变化,同步Element Plus的语言
watch(locale, (newLocale) => {
  elementLocale.value = newLocale.includes('zh') ? zhCn : en
  
  // 同时更新 dayjs 的语言，确保语言代码一致
  if (newLocale === 'zh-CN') {
    dayjs.locale('zh-cn')
  } else {
    dayjs.locale('en')
  }
})
</script>

<template>
  <el-config-provider :locale="elementLocale">
    <router-view :key="locale" />
  </el-config-provider>
</template>

<style>
html,
body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

#app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局移除按钮和下拉菜单的选中边框 */
.el-button:focus,
.el-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

.el-dropdown:focus,
.el-dropdown:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

.el-link:focus,
.el-link:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

/* 移除所有可聚焦元素的默认outline */
*:focus,
*:focus-visible {
  outline: none !important;
}

/* 保持可访问性，为键盘导航用户提供视觉反馈 */
.el-button:focus-visible {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}
</style>